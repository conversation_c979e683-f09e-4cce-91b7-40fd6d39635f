<?php

namespace App\Http\Controllers;

use App\Models\BusinessSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Artisan;

class AfricanPaymentGatewayController extends Controller
{
    /**
     * Show African payment gateway configuration page
     */
    public function configuration()
    {
        return view('backend.payment_settings.african_pg.configuration');
    }

    /**
     * Show African payment gateway credentials index page
     */
    public function credentials_index()
    {
        return view('backend.payment_settings.african_pg.credentials');
    }

    /**
     * Update African payment gateway configurations
     */
    public function updateConfiguration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'flutterwave_activation' => 'nullable|in:0,1',
            'mpesa_activation' => 'nullable|in:0,1',
            'payfast_activation' => 'nullable|in:0,1',
            'paydunia_activation' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Update activation settings
        $this->updateBusinessSetting('flutterwave_activation', $request->has('flutterwave_activation') ? 1 : 0);
        $this->updateBusinessSetting('mpesa_activation', $request->has('mpesa_activation') ? 1 : 0);
        $this->updateBusinessSetting('payfast_activation', $request->has('payfast_activation') ? 1 : 0);
        $this->updateBusinessSetting('paydunia_activation', $request->has('paydunia_activation') ? 1 : 0);

        // Clear cache to reflect changes
        try {
            Artisan::call('cache:clear');
        } catch (\Exception $e) {
            // Cache clearing failed, but continue
        }

        flash(translate('African payment gateway settings updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update Flutterwave credentials
     */
    public function updateFlutterwaveCredentials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'flutterwave_public_key' => 'required|string',
            'flutterwave_secret_key' => 'required|string',
            'flutterwave_encryption_key' => 'required|string',
            'flutterwave_sandbox' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $this->updateBusinessSetting('flutterwave_public_key', $request->flutterwave_public_key);
        $this->updateBusinessSetting('flutterwave_secret_key', $request->flutterwave_secret_key);
        $this->updateBusinessSetting('flutterwave_encryption_key', $request->flutterwave_encryption_key);
        $this->updateBusinessSetting('flutterwave_sandbox', $request->has('flutterwave_sandbox') ? 1 : 0);

        flash(translate('Flutterwave credentials updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update M-Pesa credentials
     */
    public function updateMpesaCredentials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mpesa_consumer_key' => 'required|string',
            'mpesa_consumer_secret' => 'required|string',
            'mpesa_shortcode' => 'required|string',
            'mpesa_passkey' => 'required|string',
            'mpesa_environment' => 'required|in:sandbox,live'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $this->updateBusinessSetting('mpesa_consumer_key', $request->mpesa_consumer_key);
        $this->updateBusinessSetting('mpesa_consumer_secret', $request->mpesa_consumer_secret);
        $this->updateBusinessSetting('mpesa_shortcode', $request->mpesa_shortcode);
        $this->updateBusinessSetting('mpesa_passkey', $request->mpesa_passkey);
        $this->updateBusinessSetting('mpesa_environment', $request->mpesa_environment);

        flash(translate('M-Pesa credentials updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update Payfast credentials
     */
    public function updatePayfastCredentials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payfast_merchant_id' => 'required|string',
            'payfast_merchant_key' => 'required|string',
            'payfast_passphrase' => 'nullable|string',
            'payfast_sandbox' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $this->updateBusinessSetting('payfast_merchant_id', $request->payfast_merchant_id);
        $this->updateBusinessSetting('payfast_merchant_key', $request->payfast_merchant_key);
        $this->updateBusinessSetting('payfast_passphrase', $request->payfast_passphrase);
        $this->updateBusinessSetting('payfast_sandbox', $request->has('payfast_sandbox') ? 1 : 0);

        flash(translate('Payfast credentials updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update PayDunia credentials
     */
    public function updatePayDuniaCredentials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'paydunia_username' => 'required|string',
            'paydunia_password' => 'required|string',
            'paydunia_token' => 'required|string',
            'paydunia_sandbox' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $this->updateBusinessSetting('paydunia_username', $request->paydunia_username);
        $this->updateBusinessSetting('paydunia_password', $request->paydunia_password);
        $this->updateBusinessSetting('paydunia_token', $request->paydunia_token);
        $this->updateBusinessSetting('paydunia_sandbox', $request->has('paydunia_sandbox') ? 1 : 0);

        flash(translate('PayDunia credentials updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Helper method to update business setting
     */
    private function updateBusinessSetting($type, $value)
    {
        $business_setting = BusinessSetting::where('type', $type)->first();
        
        if ($business_setting) {
            $business_setting->value = $value;
            $business_setting->save();
        } else {
            $business_setting = new BusinessSetting;
            $business_setting->type = $type;
            $business_setting->value = $value;
            $business_setting->save();
        }
    }

    /**
     * Get African payment gateway settings for API
     */
    public function getSettings()
    {
        $settings = [
            'flutterwave' => [
                'active' => get_setting('flutterwave_activation') == 1,
                'sandbox' => get_setting('flutterwave_sandbox') == 1
            ],
            'mpesa' => [
                'active' => get_setting('mpesa_activation') == 1,
                'environment' => get_setting('mpesa_environment') ?? 'sandbox'
            ],
            'payfast' => [
                'active' => get_setting('payfast_activation') == 1,
                'sandbox' => get_setting('payfast_sandbox') == 1
            ],
            'paydunia' => [
                'active' => get_setting('paydunia_activation') == 1,
                'sandbox' => get_setting('paydunia_sandbox') == 1
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Test payment gateway connection
     */
    public function testConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gateway' => 'required|in:flutterwave,mpesa,payfast,paydunia'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid gateway specified'
            ], 400);
        }

        try {
            $gateway = $request->gateway;
            $result = false;

            switch ($gateway) {
                case 'flutterwave':
                    $result = $this->testFlutterwaveConnection();
                    break;
                case 'mpesa':
                    $result = $this->testMpesaConnection();
                    break;
                case 'payfast':
                    $result = $this->testPayfastConnection();
                    break;
                case 'paydunia':
                    $result = $this->testPayDuniaConnection();
                    break;
            }

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Connection successful' : 'Connection failed'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test Flutterwave connection
     */
    private function testFlutterwaveConnection()
    {
        // Implementation for testing Flutterwave connection
        return true; // Placeholder
    }

    /**
     * Test M-Pesa connection
     */
    private function testMpesaConnection()
    {
        // Implementation for testing M-Pesa connection
        return true; // Placeholder
    }

    /**
     * Test Payfast connection
     */
    private function testPayfastConnection()
    {
        // Implementation for testing Payfast connection
        return true; // Placeholder
    }

    /**
     * Test PayDunia connection
     */
    private function testPayDuniaConnection()
    {
        // Implementation for testing PayDunia connection
        return true; // Placeholder
    }
} 