<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('return_pickup_details')) {
        Schema::create('return_pickup_details', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('return_request_info_id')->constrained('return_request_infos')->onDelete('cascade');
            $table->date('pickup_date');
            $table->string('time_slot');
            $table->text('address');
            $table->string('contact_name');
            $table->string('contact_phone');
            $table->text('instructions')->nullable();
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'missed'])->default('scheduled');
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('return_pickup_details');
    }
};
