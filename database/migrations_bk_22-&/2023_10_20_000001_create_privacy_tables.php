<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrivacyTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create user privacy settings table
        if (!Schema::hasTable('user_privacy_settings')) {
            Schema::create('user_privacy_settings', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->boolean('marketing_emails')->default(false);
                $table->boolean('third_party_cookies')->default(false);
                $table->boolean('analytics_tracking')->default(false);
                $table->boolean('data_sharing')->default(false);
                $table->boolean('personalization')->default(false);
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        // Create privacy consents table
        if (!Schema::hasTable('privacy_consents')) {
            Schema::create('privacy_consents', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('type');
                $table->text('description');
                $table->boolean('granted')->default(false);
                $table->timestamp('granted_at')->nullable();
                $table->timestamp('revoked_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->string('version');
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->index(['user_id', 'type']);
            });
        }

        // Create privacy policies table
        if (!Schema::hasTable('privacy_policies')) {
            Schema::create('privacy_policies', function (Blueprint $table) {
                $table->id();
                $table->string('version');
                $table->date('effective_date');
                $table->longText('content');
                $table->boolean('is_active')->default(false);
                $table->timestamps();

                $table->index('version');
                $table->index('is_active');
            });
        }

        // Create user preferences table if not exists
        if (!Schema::hasTable('user_preferences')) {
            Schema::create('user_preferences', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('key');
                $table->text('value');
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->unique(['user_id', 'key']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop privacy policies table
        Schema::dropIfExists('privacy_policies');

        // Drop privacy consents table
        Schema::dropIfExists('privacy_consents');

        // Drop user privacy settings table
        Schema::dropIfExists('user_privacy_settings');

        // Drop user preferences table
        Schema::dropIfExists('user_preferences');
    }
} 