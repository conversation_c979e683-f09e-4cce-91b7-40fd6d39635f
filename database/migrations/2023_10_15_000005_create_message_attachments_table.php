<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('message_attachments', function (Blueprint $table) {
                $table->id();
                //$table->foreignId('message_id')->references('id')->on('messages')->onDelete('cascade');
                $table->foreignId('message_id');
                $table->string('name');
                $table->string('url');
                $table->string('type');
                $table->unsignedInteger('size');
                $table->timestamps();
                $table->softDeletes();
                //$table->foreign('message_id')->references('id')->on('messages')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('message_attachments');
    }
};
