<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaykuTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('payku_transactions')) {
            Schema::create('payku_transactions', function (Blueprint $table) {
                $table->id();
                $table->string('transaction_id')->unique();
                $table->string('order_id')->nullable();
                $table->string('status');
                $table->decimal('amount', 10, 2);
                $table->string('currency', 3)->default('CLP');
                $table->string('payment_method')->nullable();
                $table->json('payment_details')->nullable();
                $table->string('customer_email')->nullable();
                $table->string('customer_name')->nullable();
                $table->string('customer_document')->nullable();
                $table->string('full_name')->nullable();
                $table->timestamp('paid_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payku_transactions');
    }
} 