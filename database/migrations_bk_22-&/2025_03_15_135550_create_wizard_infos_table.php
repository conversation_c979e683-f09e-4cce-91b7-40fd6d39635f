<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('wizard_infos')) {
        Schema::create('wizard_infos', function (Blueprint $table) {
            $table->id();
            $table->string('wizard_info_name', 255);
            $table->string('background_color', 255)->nullable();
            $table->string('slug',255)->unique();
            $table->longText('wizard_info_description')->nullable();
            $table->string('used_in')->nullable()->comment('user in home_page,category_page,product_page');
            $table->string('type')->nullable()->comment('for banner,slider,video,category,product,brand,blog');
            $table->longText('wizard_info_html')->nullable();
            $table->integer('start_date')->nullable();
            $table->integer('end_date')->nullable();
            $table->string('wizard_info_redirect_url',255)->nullable();
            $table->string('wizard_info_images',255)->nullable()->comment('images id in [1,2,3]');
            $table->string('wizard_info_images_url',255)->nullable()->comment('images url in [1 no image redirect url,2,3]');
            $table->string('wizard_info_mobile_images',255)->nullable()->comment('images id in [1,2,3]');
            $table->string('wizard_info_mobile_images_url',255)->nullable()->comment('images url in [1 no image redirect url,2,3]');
            $table->integer('wizard_info_position')->default(0)->nullable();
            $table->boolean('wizard_info_status')->default(1);
            $table->string('admin_note')->nullable();
            $table->string('admin_view_file')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wizard_infos');
    }
};
