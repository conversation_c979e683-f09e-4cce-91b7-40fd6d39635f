<?php
// 2023_10_20_000001_create_user_privacy_settings_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Create privacy consents table
        Schema::create('privacy_consents', function (Blueprint $table) {
                $table->id();
                //$table->unsignedInteger('user_id');
                $table->foreignId('user_id');
                $table->string('type');
                $table->text('description');
                $table->boolean('granted')->default(false);
                $table->timestamp('granted_at')->nullable();
                $table->timestamp('revoked_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->string('version');
                $table->timestamps();

                //$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->index(['user_id', 'type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_privacy_settings');
    }
};
