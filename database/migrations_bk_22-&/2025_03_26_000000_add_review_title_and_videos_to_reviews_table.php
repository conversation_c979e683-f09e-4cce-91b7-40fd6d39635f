<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            if (!Schema::hasColumn('reviews', 'review_title')) {
                $table->string('review_title')->nullable()->after('product_id');
            }
            
            // Change photos column to TEXT to support JSON arrays
            if (Schema::hasColumn('reviews', 'photos')) {
                $table->text('photos')->nullable()->change();
            }
            
            if (!Schema::hasColumn('reviews', 'review_videos')) {
                $table->text('review_videos')->nullable()->after('photos');
            } else {
                // Change review_videos column to TEXT to support JSON arrays
                $table->text('review_videos')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn('review_title');
            $table->dropColumn('review_videos');
        });
    }
};