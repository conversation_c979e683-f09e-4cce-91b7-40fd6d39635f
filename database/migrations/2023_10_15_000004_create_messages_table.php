<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('messages')) {
            Schema::create('messages', function (Blueprint $table) {
                $table->id();
                $table->foreignId('thread_id');
                $table->foreignId('sender_id');
                $table->text('content');
                $table->timestamps();
                $table->softDeletes();
                //$table->foreign('thread_id')->references('id')->on('message_threads')->onDelete('cascade');
                //$table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
