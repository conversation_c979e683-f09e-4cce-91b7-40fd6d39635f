<?php

// 2023_10_20_000001_create_user_privacy_settings_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Create privacy policies table
        Schema::create('privacy_policies', function (Blueprint $table) {
                $table->id();
                $table->string('version');
                $table->date('effective_date');
                $table->longText('content');
                $table->boolean('is_active')->default(false);
                $table->timestamps();

                $table->index('version');
                $table->index('is_active');
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('user_privacy_settings');
    }
};
