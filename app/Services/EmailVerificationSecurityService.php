<?php

namespace App\Services;

use App\Models\EmailVerify;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class EmailVerificationSecurityService
{
    /**
     * Security configuration
     */
    private const MAX_FAILED_ATTEMPTS = 5;
    private const LOCKOUT_DURATION = 3600; // 1 hour
    private const SUSPICIOUS_THRESHOLD = 10;
    private const CLEANUP_BATCH_SIZE = 1000;

    /**
     * Check if IP is blocked due to suspicious activity
     */
    public function isIpBlocked(string $ip): bool
    {
        $key = "blocked_ip:{$ip}";
        return Cache::has($key);
    }

    /**
     * Block IP for suspicious activity
     */
    public function blockIp(string $ip, int $duration = self::LOCKOUT_DURATION): void
    {
        $key = "blocked_ip:{$ip}";
        Cache::put($key, true, $duration);
        
        Log::warning('IP blocked for suspicious email verification activity', [
            'ip' => $ip,
            'duration' => $duration,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Track failed OTP attempts
     */
    public function trackFailedAttempt(string $email, string $ip): void
    {
        $emailKey = "failed_otp:{$email}";
        $ipKey = "failed_otp_ip:{$ip}";
        
        $emailAttempts = Cache::increment($emailKey, 1);
        $ipAttempts = Cache::increment($ipKey, 1);
        
        // Set expiration if this is the first attempt
        if ($emailAttempts === 1) {
            Cache::put($emailKey, $emailAttempts, self::LOCKOUT_DURATION);
        }
        if ($ipAttempts === 1) {
            Cache::put($ipKey, $ipAttempts, self::LOCKOUT_DURATION);
        }
        
        // Block if too many attempts
        if ($emailAttempts >= self::MAX_FAILED_ATTEMPTS) {
            $this->blockEmail($email);
        }
        
        if ($ipAttempts >= self::SUSPICIOUS_THRESHOLD) {
            $this->blockIp($ip);
        }
        
        Log::warning('Failed OTP attempt tracked', [
            'email' => hash('sha256', $email),
            'ip' => $ip,
            'email_attempts' => $emailAttempts,
            'ip_attempts' => $ipAttempts,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Block email for too many failed attempts
     */
    public function blockEmail(string $email, int $duration = self::LOCKOUT_DURATION): void
    {
        $key = "blocked_email:" . hash('sha256', $email);
        Cache::put($key, true, $duration);
        
        Log::warning('Email blocked for too many failed OTP attempts', [
            'email' => hash('sha256', $email),
            'duration' => $duration,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Check if email is blocked
     */
    public function isEmailBlocked(string $email): bool
    {
        $key = "blocked_email:" . hash('sha256', $email);
        return Cache::has($key);
    }

    /**
     * Clear failed attempts after successful verification
     */
    public function clearFailedAttempts(string $email, string $ip): void
    {
        $emailKey = "failed_otp:{$email}";
        $ipKey = "failed_otp_ip:{$ip}";
        
        Cache::forget($emailKey);
        Cache::forget($ipKey);
        
        Log::info('Failed attempts cleared after successful verification', [
            'email' => hash('sha256', $email),
            'ip' => $ip,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Detect and prevent email enumeration attacks
     */
    public function detectEmailEnumeration(string $ip): bool
    {
        $key = "email_enum:{$ip}";
        $attempts = Cache::get($key, []);
        
        // Clean old attempts (older than 1 hour)
        $cutoff = now()->subHour();
        $attempts = array_filter($attempts, function($timestamp) use ($cutoff) {
            return Carbon::parse($timestamp)->greaterThan($cutoff);
        });
        
        // Add current attempt
        $attempts[] = now()->toISOString();
        
        // Store updated attempts
        Cache::put($key, $attempts, 3600); // 1 hour
        
        // Flag if more than 20 different email checks in 1 hour
        if (count($attempts) > 20) {
            $this->blockIp($ip, 7200); // Block for 2 hours
            return true;
        }
        
        return false;
    }

    /**
     * Generate secure token with additional entropy
     */
    public function generateSecureToken(int $length = 32): string
    {
        try {
            return bin2hex(random_bytes($length / 2));
        } catch (\Exception $e) {
            Log::error('Failed to generate secure token: ' . $e->getMessage());
            // Fallback to less secure but still acceptable method
            return hash('sha256', uniqid(mt_rand(), true) . microtime(true));
        }
    }

    /**
     * Validate token format and security
     */
    public function validateTokenSecurity(string $token): bool
    {
        // Check minimum length
        if (strlen($token) < 32) {
            return false;
        }
        
        // Check if token is hexadecimal
        if (!ctype_xdigit($token)) {
            return false;
        }
        
        // Check entropy (basic check for randomness)
        $chars = str_split($token);
        $unique = array_unique($chars);
        $entropy = count($unique) / count($chars);
        
        // Should have at least 30% unique characters (more lenient for hex)
        // Hex tokens can have repeated characters and still be secure
        return $entropy >= 0.3;
    }

    /**
     * Clean up expired security records
     */
    public function cleanupExpiredRecords(): array
    {
        $stats = [
            'expired_otps' => 0,
            'old_records' => 0,
            'reset_resend_counts' => 0
        ];
        
        try {
            // Clean expired OTPs
            $stats['expired_otps'] = EmailVerify::batchCleanupExpiredOTPs(self::CLEANUP_BATCH_SIZE);
            
            // Delete old unverified records
            $stats['old_records'] = EmailVerify::batchDeleteOldRecords(1, self::CLEANUP_BATCH_SIZE);
            
            // Reset expired resend counts
            $stats['reset_resend_counts'] = EmailVerify::resetExpiredResendCounts();
            
            Log::info('Email verification security cleanup completed', $stats);
            
        } catch (\Exception $e) {
            Log::error('Email verification security cleanup failed: ' . $e->getMessage());
        }
        
        return $stats;
    }

    /**
     * Get security statistics for monitoring
     */
    public function getSecurityStatistics(): array
    {
        try {
            // Try to get EmailVerify statistics, but handle gracefully if not available
            $stats = [];
            
            try {
                $stats = EmailVerify::getCleanupStatistics();
            } catch (\Exception $e) {
                // If EmailVerify is not available (e.g., in tests), provide default stats
                $stats = [
                    'expired_otps_count' => 0,
                    'old_unverified_count' => 0,
                    'high_resend_count' => 0,
                    'total_verified' => 0,
                    'total_pending' => 0
                ];
            }
            
            // Add rate limiting statistics
            $stats['rate_limits'] = [
                'active_email_limits' => $this->countActiveRateLimits('email_verification_email:*'),
                'active_ip_limits' => $this->countActiveRateLimits('email_verification_ip:*'),
                'blocked_ips' => $this->countBlockedItems('blocked_ip:*'),
                'blocked_emails' => $this->countBlockedItems('blocked_email:*')
            ];
            
            return $stats;
            
        } catch (\Exception $e) {
            Log::error('Failed to get security statistics: ' . $e->getMessage());
            return [
                'rate_limits' => [
                    'active_email_limits' => 0,
                    'active_ip_limits' => 0,
                    'blocked_ips' => 0,
                    'blocked_emails' => 0
                ]
            ];
        }
    }

    /**
     * Count active rate limits
     */
    private function countActiveRateLimits(string $pattern): int
    {
        try {
            // This is a simplified count - in production you might want to use Redis SCAN
            return 0; // Placeholder - implement based on your cache driver
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Count blocked items
     */
    private function countBlockedItems(string $pattern): int
    {
        try {
            // This is a simplified count - in production you might want to use Redis SCAN
            return 0; // Placeholder - implement based on your cache driver
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Validate OTP format and security
     */
    public function validateOtpFormat(string $otp): bool
    {
        // Must be exactly 6 digits
        if (!preg_match('/^\d{6}$/', $otp)) {
            return false;
        }
        
        // Should not be all the same digit (weak OTP)
        if (preg_match('/^(\d)\1{5}$/', $otp)) {
            return false;
        }
        
        // Should not be sequential (123456, 654321)
        $sequential = ['123456', '654321', '012345', '543210'];
        if (in_array($otp, $sequential)) {
            return false;
        }
        
        return true;
    }

    /**
     * Log security event with proper formatting
     */
    public function logSecurityEvent(string $event, array $data): void
    {
        $logData = array_merge([
            'event' => $event,
            'timestamp' => now()->toISOString(),
            'service' => 'EmailVerificationSecurity'
        ], $data);
        
        Log::info('Email verification security event', $logData);
    }

    /**
     * Check if request shows signs of automation/bot activity
     */
    public function detectBotActivity(string $userAgent, array $headers): bool
    {
        // Common bot indicators
        $botIndicators = [
            'curl', 'wget', 'python', 'bot', 'crawler', 'spider',
            'scraper', 'automation', 'headless', 'phantom'
        ];
        
        $userAgentLower = strtolower($userAgent);
        
        foreach ($botIndicators as $indicator) {
            if (strpos($userAgentLower, $indicator) !== false) {
                return true;
            }
        }
        
        // Check for missing common browser headers
        $expectedHeaders = ['accept', 'accept-language', 'accept-encoding'];
        $missingHeaders = 0;
        
        foreach ($expectedHeaders as $header) {
            if (!isset($headers[$header])) {
                $missingHeaders++;
            }
        }
        
        // Flag if missing more than 1 common header
        return $missingHeaders > 1;
    }
}