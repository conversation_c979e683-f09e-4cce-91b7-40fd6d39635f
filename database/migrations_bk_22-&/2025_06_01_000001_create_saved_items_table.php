<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saved_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->nullable()->index();
            $table->string('temp_user_id')->nullable()->index();
            $table->integer('product_id')->index();
            $table->string('variation')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('original_price', 10, 2)->nullable();
            $table->integer('quantity')->default(1);
            $table->json('options')->nullable();
            $table->string('currency', 3)->default('USD');
            $table->timestamp('moved_from_cart_at')->nullable();
            $table->timestamps();

            // Unique constraint to prevent duplicate saved items
            $table->index(['user_id', 'product_id']);
            $table->index(['temp_user_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saved_items');
    }
}; 