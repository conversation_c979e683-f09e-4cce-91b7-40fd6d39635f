<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Get all tables from the database
        $tables = DB::select('SHOW TABLES');
        $dbName = DB::getDatabaseName();
        $tableKey = 'Tables_in_' . $dbName;
        
        // Get the current batch number
        $currentBatch = DB::table('migrations')->max('batch') ?? 0;
        $currentBatch++;
        
        // Insert migrations for all existing tables
        foreach ($tables as $table) {
            $tableName = $table->$tableKey;
            if ($tableName !== 'migrations') {
                // Create a migration name based on the table name
                $migrationName = date('Y_m_d_000000') . '_create_' . $tableName . '_table';
                
                // Check if this migration is already recorded
                $exists = DB::table('migrations')
                    ->where('migration', $migrationName)
                    ->exists();
                
                if (!$exists) {
                    DB::table('migrations')->insert([
                        'migration' => $migrationName,
                        'batch' => $currentBatch
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No need to do anything in down() as we're just fixing the migrations table
    }
};
