<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_info', function (Blueprint $table) {
            // For wallet balance usage
            if (!Schema::hasColumn('cart_info', 'wallet_amount_used')) {
                $table->decimal('wallet_amount_used', 10, 2)->default(0);
            }

            // For reward points redemption
            if (!Schema::hasColumn('cart_info', 'reward_points_used')) {
                $table->integer('reward_points_used')->default(0);
            }
            if (!Schema::hasColumn('cart_info', 'reward_points_discount')) {
                $table->decimal('reward_points_discount', 10, 2)->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_info', function (Blueprint $table) {
            if (Schema::hasColumn('cart_info', 'wallet_amount_used')) {
                $table->dropColumn('wallet_amount_used');
            }
            if (Schema::hasColumn('cart_info', 'reward_points_used')) {
                $table->dropColumn('reward_points_used');
            }
            if (Schema::hasColumn('cart_info', 'reward_points_discount')) {
                $table->dropColumn('reward_points_discount');
            }
        });
    }
}; 