<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, add the columns
        Schema::table('wallets', function (Blueprint $table) {
            if (!Schema::hasColumn('wallets', 'balance')) {
                $table->decimal('balance', 10, 2)->default(0.00)->after('user_id');
            }
            
            if (!Schema::hasColumn('wallets', 'reward_points')) {
                $table->integer('reward_points')->default(0)->after('balance');
            }
            
            if (!Schema::hasColumn('wallets', 'promotional_credits')) {
                $table->decimal('promotional_credits', 10, 2)->default(0.00)->after('reward_points');
            }
            
            if (!Schema::hasColumn('wallets', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('promotional_credits');
            }
            
            if (!Schema::hasColumn('wallets', 'last_updated_at')) {
                $table->timestamp('last_updated_at')->nullable()->after('status');
            }
        });
        
        // Second, copy amount to balance if both exist
        if (Schema::hasColumn('wallets', 'amount') && Schema::hasColumn('wallets', 'balance')) {
            DB::statement('UPDATE wallets SET balance = amount');
        }
        
        // Third, drop unnecessary columns
        Schema::table('wallets', function (Blueprint $table) {
            if (Schema::hasColumn('wallets', 'amount')) {
                $table->dropColumn('amount');
            }
            
            if (Schema::hasColumn('wallets', 'payment_method')) {
                $table->dropColumn('payment_method');
            }
            
            if (Schema::hasColumn('wallets', 'payment_details')) {
                $table->dropColumn('payment_details');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('wallets', function (Blueprint $table) {
            // First, add back the old columns
            $table->decimal('amount', 20, 2)->after('user_id');
            $table->string('payment_method')->nullable()->after('amount');
            $table->longText('payment_details')->nullable()->after('payment_method');
            
            // Then drop the new columns
            $table->dropColumn('balance');
            $table->dropColumn('reward_points');
            $table->dropColumn('promotional_credits');
            $table->dropColumn('status');
            $table->dropColumn('last_updated_at');
        });
    }
};
