<?php

// 2023_10_20_000001_create_user_privacy_settings_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('user_preferences')) {
            Schema::create('user_preferences', function (Blueprint $table) {
                $table->id();
                //$table->unsignedInteger('user_id');
                $table->foreignId('user_id');
                $table->string('key');
                $table->text('value');
                $table->timestamps();

                //$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->unique(['user_id', 'key']);
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('user_privacy_settings');
    }
};
