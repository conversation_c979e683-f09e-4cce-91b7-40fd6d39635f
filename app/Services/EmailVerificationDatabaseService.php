<?php

namespace App\Services;

use App\Models\EmailVerify;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmailVerificationDatabaseService
{
    /**
     * Cleanup expired OTP records
     * This method removes expired OTPs and resets resend counts for old records
     */
    public function cleanupExpiredRecords(): array
    {
        $results = [
            'expired_otps_cleared' => 0,
            'resend_counts_reset' => 0,
            'old_records_deleted' => 0
        ];

        try {
            DB::beginTransaction();

            // 1. Clear expired OTPs (set otp to null for expired records)
            $expiredOtpsCleared = EmailVerify::where('expire_time', '<', Carbon::now())
                ->where('is_verify', false)
                ->whereNotNull('otp')
                ->update(['otp' => null]);
            
            $results['expired_otps_cleared'] = $expiredOtpsCleared;

            // 2. Reset resend counts for records older than 1 hour
            $resendCountsReset = EmailVerify::where('last_resend_at', '<', Carbon::now()->subHour())
                ->where('resend_count', '>=', 5)
                ->update(['resend_count' => 0]);
            
            $results['resend_counts_reset'] = $resendCountsReset;

            // 3. Delete very old unverified records (older than 24 hours)
            $oldRecordsDeleted = EmailVerify::where('created_at', '<', Carbon::now()->subDay())
                ->where('is_verify', false)
                ->whereNull('otp')
                ->delete();
            
            $results['old_records_deleted'] = $oldRecordsDeleted;

            DB::commit();

            Log::info('Email verification cleanup completed', $results);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Email verification cleanup failed: ' . $e->getMessage());
            throw $e;
        }

        return $results;
    }

    /**
     * Optimize email lookup performance by ensuring proper indexes exist
     */
    public function optimizeEmailLookupPerformance(): array
    {
        $results = [
            'indexes_checked' => 0,
            'indexes_created' => 0,
            'performance_optimized' => false
        ];

        try {
            // Check if required indexes exist on email_verify table
            $emailVerifyIndexes = $this->getTableIndexes('email_verify');
            $results['indexes_checked']++;

            // Check if composite index for (email, otp) exists
            $hasEmailOtpIndex = collect($emailVerifyIndexes)->contains(function ($index) {
                return str_contains($index['Key_name'], 'email') && str_contains($index['Key_name'], 'otp');
            });

            // Check if expire_time index exists
            $hasExpireTimeIndex = collect($emailVerifyIndexes)->contains(function ($index) {
                return str_contains($index['Key_name'], 'expire_time');
            });

            // Check if last_resend_at index exists for cleanup performance
            $hasLastResendIndex = collect($emailVerifyIndexes)->contains(function ($index) {
                return str_contains($index['Key_name'], 'last_resend_at');
            });

            // Create missing indexes if needed (these should already exist from migration)
            if (!$hasEmailOtpIndex) {
                DB::statement('CREATE INDEX idx_email_verify_email_otp ON email_verify (email, otp)');
                $results['indexes_created']++;
            }

            if (!$hasExpireTimeIndex) {
                DB::statement('CREATE INDEX idx_email_verify_expire_time ON email_verify (expire_time)');
                $results['indexes_created']++;
            }

            if (!$hasLastResendIndex) {
                DB::statement('CREATE INDEX idx_email_verify_last_resend_at ON email_verify (last_resend_at)');
                $results['indexes_created']++;
            }

            // Check users table email index (should exist)
            $userIndexes = $this->getTableIndexes('users');
            $results['indexes_checked']++;

            $hasUserEmailIndex = collect($userIndexes)->contains(function ($index) {
                return $index['Column_name'] === 'email' && $index['Non_unique'] == 0;
            });

            if (!$hasUserEmailIndex) {
                Log::warning('Users table missing unique email index - this should be handled by Laravel migration');
            }

            $results['performance_optimized'] = true;
            Log::info('Email lookup performance optimization completed', $results);

        } catch (\Exception $e) {
            Log::error('Email lookup performance optimization failed: ' . $e->getMessage());
            throw $e;
        }

        return $results;
    }

    /**
     * Get table indexes information
     */
    private function getTableIndexes(string $tableName): array
    {
        return DB::select("SHOW INDEX FROM {$tableName}");
    }

    /**
     * Verify database compatibility with existing authentication token structure
     */
    public function verifyAuthenticationCompatibility(): array
    {
        $results = [
            'user_table_compatible' => false,
            'email_verify_table_compatible' => false,
            'sanctum_tokens_compatible' => false,
            'required_columns_present' => []
        ];

        try {
            // Check User model required columns for authentication
            $userColumns = $this->getTableColumns('users');
            $requiredUserColumns = ['id', 'email', 'password', 'email_verified_at', 'remember_token'];
            
            foreach ($requiredUserColumns as $column) {
                $columnExists = collect($userColumns)->contains('Field', $column);
                $results['required_columns_present']['users'][$column] = $columnExists;
            }

            $results['user_table_compatible'] = collect($results['required_columns_present']['users'])->every(fn($exists) => $exists);

            // Check EmailVerify table required columns
            $emailVerifyColumns = $this->getTableColumns('email_verify');
            $requiredEmailVerifyColumns = ['id', 'email', 'otp', 'generate_date', 'expire_time', 'is_verify', 'resend_count', 'last_resend_at'];
            
            foreach ($requiredEmailVerifyColumns as $column) {
                $columnExists = collect($emailVerifyColumns)->contains('Field', $column);
                $results['required_columns_present']['email_verify'][$column] = $columnExists;
            }

            $results['email_verify_table_compatible'] = collect($results['required_columns_present']['email_verify'])->every(fn($exists) => $exists);

            // Check if personal_access_tokens table exists (Laravel Sanctum)
            $sanctumTableExists = $this->tableExists('personal_access_tokens');
            $results['sanctum_tokens_compatible'] = $sanctumTableExists;

            if ($sanctumTableExists) {
                $sanctumColumns = $this->getTableColumns('personal_access_tokens');
                $requiredSanctumColumns = ['id', 'tokenable_type', 'tokenable_id', 'name', 'token', 'abilities'];
                
                foreach ($requiredSanctumColumns as $column) {
                    $columnExists = collect($sanctumColumns)->contains('Field', $column);
                    $results['required_columns_present']['personal_access_tokens'][$column] = $columnExists;
                }

                $results['sanctum_tokens_compatible'] = collect($results['required_columns_present']['personal_access_tokens'])->every(fn($exists) => $exists);
            }

            Log::info('Authentication compatibility verification completed', $results);

        } catch (\Exception $e) {
            Log::error('Authentication compatibility verification failed: ' . $e->getMessage());
            throw $e;
        }

        return $results;
    }

    /**
     * Get table columns information
     */
    private function getTableColumns(string $tableName): array
    {
        return DB::select("SHOW COLUMNS FROM {$tableName}");
    }

    /**
     * Check if table exists
     */
    private function tableExists(string $tableName): bool
    {
        $result = DB::select("SHOW TABLES LIKE '{$tableName}'");
        return !empty($result);
    }

    /**
     * Get database statistics for monitoring
     */
    public function getDatabaseStatistics(): array
    {
        $stats = [
            'email_verify_records' => [
                'total' => 0,
                'verified' => 0,
                'pending' => 0,
                'expired' => 0,
                'in_cooldown' => 0
            ],
            'users_count' => 0,
            'performance_metrics' => []
        ];

        try {
            // Email verification statistics
            $stats['email_verify_records']['total'] = EmailVerify::count();
            $stats['email_verify_records']['verified'] = EmailVerify::where('is_verify', true)->count();
            $stats['email_verify_records']['pending'] = EmailVerify::where('is_verify', false)->whereNotNull('otp')->count();
            $stats['email_verify_records']['expired'] = EmailVerify::where('expire_time', '<', Carbon::now())->where('is_verify', false)->count();
            $stats['email_verify_records']['in_cooldown'] = EmailVerify::where('last_resend_at', '>', Carbon::now()->subMinutes(5))->where('is_verify', false)->count();

            // Users count
            $stats['users_count'] = User::count();

            // Performance metrics (query execution times)
            $start = microtime(true);
            User::where('email', '<EMAIL>')->first();
            $stats['performance_metrics']['user_email_lookup_ms'] = round((microtime(true) - $start) * 1000, 2);

            $start = microtime(true);
            EmailVerify::where('email', '<EMAIL>')->where('otp', '123456')->first();
            $stats['performance_metrics']['email_verify_lookup_ms'] = round((microtime(true) - $start) * 1000, 2);

        } catch (\Exception $e) {
            Log::error('Failed to get database statistics: ' . $e->getMessage());
        }

        return $stats;
    }

    /**
     * Ensure database integrity and consistency
     */
    public function ensureDatabaseIntegrity(): array
    {
        $results = [
            'integrity_checks_passed' => 0,
            'integrity_issues_found' => 0,
            'issues_fixed' => 0,
            'issues' => []
        ];

        try {
            DB::beginTransaction();

            // 1. Check for orphaned email verification records (emails not in users table but marked as verified)
            $orphanedVerified = EmailVerify::where('is_verify', true)
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('users')
                        ->whereRaw('users.email = email_verify.email');
                })
                ->get();

            if ($orphanedVerified->count() > 0) {
                $results['integrity_issues_found']++;
                $results['issues'][] = "Found {$orphanedVerified->count()} orphaned verified email records";
                
                // Clean up orphaned verified records
                EmailVerify::where('is_verify', true)
                    ->whereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('users')
                            ->whereRaw('users.email = email_verify.email');
                    })
                    ->delete();
                
                $results['issues_fixed']++;
            } else {
                $results['integrity_checks_passed']++;
            }

            // 2. Check for inconsistent OTP states (expired but not cleared)
            $inconsistentOTPs = EmailVerify::where('expire_time', '<', Carbon::now())
                ->where('is_verify', false)
                ->whereNotNull('otp')
                ->count();

            if ($inconsistentOTPs > 0) {
                $results['integrity_issues_found']++;
                $results['issues'][] = "Found {$inconsistentOTPs} expired OTPs that should be cleared";
                
                // Fix inconsistent OTP states
                EmailVerify::where('expire_time', '<', Carbon::now())
                    ->where('is_verify', false)
                    ->whereNotNull('otp')
                    ->update(['otp' => null]);
                
                $results['issues_fixed']++;
            } else {
                $results['integrity_checks_passed']++;
            }

            // 3. Check for records with invalid resend counts
            $invalidResendCounts = EmailVerify::where('resend_count', '<', 0)
                ->orWhere('resend_count', '>', 10) // Reasonable upper limit
                ->count();

            if ($invalidResendCounts > 0) {
                $results['integrity_issues_found']++;
                $results['issues'][] = "Found {$invalidResendCounts} records with invalid resend counts";
                
                // Fix invalid resend counts
                EmailVerify::where('resend_count', '<', 0)->update(['resend_count' => 0]);
                EmailVerify::where('resend_count', '>', 10)->update(['resend_count' => 5]);
                
                $results['issues_fixed']++;
            } else {
                $results['integrity_checks_passed']++;
            }

            DB::commit();
            Log::info('Database integrity check completed', $results);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Database integrity check failed: ' . $e->getMessage());
            throw $e;
        }

        return $results;
    }
}