<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\EmailVerify;
//implements ShouldQueue
class RegistrationOTPNotification extends Notification
{
    use Queueable;

    protected $emailVerify;

    /**
     * Create a new notification instance.
     */
    public function __construct(EmailVerify $emailVerify)
    {
        $this->emailVerify = $emailVerify;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $data = [
            'subject' => 'Email Verification - BUZFI Registration',
            'user_name' => $notifiable->name ?? 'User',
            'otp' => $this->emailVerify->otp,
            'expire_time' => $this->emailVerify->expire_time,
            'remaining_minutes' => $this->emailVerify->remaining_time,
            'resend_count' => $this->emailVerify->resend_count,
            'app_name' => config('app.name'),
            'support_email' => config('mail.support_email', '<EMAIL>')
        ];

        return (new MailMessage)
            ->subject($data['subject'])
            ->view('emails.registration_otp_verification', ['array' => $data]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'email' => $this->emailVerify->email,
            'otp' => $this->emailVerify->otp,
            'expire_time' => $this->emailVerify->expire_time,
        ];
    }
}
