# Buzfi Laravel Project - Docker Management
# Usage: make [command]

.PHONY: help build up down restart status logs clean install migrate seed fresh composer artisan shell mysql redis backup restore

# Default target
help: ## Show this help message
	@echo "Buzfi Laravel Project - Docker Management Commands"
	@echo "=================================================="
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "URLs after starting:"
	@echo "  Application:    http://localhost/buzfi-new-backend"
	@echo "  phpMyAdmin:     http://localhost:8080"
	@echo "  Redis Commander: http://localhost:8081"

# Docker Management
build: ## Build all Docker containers
	@echo "Building Docker containers..."
	cd docker && docker-compose build --no-cache

up: ## Start all services
	@echo "Starting all services..."
	cd docker && docker-compose up -d
	@echo ""
	@echo "Services started successfully!"
	@echo "Application:     http://localhost/buzfi-new-backend"
	@echo "phpMyAdmin:      http://localhost:8080"
	@echo "Redis Commander: http://localhost:8081"

down: ## Stop all services
	@echo "Stopping all services..."
	cd docker && docker-compose down

restart: ## Restart all services
	@echo "Restarting all services..."
	cd docker && docker-compose restart

stop: ## Stop all services (alias for down)
	@make down

start: ## Start all services (alias for up)
	@make up

status: ## Show status of all containers
	@echo "Container Status:"
	cd docker && docker-compose ps

logs: ## Show logs from all containers
	cd docker && docker-compose logs -f

logs-app: ## Show logs from PHP application
	cd docker && docker-compose logs -f php

logs-nginx: ## Show logs from Nginx
	cd docker && docker-compose logs -f nginx

logs-mysql: ## Show logs from MySQL
	cd docker && docker-compose logs -f mysql

# Application Management
install: ## Install Laravel dependencies and setup
	@echo "Installing Laravel dependencies..."
	cd docker && docker-compose exec php composer install
	@echo "Copying environment file..."
	cd docker && docker-compose exec php cp .env.example .env || true
	@echo "Generating application key..."
	cd docker && docker-compose exec php php artisan key:generate
	@echo "Setting up storage permissions..."
	cd docker && docker-compose exec php php artisan storage:link
	@echo "Clearing caches..."
	cd docker && docker-compose exec php php artisan config:clear
	cd docker && docker-compose exec php php artisan cache:clear
	cd docker && docker-compose exec php php artisan view:clear
	cd docker && docker-compose exec php php artisan route:clear

migrate: ## Run database migrations
	@echo "Running database migrations..."
	cd docker && docker-compose exec php php artisan migrate

migrate-fresh: ## Fresh migration (drops all tables)
	@echo "Running fresh migrations..."
	cd docker && docker-compose exec php php artisan migrate:fresh

seed: ## Run database seeders
	@echo "Running database seeders..."
	cd docker && docker-compose exec php php artisan db:seed

fresh: ## Fresh install with migrations and seeders
	@make migrate-fresh
	@make seed

# Development Tools
composer: ## Run composer commands (usage: make composer cmd="install")
	cd docker && docker-compose exec php composer $(cmd)

artisan: ## Run artisan commands (usage: make artisan cmd="migrate")
	cd docker && docker-compose exec php php artisan $(cmd)

shell: ## Access PHP container shell
	cd docker && docker-compose exec php bash

shell-nginx: ## Access Nginx container shell
	cd docker && docker-compose exec nginx sh

shell-mysql: ## Access MySQL container shell
	cd docker && docker-compose exec mysql bash

# Database Management
mysql: ## Access MySQL CLI
	cd docker && docker-compose exec mysql mysql -u root -proot_password buzfi

mysql-root: ## Access MySQL CLI as root
	cd docker && docker-compose exec mysql mysql -u root -proot_password

redis-cli: ## Access Redis CLI
	cd docker && docker-compose exec redis redis-cli

# Maintenance
clean: ## Clean up Docker resources
	@echo "Cleaning up Docker resources..."
	cd docker && docker-compose down -v
	docker system prune -f
	docker volume prune -f

clean-all: ## Clean everything including images
	@echo "Cleaning all Docker resources..."
	cd docker && docker-compose down -v --rmi all
	docker system prune -af
	docker volume prune -f

backup-db: ## Backup database
	@echo "Creating database backup..."
	cd docker && docker-compose exec mysql mysqldump -u root -proot_password buzfi > ../backup/buzfi_backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Database backup created in backup/ directory"

restore-db: ## Restore database from backup (usage: make restore-db file=backup_file.sql)
	@echo "Restoring database from $(file)..."
	cd docker && docker-compose exec -T mysql mysql -u root -proot_password buzfi < ../backup/$(file)
	@echo "Database restored successfully"

# Quick Setup
setup: ## Complete project setup (build, up, install, migrate)
	@echo "Setting up Buzfi Laravel Project..."
	@make build
	@make up
	@sleep 10
	@make install
	@sleep 5
	@make migrate
	@echo ""
	@echo "Setup completed successfully!"
	@echo "Application:     http://localhost/buzfi-new-backend"
	@echo "phpMyAdmin:      http://localhost:8080"
	@echo "Redis Commander: http://localhost:8081"

# Monitoring
monitor: ## Monitor all container logs
	cd docker && docker-compose logs -f --tail=100

ps: ## Show running containers
	cd docker && docker-compose ps

top: ## Show container resource usage
	docker stats $(shell cd docker && docker-compose ps -q)

# Development helpers
dev-up: ## Start development environment
	@make up
	@make logs-app

dev-down: ## Stop development environment
	@make down

dev-restart: ## Restart development environment
	@make restart
	@make logs-app
