<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //if (!Schema::hasTable('refund_request_products')) {
        Schema::create('refund_request_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('refund_request_info_id');
            $table->foreignId('order_detail_id');
            $table->foreignId('product_id');
            $table->integer('quantity');
            $table->decimal('unit_price');
            $table->decimal('amount', 20, 2);
            $table->enum('refund_status', ['pending', 'approved', 'processing','completed', 'rejected', 'cancelled','paid'])->default('pending')->comment('only for admin');
            $table->foreignId('seller_id')->nullable();
            $table->boolean('seller_approval')->default(0);
            $table->string('admin_note_for_product')->nullable();
            $table->string('seller_note_for_product')->nullable();
            $table->timestamps();
        });
        //}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('refund_request_products');
    }
};
