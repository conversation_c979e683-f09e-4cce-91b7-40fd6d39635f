<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOfferProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('offer_product')) {
            Schema::create('offer_product', function (Blueprint $table) {
                $table->id();
                $table->foreignId('dropshipper_offer_id')->references('id')->on('dropshipper_offers');
                $table->unsignedInteger('product_id');
                $table->timestamps();
                
                $table->foreign('dropshipper_offer_id')->on('dropshipper_offers')->onDelete('cascade');
                // Commented out foreign key constraint which is causing the issue
                // $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                
                $table->unique(['dropshipper_offer_id', 'product_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('offer_product');
    }
} 