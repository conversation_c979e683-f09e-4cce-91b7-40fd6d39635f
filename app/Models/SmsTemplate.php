<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SmsTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'identifier',
        'template',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Get template by identifier
     */
    public static function getByIdentifier($identifier)
    {
        return self::where('identifier', $identifier)->where('status', 1)->first();
    }

    /**
     * Parse template with variables
     */
    public function parseWithVariables($variables = [])
    {
        $template = $this->template;
        
        foreach ($variables as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        
        return $template;
    }
}
