<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('size_charts')) {
            Schema::create('size_charts', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->json('chart_data');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
                });
        }

        // Create pivot table for categories and size charts
        if (!Schema::hasTable('category_size_charts')) {
            Schema::create('category_size_charts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('category_id')->references('id')->on('categories')->onDelete('cascade');
                $table->foreignId('size_chart_id')->references('id')->on('size_charts')->onDelete('cascade');
                $table->timestamps();
            
            $table->unique(['category_id', 'size_chart_id']);
            });
        }

        // Create pivot table for sizes and size charts
        if (!Schema::hasTable('size_chart_sizes')) {
            Schema::create('size_chart_sizes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('size_chart_id')->constrained()->onDelete('cascade');
                $table->foreignId('size_id')->onDelete('cascade');
                $table->timestamps();
            
            $table->unique(['size_chart_id', 'size_id']);
            });
        }

        // Create product sizes relationship table
        if (!Schema::hasTable('product_sizes')) {
            Schema::create('product_sizes', function (Blueprint $table) {
                $table->id();
            $table->foreignId('product_id')->onDelete('cascade');
            $table->foreignId('size_id')->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['product_id', 'size_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_sizes');
        Schema::dropIfExists('size_chart_sizes');
        Schema::dropIfExists('category_size_charts');
        Schema::dropIfExists('size_charts');
    }
}; 