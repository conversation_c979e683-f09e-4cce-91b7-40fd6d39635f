<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('b2b_seller_profile', function (Blueprint $table) {

            $table->json('business_address')
                ->comment('{"street": "123 Commerce St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA"}')
                ->after('address');
            $table->json('shipping_address')->nullable()
                ->comment('{"street": "123 Commerce St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA"}')
                ->after('business_address');
            $table->string('tax_id')->nullable()->comment('Tax ID')->after('shipping_address');
            $table->string('business_type')->nullable()->comment('LLC')->after('tax_id');
            $table->string('website')->nullable()->comment('Website URL')->after('business_type');
            $table->string('business_category')->nullable()->comment('Electronics')->after('website');
        });
    }

    public function down()
    {
        Schema::table('b2b_seller_profile', function (Blueprint $table) {
            $table->dropColumn([
                'slug',
                'business_address',
                'shipping_address',
                'tax_id',
                'business_type',
                'website',
                'business_category'
            ]);
        });
    }
};