<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            // Add balance_type column after amount column
            if (!Schema::hasColumn('wallet_transactions', 'balance_type')) {
                $table->string('balance_type')->default('balance')->after('amount')->comment('balance, reward_points, promotional_credits');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            if (Schema::hasColumn('wallet_transactions', 'balance_type')) {
                $table->dropColumn('balance_type');
            }
        });
    }
};
