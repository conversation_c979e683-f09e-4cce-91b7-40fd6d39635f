<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('user_notification_preferences')) {
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('key')->comment('Preference key (e.g., order_updates, stock_alerts, etc.)');
            $table->boolean('value')->default(true)->comment('Preference value (true/false)');
            $table->timestamps();

            // Add a unique constraint to prevent duplicate keys for the same user
            $table->unique(['user_id', 'key']);
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};
