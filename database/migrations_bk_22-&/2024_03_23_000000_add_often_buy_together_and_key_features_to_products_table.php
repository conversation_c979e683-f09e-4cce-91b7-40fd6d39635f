<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'often_buy_together')) {
                $table->json('often_buy_together')->nullable()->after('warranty');
            }
            if (!Schema::hasColumn('products', 'key_features')) {
                $table->text('key_features')->nullable()->after('often_buy_together');
            }
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'often_buy_together',
                'key_features'
            ]);
        });
    }
};