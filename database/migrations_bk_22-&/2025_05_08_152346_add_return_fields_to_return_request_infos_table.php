<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('return_request_infos', function (Blueprint $table) {
            if (!Schema::hasColumn('return_request_infos', 'returnMethod')) {
                $table->string('returnMethod')->nullable();
            }
            if (!Schema::hasColumn('return_request_infos', 'returnCondition')) {
                $table->string('returnCondition')->nullable();
            }
            if (!Schema::hasColumn('return_request_infos', 'originalPackaging')) {
                $table->boolean('originalPackaging')->default(false);
            }
            if (!Schema::hasColumn('return_request_infos', 'feedback')) {
                $table->json('feedback')->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('return_request_infos', function (Blueprint $table) {
            $table->dropColumn(['returnMethod', 'returnCondition', 'originalPackaging', 'feedback']);
        });
    }
};
