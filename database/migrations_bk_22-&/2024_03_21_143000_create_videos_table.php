<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('videos')) {
        Schema::create('videos', function (Blueprint $table) {
            $table->id();
            $table->string('thumbnail');  // Video thumbnail URL
            $table->string('title')->nullable();
            $table->unsignedInteger('views')->nullable()->default(0);  // View count
            $table->unsignedInteger('duration')->nullable();  // Video duration in seconds
            $table->string('subject_type');
            $table->unsignedBigInteger('subject_id');
            $table->unsignedBigInteger('upload_id');
            $table->integer('sequence')->default(1)->nullable();
            $table->unsignedBigInteger('insert_by')->nullable();
            $table->unsignedBigInteger('update_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    public function down()
    {
        Schema::dropIfExists('videos');
    }
};
