<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
            Schema::table('refund_requests', function (Blueprint $table) {
                if (!Schema::hasColumn('refund_requests', 'return_request_info_id')) {
                    $table->foreignId('return_request_info_id')->nullable();
                }
                if (!Schema::hasColumn('refund_requests', 'payment_charge_id')) {
                    $table->string('payment_charge_id')->nullable();
                }
            });
    }

    public function down()
    {
        Schema::dropIfExists('refund_requests');
    }
};
