<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('conversation_message_read_status', function (Blueprint $table) {
            if (!Schema::hasColumn('conversation_message_read_status', 'is_read')) {
                $table->boolean('is_read')->default(1)->after('user_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('conversation_message_read_status', function (Blueprint $table) {
            if (Schema::hasColumn('conversation_message_read_status', 'is_read')) {
                $table->dropColumn('is_read');
            }
        });
    }
};
