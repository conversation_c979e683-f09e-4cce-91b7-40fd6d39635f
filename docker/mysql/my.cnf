[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql
skip-external-locking

# Fine Tuning
key_buffer_size = 16M
max_allowed_packet = 100M
thread_stack = 192K
thread_cache_size = 8
myisam-recover-options = BACKUP
max_connections = 100
table_open_cache = 64

# Query Cache Configuration
query_cache_limit = 1M
query_cache_size = 16M

# Logging and Replication
log_error = /var/log/mysql/error.log
expire_logs_days = 10
max_binlog_size = 100M

# InnoDB Settings
innodb_buffer_pool_size = 128M
innodb_log_file_size = 5M
innodb_log_buffer_size = 8M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# SQL Mode
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
