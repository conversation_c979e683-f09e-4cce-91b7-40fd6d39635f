<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameDropshipperOffersToOffers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First add the offer_banner field to dropshipper_offers
        Schema::table('dropshipper_offers', function (Blueprint $table) {
            $table->string('offer_banner')->nullable()->after('description');
        });

        // Rename the table
        Schema::rename('dropshipper_offers', 'offers');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Rename back to dropshipper_offers
        Schema::rename('offers', 'dropshipper_offers');

        // Remove the offer_banner field
        Schema::table('dropshipper_offers', function (Blueprint $table) {
            $table->dropColumn('offer_banner');
        });
    }
} 