<template>
  <div class="messaging-widget">
    <!-- Message icon button with counter -->
    <button 
      @click="toggleMessagingModal"
      class="messaging-widget__button"
      :class="{'has-unread': unreadCount > 0}"
    >
      <i class="la la-comment"></i>
      <span v-if="unreadCount > 0" class="messaging-widget__counter">{{ unreadCount }}</span>
    </button>

    <!-- Messaging modal -->
    <div class="messaging-widget__modal" :class="{'active': isModalOpen}">
      <div class="messaging-widget__header">
        <h4>Messages</h4>
        <button @click="toggleMessagingModal" class="close-btn">
          <i class="la la-times"></i>
        </button>
      </div>

      <div class="messaging-widget__tabs">
        <button 
          @click="activeTab = 'conversations'"
          :class="{'active': activeTab === 'conversations'}"
        >
          Conversations
        </button>
        <button 
          @click="activeTab = 'contacts'"
          :class="{'active': activeTab === 'contacts'}"
        >
          Contacts
        </button>
      </div>

      <!-- Conversations tab -->
      <div v-if="activeTab === 'conversations'" class="messaging-widget__conversations">
        <div class="messaging-widget__search">
          <input 
            type="text" 
            v-model="conversationSearch" 
            placeholder="Search conversations..."
            @input="searchConversations"
          >
        </div>
        
        <div class="messaging-widget__conversation-list">
          <div v-if="loadingConversations" class="messaging-widget__loading">
            <i class="la la-spinner la-spin"></i>
          </div>
          <div v-else-if="conversations.length === 0" class="messaging-widget__empty">
            No conversations found.
          </div>
          <div 
            v-else
            v-for="conversation in conversations" 
            :key="conversation.id"
            class="messaging-widget__conversation-item"
            :class="{'unread': conversation.unread_count > 0, 'active': activeConversation && activeConversation.id === conversation.id}"
            @click="setActiveConversation(conversation)"
          >
            <div class="item-avatar">
              <img :src="conversation.participant.avatar || '/assets/img/placeholder.jpg'" alt="Avatar">
            </div>
            <div class="item-content">
              <div class="item-header">
                <h5>{{ conversation.participant.name }}</h5>
                <span class="time">{{ formatTime(conversation.last_message.created_at) }}</span>
              </div>
              <p class="item-message">{{ conversation.last_message.content }}</p>
              <span v-if="conversation.unread_count > 0" class="unread-badge">{{ conversation.unread_count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Contacts tab -->
      <div v-if="activeTab === 'contacts'" class="messaging-widget__contacts">
        <div class="messaging-widget__search">
          <input 
            type="text" 
            v-model="contactSearch" 
            placeholder="Search contacts..."
            @input="searchContacts"
          >
        </div>
        
        <div class="messaging-widget__contact-list">
          <div v-if="loadingContacts" class="messaging-widget__loading">
            <i class="la la-spinner la-spin"></i>
          </div>
          <div v-else-if="contacts.length === 0" class="messaging-widget__empty">
            No contacts found.
          </div>
          <div 
            v-else
            v-for="contact in contacts" 
            :key="contact.id"
            class="messaging-widget__contact-item"
            @click="startConversation(contact)"
          >
            <div class="item-avatar">
              <img :src="contact.avatar || '/assets/img/placeholder.jpg'" alt="Avatar">
            </div>
            <div class="item-content">
              <h5>{{ contact.name }}</h5>
              <span class="user-type">{{ contact.user_type }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Conversation view -->
      <div v-if="activeConversation" class="messaging-widget__conversation-view">
        <div class="messaging-widget__conversation-header">
          <button @click="activeConversation = null" class="back-btn">
            <i class="la la-arrow-left"></i>
          </button>
          <div class="conversation-user">
            <img :src="activeConversation.participant.avatar || '/assets/img/placeholder.jpg'" alt="Avatar">
            <h5>{{ activeConversation.participant.name }}</h5>
          </div>
        </div>

        <div class="messaging-widget__messages" ref="messagesContainer">
          <div v-if="loadingMessages" class="messaging-widget__loading">
            <i class="la la-spinner la-spin"></i>
          </div>
          <div v-else-if="messages.length === 0" class="messaging-widget__empty">
            No messages yet. Start the conversation!
          </div>
          <div v-else class="messaging-widget__message-list">
            <div 
              v-for="message in messages" 
              :key="message.id"
              class="messaging-widget__message"
              :class="{'outgoing': message.user_id === currentUser.id, 'incoming': message.user_id !== currentUser.id}"
            >
              <div class="message-content">
                <p>{{ message.content }}</p>
                <span class="message-time">{{ formatTime(message.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="messaging-widget__composer">
          <textarea 
            v-model="newMessage" 
            placeholder="Type a message..." 
            @keyup.enter.prevent="sendMessage"
          ></textarea>
          <button @click="sendMessage" :disabled="!newMessage.trim()">
            <i class="la la-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isModalOpen: false,
      activeTab: 'conversations',
      unreadCount: 0,
      conversations: [],
      contacts: [],
      messages: [],
      activeConversation: null,
      newMessage: '',
      conversationSearch: '',
      contactSearch: '',
      currentUser: null,
      loadingConversations: false,
      loadingContacts: false,
      loadingMessages: false,
      refreshInterval: null
    }
  },
  
  mounted() {
    this.getCurrentUser();
    this.getUnreadCount();
    this.refreshInterval = setInterval(() => {
      this.getUnreadCount();
      if (this.isModalOpen && this.activeTab === 'conversations') {
        this.getConversations();
      }
      if (this.isModalOpen && this.activeConversation) {
        this.getMessages();
      }
    }, 10000);
    
    // Set up Echo listeners once we have the user
    this.setupEchoListeners();
  },
  
  beforeDestroy() {
    clearInterval(this.refreshInterval);
  },
  
  methods: {
    getCurrentUser() {
      // Get the current user information from the global window object or API
      axios.get('/api/v3/auth/user')
        .then(response => {
          this.currentUser = response.data.user;
        });
    },
    
    toggleMessagingModal() {
      this.isModalOpen = !this.isModalOpen;
      if (this.isModalOpen) {
        if (this.activeTab === 'conversations') {
          this.getConversations();
        } else {
          this.getContacts();
        }
      }
    },
    
    getUnreadCount() {
      axios.get('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/unread-counts')
        .then(response => {
          this.unreadCount = response.data.unread_count;
        });
    },
    
    getConversations() {
      this.loadingConversations = true;
      axios.get('/api/v3/messaging/conversations', {
        params: {
          search: this.conversationSearch
        }
      })
        .then(response => {
          this.conversations = response.data.conversations;
          this.loadingConversations = false;
        })
        .catch(error => {
          console.error(error);
          this.loadingConversations = false;
        });
    },
    
    getContacts() {
      this.loadingContacts = true;
      axios.get('/api/v3/messaging/contacts', {
        params: {
          search: this.contactSearch
        }
      })
        .then(response => {
          this.contacts = response.data.contacts;
          this.loadingContacts = false;
        })
        .catch(error => {
          console.error(error);
          this.loadingContacts = false;
        });
    },
    
    setActiveConversation(conversation) {
      this.activeConversation = conversation;
      this.getMessages();
      if (conversation.unread_count > 0) {
        this.markConversationAsRead(conversation.id);
      }
    },
    
    getMessages() {
      if (!this.activeConversation) return;
      
      this.loadingMessages = true;
      axios.get(`/api/v3/messaging/conversations/${this.activeConversation.id}`)
        .then(response => {
          this.messages = response.data.messages;
          this.loadingMessages = false;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        })
        .catch(error => {
          console.error(error);
          this.loadingMessages = false;
        });
    },
    
    sendMessage() {
      if (!this.newMessage.trim()) return;
      
      const messageData = {
        content: this.newMessage,
        conversation_id: this.activeConversation ? this.activeConversation.id : null,
        recipient_id: this.activeConversation ? null : this.activeContact.id
      };
      
      this.newMessage = '';
      
      axios.post('/api/v3/messaging/conversations', messageData)
        .then(response => {
          if (!this.activeConversation) {
            this.activeConversation = response.data.conversation;
          }
          this.messages.push(response.data.message);
          this.$nextTick(() => {
            this.scrollToBottom();
          });
          this.getConversations();
        })
        .catch(error => {
          console.error(error);
        });
    },
    
    startConversation(contact) {
      this.activeContact = contact;
      this.activeConversation = null;
      this.messages = [];
      this.newMessage = '';
      
      // Check if conversation already exists
      const existingConversation = this.conversations.find(
        conv => conv.participant.id === contact.id
      );
      
      if (existingConversation) {
        this.setActiveConversation(existingConversation);
      }
    },
    
    markConversationAsRead(conversationId) {
      axios.put('/api/v3/messaging/messages/mark-read', {
        conversation_id: conversationId
      })
        .then(() => {
          this.getUnreadCount();
          const convo = this.conversations.find(c => c.id === conversationId);
          if (convo) {
            convo.unread_count = 0;
          }
        });
    },
    
    searchConversations() {
      this.getConversations();
    },
    
    searchContacts() {
      this.getContacts();
    },
    
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
      }
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      
      // If today, show time
      if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      }
      
      // If this year, show date without year
      if (date.getFullYear() === now.getFullYear()) {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
      }
      
      // Otherwise show full date
      return date.toLocaleDateString();
    },
    
    setupEchoListeners() {
      // First wait for the current user to be loaded
      if (!this.currentUser) {
        setTimeout(() => this.setupEchoListeners(), 1000);
        return;
      }
      
      const userId = this.currentUser.id;
      
      // Listen for new messages in conversations
      window.Echo.private(`user.${userId}.conversations`)
        .listen('.message.new', (data) => {
          // If we're viewing the conversation that received a new message
          if (this.activeConversation && this.activeConversation.id === data.conversation_id) {
            // Add the message to the current conversation view
            this.messages.push({
              id: data.message.id,
              content: data.message.content,
              user_id: data.message.sender_id,
              created_at: data.message.created_at,
              attachments: data.message.attachments || []
            });
            
            // Scroll to bottom
            this.$nextTick(() => {
              this.scrollToBottom();
            });
            
            // Mark the message as read
            this.markConversationAsRead(data.conversation_id);
          } else {
            // Update unread count
            this.getUnreadCount();
            
            // If we're viewing the conversations list, refresh it
            if (this.isModalOpen && this.activeTab === 'conversations') {
              this.getConversations();
            }
          }
        })
        .listen('.conversation.updated', (data) => {
          // If we're viewing the conversations list, refresh it
          if (this.isModalOpen && this.activeTab === 'conversations') {
            this.getConversations();
          }
        })
        .listen('.unread.updated', (data) => {
          // Update the unread count
          this.unreadCount = data.unread_count;
        });
    }
  }
}
</script>

<style scoped>
.messaging-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.messaging-widget__button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #377dff;
  color: white;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.messaging-widget__button i {
  font-size: 24px;
}

.messaging-widget__button:hover {
  background-color: #2563eb;
  transform: scale(1.05);
}

.messaging-widget__button.has-unread {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(55, 125, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(55, 125, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(55, 125, 255, 0);
  }
}

.messaging-widget__counter {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #e53e3e;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.messaging-widget__modal {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 25px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9);
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s;
}

.messaging-widget__modal.active {
  transform: scale(1);
  opacity: 1;
  pointer-events: all;
}

.messaging-widget__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.messaging-widget__header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.messaging-widget__header .close-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 20px;
  cursor: pointer;
}

.messaging-widget__tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.messaging-widget__tabs button {
  flex: 1;
  padding: 10px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.messaging-widget__tabs button.active {
  border-bottom-color: #377dff;
  color: #377dff;
}

.messaging-widget__search {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.messaging-widget__search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}

.messaging-widget__conversations,
.messaging-widget__contacts {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messaging-widget__conversation-list,
.messaging-widget__contact-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.messaging-widget__conversation-item,
.messaging-widget__contact-item {
  display: flex;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: 5px;
  position: relative;
}

.messaging-widget__conversation-item:hover,
.messaging-widget__contact-item:hover {
  background-color: #f5f8ff;
}

.messaging-widget__conversation-item.active {
  background-color: #edf2ff;
}

.messaging-widget__conversation-item.unread {
  background-color: #f0f9ff;
}

.item-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.item-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 1;
  overflow: hidden;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.item-header h5 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
}

.time {
  font-size: 12px;
  color: #888;
}

.item-message {
  margin: 0;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #377dff;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
}

.user-type {
  font-size: 12px;
  color: #666;
  display: block;
  margin-top: 3px;
}

.messaging-widget__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 20px;
  color: #888;
}

.messaging-widget__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  font-size: 14px;
}

.messaging-widget__conversation-view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  z-index: 2;
  display: flex;
  flex-direction: column;
}

.messaging-widget__conversation-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.messaging-widget__conversation-header .back-btn {
  background: none;
  border: none;
  margin-right: 10px;
  cursor: pointer;
  font-size: 18px;
}

.conversation-user {
  display: flex;
  align-items: center;
}

.conversation-user img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.conversation-user h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.messaging-widget__messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f8f9fa;
}

.messaging-widget__message {
  margin-bottom: 15px;
  display: flex;
}

.messaging-widget__message.outgoing {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
}

.messaging-widget__message.outgoing .message-content {
  background-color: #377dff;
  color: white;
  border-bottom-right-radius: 4px;
}

.messaging-widget__message.incoming .message-content {
  background-color: white;
  color: #333;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-content p {
  margin: 0;
  font-size: 14px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 5px;
  display: block;
  text-align: right;
}

.messaging-widget__composer {
  display: flex;
  padding: 10px;
  border-top: 1px solid #eee;
}

.messaging-widget__composer textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 8px 15px;
  resize: none;
  height: 40px;
  font-size: 14px;
  margin-right: 10px;
}

.messaging-widget__composer button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #377dff;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.messaging-widget__composer button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .messaging-widget__modal {
    width: 100%;
    height: 100%;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }
}
</style> 