<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ManualPaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'heading',
        'description',
        'bank_info',
        'photo'
    ];

    /**
     * Get the full URL for the photo
     */
    public function getPhotoUrlAttribute()
    {
        if ($this->photo) {
            return asset('storage/' . $this->photo);
        }
        return null;
    }

    /**
     * Scope for active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
