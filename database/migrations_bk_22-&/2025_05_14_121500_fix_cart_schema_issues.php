<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix any remaining cart schema issues

        // Check and fix cart_items table structure
        if (Schema::hasTable('cart_items')) {
            Schema::table('cart_items', function (Blueprint $table) {
                // Add correct foreign key for product_id if it doesn't exist
                if (Schema::hasColumn('cart_items', 'product_id')) {
                    // First remove any invalid constraints
                    try {
                        $sm = Schema::getConnection()->getDoctrineSchemaManager();
                        $foreignKeys = $sm->listTableForeignKeys('cart_items');
                        
                        foreach ($foreignKeys as $foreignKey) {
                            if ($foreignKey->getLocalColumns() === ['product_id']) {
                                $table->dropForeign($foreignKey->getName());
                            }
                        }
                    } catch (\Exception $e) {
                        // Log but continue if this fails
                        \Log::error('Failed to drop foreign key: ' . $e->getMessage());
                    }
                    
                    // Then recreate it properly
                    if (!DB::getSchemaBuilder()->hasTable('products')) {
                        // If products table doesn't exist, we have a bigger problem
                        \Log::error('Products table does not exist');
                    } else {
                        // Make product_id nullable to avoid constraint issues
                        $table->foreignId('product_id')->nullable()->change();
                    }
                }
                
                // Ensure cart_info_id is correctly defined as UUID
                if (Schema::hasColumn('cart_items', 'cart_info_id')) {
                    try {
                        // Change cart_info_id to string/uuid if it's not already
                        $table->uuid('cart_info_id')->nullable()->change();
                    } catch (\Exception $e) {
                        // Log but continue if this fails
                        \Log::error('Failed to change cart_info_id column: ' . $e->getMessage());
                    }
                }
            });
        }

        // If cart_info_id field is in cart_info table, we need to remove it
        if (Schema::hasTable('cart_info') && Schema::hasColumn('cart_info', 'cart_info_id')) {
            Schema::table('cart_info', function (Blueprint $table) {
                $table->dropColumn('cart_info_id');
            });
        }

        // Make sure required columns exist in cart_info
        if (Schema::hasTable('cart_info')) {
            Schema::table('cart_info', function (Blueprint $table) {
                if (!Schema::hasColumn('cart_info', 'total')) {
                    $table->decimal('total', 10, 2)->default(0);
                }
            });
        }

        // Set foreign key relations correctly
        if (Schema::hasTable('cart_items') && Schema::hasTable('cart_info')) {
            try {
                Schema::table('cart_items', function (Blueprint $table) {
                    // First check if the foreign key constraint exists
                    $sm = Schema::getConnection()->getDoctrineSchemaManager();
                    $foreignKeys = $sm->listTableForeignKeys('cart_items');
                    $hasConstraint = false;
                    
                    foreach ($foreignKeys as $foreignKey) {
                        if ($foreignKey->getLocalColumns() === ['cart_info_id']) {
                            $hasConstraint = true;
                            break;
                        }
                    }
                    
                    if (!$hasConstraint) {
                        // Add foreign key relationship to cart_info
                        $table->foreign('cart_info_id')
                            ->references('id')
                            ->on('cart_info')
                            ->onDelete('cascade');
                    }
                });
            } catch (\Exception $e) {
                // Log but continue if this fails
                \Log::error('Failed to create foreign key: ' . $e->getMessage());
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't roll back to prevent data loss
    }
}; 