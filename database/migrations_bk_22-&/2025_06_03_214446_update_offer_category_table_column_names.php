<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOfferCategoryTableColumnNames extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('offer_category', function (Blueprint $table) {
            // Rename dropshipper_offer_id to offer_id
            $table->renameColumn('dropshipper_offer_id', 'offer_id');
        });

        Schema::table('offer_product', function (Blueprint $table) {
            // Rename dropshipper_offer_id to offer_id
            $table->renameColumn('dropshipper_offer_id', 'offer_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('offer_category', function (Blueprint $table) {
            // Rename back to dropshipper_offer_id
            $table->renameColumn('offer_id', 'dropshipper_offer_id');
        });

        Schema::table('offer_product', function (Blueprint $table) {
            // Rename back to dropshipper_offer_id
            $table->renameColumn('offer_id', 'dropshipper_offer_id');
        });
    }
}
