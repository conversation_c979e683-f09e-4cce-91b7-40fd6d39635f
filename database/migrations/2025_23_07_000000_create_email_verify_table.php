<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_verify', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('otp', 6)->nullable();
            $table->timestamp('generate_date')->nullable();
            $table->timestamp('expire_time')->nullable();
            $table->integer('duration')->default(15); // duration in minutes
            $table->boolean('is_verify')->default(false);
            $table->integer('resend_count')->default(0);
            $table->timestamps();

            $table->index(['email', 'otp']);
            $table->index(['expire_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_verify');
    }
}; 