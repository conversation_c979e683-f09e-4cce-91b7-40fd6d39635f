<?php

use App\Http\Controllers\Api\V3\ApiAddressController;
use App\Http\Controllers\Api\V3\ApiCartController;
use App\Http\Controllers\Api\V3\ApiCheckoutController;
use App\Http\Controllers\Api\V3\ApiCompareProductController;
use App\Http\Controllers\Api\V3\ApiProductController;
use App\Http\Controllers\Api\V3\ApiSearchController;
use App\Http\Controllers\Api\V3\ApiShippingMethodController;
use App\Http\Controllers\Api\V3\ApiWishlistController;
use App\Http\Controllers\Api\V3\users\ApiUserController;
use App\Http\Controllers\Api\V3\users\RecentlyViewedProductController;
use App\Http\Controllers\Api\V3\Products\ApiRatingReviewController;
use App\Http\Controllers\Api\V3\ApiStripePaymentController;
use App\Http\Controllers\Api\V3\Products\WishlistController;
use App\Http\Controllers\Api\V3\Products\ApiStockRequestController;
use App\Http\Controllers\Api\V3\ApiOrdersController;
use App\Http\Controllers\Api\V3\ApiStripeController;
use App\Http\Controllers\Api\V3\ApiPremiumProductController;
use App\Http\Controllers\Api\V3\ApiEarlyAccessProductController;
use App\Http\Controllers\Api\V3\ApiSupportTicketController;
use App\Http\Controllers\Api\V3\ApiCategoryController;
use App\Http\Controllers\Api\V3\ApiBrandController;
use App\Http\Controllers\Api\V3\ApiReturnRefundController;
use App\Http\Controllers\Api\V3\ApiNotificationController;
use App\Http\Controllers\Api\V3\Stripe\ApiPaymentMethodController;
use App\Http\Controllers\Api\V3\ApiSavingsTrackerController;
use App\Http\Controllers\Api\V3\ApiOrderMessagesController;
use App\Http\Controllers\Api\V3\Upload\ApiAvatarUploadController;
use App\Http\Controllers\Api\V3\ApiReturnReasonsController;
use App\Http\Controllers\Api\V3\ApiResolutionTypesController;
use App\Http\Controllers\Api\V3\ReturnFaqController;
use App\Http\Controllers\Api\V3\ReturnKnowledgeBaseController;
use App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController;
use App\Http\Controllers\Api\V3\SavingsController;

use App\Http\Controllers\ApiCouponController;
use App\Http\Controllers\ApiInvoiceController;
use App\Http\Controllers\Api\V3\ApiReturnController;
use App\Http\Controllers\Api\V3\ApiRefundController;
use App\Http\Controllers\Api\V3\Wallet\ApiWalletController;
use App\Http\Controllers\Api\V3\Wallet\ApiAdminWalletController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V3\UserPreferencesController;
use App\Http\Controllers\Api\V3\ApiBundleProductController;
use App\Http\Controllers\Api\V3\ApiSizeController;
use App\Http\Controllers\Api\V3\ApiTagsController;
use App\Http\Controllers\Api\V3\ApiIntegrationsController;
// use App\Http\Controllers\Api\V3\ApiAnalyticsController; // Commented out - controller doesn't exist
use App\Http\Controllers\Api\V3\ApiSellerController;
use App\Http\Controllers\Api\V3\ApiVoiceSearchController;
use App\Http\Controllers\Api\V3\ApiFaqController;
use App\Http\Controllers\Api\V3\ApiLiveChatController;
use App\Http\Controllers\Api\V3\ApiCancelRequestController;
use App\Http\Controllers\Api\V3\Products\ApiProductVariantsController;
use App\Http\Controllers\Api\V3\ApiTicketController;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Broadcast;

// CORS Test Endpoint
Route::get('/cors-test', function () {
    return response()->json([
        'message' => 'CORS is working correctly!',
        'timestamp' => now(),
        'origin' => request()->headers->get('Origin'),
        'method' => request()->method(),
        'headers' => [
            'Access-Control-Allow-Origin' => response()->headers->get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods' => response()->headers->get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers' => response()->headers->get('Access-Control-Allow-Headers'),
        ]
    ]);
})->middleware(['cors']);

Route::options('/cors-test', function () {
    return response()->json(['message' => 'CORS preflight successful'], 200);
})->middleware(['cors']);

Route::group(['prefix' => 'v3', 'middleware' => ['app_language']], function () {

    // Upload routes - moved to top to avoid conflicts with wildcard routes
    Route::prefix('upload')->group(function () {
        Route::get('test', [ApiAvatarUploadController::class, 'test']); // Test route
        Route::post('avatar', [ApiAvatarUploadController::class, 'uploadAvatar']);
        Route::delete('avatar', [ApiAvatarUploadController::class, 'removeAvatar']);
        Route::get('avatar', [ApiAvatarUploadController::class, 'getAvatar']);
    });

    Route::group(['prefix' => 'users', 'middleware' => ['app_language']], function () {
        // Routes that can be accessed with either authentication or session_id
        Route::group(['middleware' => ['optional.auth']], function () {
            Route::get('recently-viewed', [RecentlyViewedProductController::class, 'index']);
            Route::post('recently-viewed/view', [RecentlyViewedProductController::class, 'store']);
            Route::delete('recently-viewed/clear', [RecentlyViewedProductController::class, 'destroy']);
            Route::get('recently-viewed/stats', [RecentlyViewedProductController::class, 'stats']);
            Route::delete('recently-viewed/{productId}', [RecentlyViewedProductController::class, 'removeProduct']);
        });

        Route::group(['middleware' => ['api.auth']], function () {
            // Routes that require authentication
            Route::post('recently-viewed/merge', [RecentlyViewedProductController::class, 'mergeSessionToUser']);
            Route::get('me/recently-viewed', [RecentlyViewedProductController::class, 'index']);
            Route::post('me/recently-viewed/view', [RecentlyViewedProductController::class, 'store']);
            Route::delete('me/recently-viewed/clear', [RecentlyViewedProductController::class, 'destroy']);
            Route::get('me/recently-viewed/stats', [RecentlyViewedProductController::class, 'stats']);
            Route::delete('me/recently-viewed/{productId}', [RecentlyViewedProductController::class, 'removeProduct']);

            Route::get('profile', [ApiUserController::class, 'profile']);
            Route::post('profile/picture', [ApiUserController::class, 'uploadProfilePicture']);
            Route::put('change-password', [ApiUserController::class, 'changePassword']);
            Route::get('get_reviews', [ApiRatingReviewController::class, 'get_user_reviews_v2']);
            Route::post('reviews/{reviewId}/helpful', [ApiRatingReviewController::class, 'mark_review_helpful']);
            Route::delete('reviews/{reviewId}', [ApiRatingReviewController::class, 'delete_review']);
        });
    });
    // Updated Cart routes for v3 API
    Route::group(['prefix' => 'cart', 'middleware' => ['app_language', 'optional.auth']], function () {
        // Public cart routes (both guest and authenticated users)
        // Route::get('/', [ApiCartController::class, 'getCart']);
        // Route::post('/add', [ApiCartController::class, 'addToCart']);
        // Route::put('/update', [ApiCartController::class, 'updateCartItem']);
        // Route::delete('/remove', [ApiCartController::class, 'removeCartItem']);
        // Route::post('/clear', [ApiCartController::class, 'clearCart']);
        // Route::post('/coupons', [ApiCartController::class, 'applyCoupon']);
        // Route::delete('/coupons', [ApiCartController::class, 'removeCoupon']);
        Route::get('/shipping-methods', [ApiCartController::class, 'getShippingMethods']);
        Route::post('/shipping-method', [ApiCartController::class, 'applyShippingMethod']);
        Route::post('/calculate-taxes', [ApiCartController::class, 'calculateTaxes']);
        Route::get('/recommendations', [ApiCartController::class, 'getRecommendations']);
        Route::get('/check-stock', [ApiCartController::class, 'checkStock']);
        Route::post('/repair', [ApiCartController::class, 'repairCart']);
        Route::post('/sync', [ApiCartController::class, 'syncCart']);
        // Route::get('/generate-temp-id', [ApiCartController::class, 'generateTempUserId']);
        // Main cart operations
        Route::get('/', 'App\Http\Controllers\Api\V3\CartController@index');
        Route::get('/count', 'App\Http\Controllers\Api\V3\CartController@count');
        Route::post('/add', 'App\Http\Controllers\Api\V3\CartController@addToCart');
        Route::post('/add-multiple', 'App\Http\Controllers\Api\V3\CartController@addMultiple');
        Route::put('/items/{itemId}', 'App\Http\Controllers\Api\V3\CartController@updateItem');
        Route::delete('/items/{itemId}', 'App\Http\Controllers\Api\V3\CartController@removeItem');
        Route::delete('/clear', 'App\Http\Controllers\Api\V3\CartController@clear');

        // Save for later functionality
        Route::post('/items/{itemId}/save-for-later', 'App\Http\Controllers\Api\V3\CartController@saveForLater');
        Route::get('/saved-items', 'App\Http\Controllers\Api\V3\CartController@savedItems');
        Route::post('/saved-items/{savedItemId}/move-to-cart', 'App\Http\Controllers\Api\V3\CartController@moveToCart');
        Route::delete('/saved-items/{savedItemId}', 'App\Http\Controllers\Api\V3\CartController@removeSavedItem');

        // Coupon management
        Route::post('/apply-coupon', 'App\Http\Controllers\Api\V3\CartController@applyCoupon');
        Route::delete('/remove-coupon', 'App\Http\Controllers\Api\V3\CartController@removeCoupon');

        // Cart notes/special instructions
        Route::put('/notes', 'App\Http\Controllers\Api\V3\CartController@updateNotes');
        // User authenticated cart routes
        Route::group(['middleware' => ['api.auth']], function () {
            // Route::post('/merge', [ApiCartController::class, 'mergeCarts']);
            Route::post('/merge', 'App\Http\Controllers\Api\V3\CartController@mergeCart');
            Route::post('/merge-after-login', [\App\Http\Controllers\Api\V3\CartController::class, 'mergeCartAfterLogin']);
            Route::post('/checkout', [ApiCartController::class, 'checkout']);
        });
    });
    
    Route::group(['prefix' => 'checkout', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::post('initialize', [ApiCheckoutController::class, 'initialize']);
        Route::get('current', [ApiCheckoutController::class, 'getCurrent']);
        Route::post('shipping-address', [ApiCheckoutController::class, 'updateShippingAddress']);
        Route::post('billing-address', [ApiCheckoutController::class, 'updateBillingAddress']);
        Route::get('shipping-methods', [ApiCheckoutController::class, 'getShippingMethods']);
        Route::post('shipping', [ApiCheckoutController::class, 'selectShippingMethod']);
        Route::get('payment-methods', [ApiCheckoutController::class, 'getPaymentMethods']);
        Route::post('select-payment-method', [ApiCheckoutController::class, 'selectPaymentMethod']);
        Route::post('apply-coupon', [ApiCheckoutController::class, 'apply_coupon_code']);
        Route::post('remove-coupon', [ApiCheckoutController::class, 'removeCoupon']);
        Route::post('gift-options', [ApiCheckoutController::class, 'updateGiftOptions']);
        Route::post('validate', [ApiCheckoutController::class, 'validateStep']);
        Route::get('summary', [ApiCartController::class, 'checkout_summary']);
        Route::post('address', [ApiCheckoutController::class, 'setaAddress']);
    });
    Route::group(['prefix' => 'orders', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/track-order/{trackingCode}', [ApiOrdersController::class, 'trackOrder']);
        Route::get('order-stats', [ApiOrdersController::class, 'getOrderStats']);

        // Orders list endpoint
        Route::get('/', [ApiOrdersController::class, 'getOrders']);
        Route::get('list', [ApiOrdersController::class, 'order_list']);
        Route::get('stats', [ApiOrdersController::class, 'getOrderStats']);
        Route::get('get_order_details', [ApiOrdersController::class, 'get_order_details']);
        Route::get('get_order_status', [ApiOrdersController::class, 'get_order_status']);
        Route::post('save', [ApiOrdersController::class, 'save_order']);
        Route::post('process-from-cart', [ApiOrdersController::class, 'process_order_from_cart']);
        Route::post('complete', [ApiOrdersController::class, 'complete_order']);
        Route::post('pay-with-card', [ApiStripePaymentController::class, 'payWithStripeCard']);
        Route::post('{orderId}/process-payment', [ApiStripePaymentController::class, 'processOrderPayment'])->where('orderId', '[A-Za-z0-9\-_]+');
        Route::post('{orderId}/complete-payment', [ApiStripePaymentController::class, 'completeOrderPayment'])->where('orderId', '[A-Za-z0-9\-_]+');
        Route::get('{orderId}/invoice', [ApiInvoiceController::class, 'getOrderInvoice']);
        Route::get('{orderId}/invoice/download', [ApiInvoiceController::class, 'downloadInvoice'])->name('api.customer.orders.invoice.download');
        Route::post('{orderId}/invoice/email', [ApiInvoiceController::class, 'emailInvoice']);
        Route::get('{orderId}/invoice/metadata', [ApiInvoiceController::class, 'getInvoiceMetadata']);
        Route::post('{orderId}/invoice/view', [ApiInvoiceController::class, 'markInvoiceAsViewed']);
        Route::post('{orderId}/cancel', [ApiOrdersController::class, 'cancelOrder']);
        Route::get('{orderId}/return-eligible', [ApiOrdersController::class, 'getReturnableItems']);
        Route::post('{orderId}/reorder', [ApiOrdersController::class, 'reorderItems']);
        Route::get('{orderId}/track', [ApiOrdersController::class, 'trackOrder']);
        Route::get('{orderId}', [ApiOrdersController::class, 'get_order_details']);
        Route::get('{orderId}/status', [ApiOrdersController::class, 'get_order_status']);
            Route::get('{orderId}/cancellable', [ApiOrdersController::class, 'isOrderCancellable']);
    Route::get('/cancellation-reasons', [ApiOrdersController::class, 'getCancellationReasons']);

    // Order messages routes
        Route::get('{orderId}/messages', [ApiOrderMessagesController::class, 'getMessages']);
        Route::post('/messages/send', [ApiOrderMessagesController::class, 'sendMessage']);
        Route::post('{orderId}/messages/read', [ApiOrderMessagesController::class, 'markAsRead']);
        Route::get('/messages/unread-count', [ApiOrderMessagesController::class, 'getUnreadCount']);

        Route::get('{orderId}/return-methods', [ApiReturnController::class, 'getReturnMethods']);
        Route::get('{orderId}/refund-methods', [ApiRefundController::class, 'getRefundMethods']);
        Route::get('{orderId}/return-eligibility', [ApiReturnController::class, 'checkReturnEligibility']);
        Route::get('{orderId}/returned-items', [ApiReturnController::class, 'getOrderReturnedItems']);
        Route::get('{orderId}/refund-eligibility', [ApiRefundController::class, 'checkRefundEligibility']);
        Route::post('{orderId}/return', [ApiReturnController::class, 'submitReturnRequest']);
        // Route::post('{orderId}/return', [ApiReturnController::class, 'save_order_return_request']);
        Route::post('{orderId}/refund', [ApiRefundController::class, 'save_order_return_request']);
        Route::post('/return', [ApiReturnController::class, 'test_return_request']);
        Route::post('/debug-request', [ApiReturnController::class, 'debug_request']);
        Route::get('/thank-you/{orderCode}', [ApiOrdersController::class, 'getOrderForThankYou']);
        Route::post('/process-from-cart', [ApiOrdersController::class, 'process_order_from_cart']);
    });

    // Cancel Requests Routes
    Route::group(['prefix' => 'cancel-requests', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/', [ApiCancelRequestController::class, 'index']);
        Route::get('/stats', [ApiCancelRequestController::class, 'getStats']);
        Route::get('/{id}', [ApiCancelRequestController::class, 'show']);
        Route::post('/{id}/process', [ApiCancelRequestController::class, 'process']);
    });

    // Invoices API
    Route::group(['prefix' => 'invoices', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/list', [ApiInvoiceController::class, 'getInvoiceList']);
        Route::get('/unpaid', [ApiInvoiceController::class, 'getUnpaidInvoices']);
        Route::get('/recent', [ApiInvoiceController::class, 'getRecentInvoices']);
        Route::get('/search', [ApiInvoiceController::class, 'searchInvoices']);
    });

    Route::group(['prefix' => 'addresses', 'middleware' => ['app_language']], function () {
        Route::post('add-city', [ApiAddressController::class, 'addCity']);
        Route::get('cities', [ApiAddressController::class, 'getCities']);
        Route::get('states', [ApiAddressController::class, 'getStates']);
        Route::get('states/{country}', [ApiAddressController::class, 'getStates']);
        Route::group(['middleware' => ['api.auth']], function () {
            Route::get('countries', [ApiAddressController::class, 'getCountries']);
            Route::get('states-by-country/{country_id}', [ApiAddressController::class, 'getStatesByCountry']);
            Route::get('cities-by-state/{state_id}', [ApiAddressController::class, 'getCitiesByState']);
            Route::get('list', [ApiAddressController::class, 'shipping_address_list']);
            Route::post('save', [ApiAddressController::class, 'createShippingAddress']);
            Route::put('{id}/update', [ApiAddressController::class, 'updateShippingAddress']);
            Route::delete('{id}/delete', [ApiAddressController::class, 'deleteShippingAddress']);
            Route::put('{id}/default', [ApiAddressController::class, 'set_default']);
        });
    });
    Route::group(['prefix' => 'categories'], function () {
        Route::get('/', [ApiCategoryController::class, 'index']);
        Route::get('/details', [ApiCategoryController::class, 'details']);
        Route::get('/tree', [ApiCategoryController::class, 'categories_tree']);
        Route::get('/{category}/brands', [ApiBrandController::class, 'getBrandsByCategory']);
        Route::get('/{category}/breadcrumbs', [ApiCategoryController::class, 'getCategoryBreadcrumbs']);
        Route::get('/{category}/filters', [ApiCategoryController::class, 'getCategoryFilters']);
        Route::get('/{category}/subcategories', [ApiCategoryController::class, 'getSubcategories']);
        Route::get('/sections', [ApiCategoryController::class, 'getCategorySections']);
    });
    Route::group(['prefix' => 'brands', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiBrandController::class, 'index']);
        Route::get('/details/{brandSlug}', [ApiBrandController::class, 'details']);
        Route::get('/{categorySlug}', [ApiBrandController::class, 'get_category_by_brands']);
        Route::get('/categories/all', [ApiBrandController::class, 'get_brand_categories']);
        Route::get('/featured', [ApiBrandController::class, 'get_featured_brands']);
        Route::get('/popular', [ApiBrandController::class, 'getPopularBrands']);
    });
    Route::group(['prefix' => 'products', 'middleware' => ['app_language', 'optional.auth']], function () {
        Route::get('/', [ApiProductController::class, 'index']);

        // Get product colors
        Route::get('{id}/colors', [ApiProductVariantsController::class, 'getProductColors']);

        // Get product packages
        Route::get('{id}/packages', [ApiProductVariantsController::class, 'getProductPackages']);

        // Get product discounts
        Route::get('{id}/discounts', [ApiProductVariantsController::class, 'getProductDiscounts']);

        // Get frequently bought together products
        Route::get('{id}/recommendations/frequently-bought-together', [ApiProductVariantsController::class, 'getFrequentlyBoughtTogether']);

        // Existing routes
        Route::get('/recommendations', [ApiProductController::class, 'getRecommendations']);
        Route::get('/recommendations/related', [ApiProductController::class, 'getRelatedRecommendations']);
        Route::get('/trending', [ApiProductController::class, 'getTrendingProducts']);
        Route::get('/new-arrivals', [ApiProductController::class, 'getNewArrivals']);
        Route::get('/best-sellers', [ApiProductController::class, 'getBestSellers']);
        Route::get('/premium', [ApiProductController::class, 'getPremiumOr_highMargin']);
        Route::get('/on-sale', [ApiProductController::class, 'getOnSaleProducts']);
        Route::get('/deal-of-the-day', [ApiProductController::class, 'getDealOfTheDay']);
        Route::get('/featured', [ApiProductController::class, 'getFeaturedProducts']);
        Route::get('/details', [ApiProductController::class, 'get_product_details']);
        Route::get('/specifications', [ApiProductController::class, 'getProductSpecifications']);
        Route::get('/reviews', [ApiProductController::class, 'getProductReviews']);
        Route::get('/{id}/enhanced-details', [ApiProductController::class, 'getEnhancedDetails']);
        Route::get('/{id}/faqs', [ApiProductController::class, 'getProductFaqs']);
        Route::get('/{id}/related', [ApiProductController::class, 'getRelatedProducts']);
        Route::get('/{id}/related-products-id', [ApiProductController::class, 'getRelatedProductsId']);
        Route::get('/{id}/similar', [ApiProductController::class, 'getSimilarProducts']);
        Route::get('/{id}/stock-status', [ApiProductController::class, 'getStockStatus']);
        Route::get('/{id}/availability', [ApiProductController::class, 'getAvailability']);
        Route::get('/{id}/reviews-summary', [ApiProductController::class, 'getReviewsSummary']);
        Route::get('/{id}/reviews', [ApiProductController::class, 'getReviews']);
        Route::get('/{id}/reviews/summary', [ApiProductController::class, 'getReviewsSummary']);
        Route::get('/{id}/reviews/count', [ApiProductController::class, 'getReviewsCount']);
        Route::get('/{id}/reviews/average-rating', [ApiProductController::class, 'getAverageRating']);
        Route::get('/{id}/reviews/rating-distribution', [ApiProductController::class, 'getRatingDistribution']);
        Route::get('/{id}/quick-view', [ApiProductController::class, 'getQuickView']);
        Route::group(['middleware' => ['api.auth']], function () {
            Route::get('/top-tier-products', [ApiProductController::class, 'getPremiumOr_highMargin']);
            Route::get('/early-access-products', [ApiProductController::class, 'early_access_products']);
            Route::post('/{id}/reviews', [ApiProductController::class, 'submitProductReview']);
        });
    });

    // Public Support Ticket routes (no authentication required)
    Route::group(['prefix' => 'support-tickets', 'middleware' => ['app_language']], function () {
        Route::get('categories', [ApiSupportTicketController::class, 'getCategories']);
    });

    // Support Ticket routes (authentication required)
    Route::group(['prefix' => 'support-tickets', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/', [ApiSupportTicketController::class, 'getUserTickets']);
        Route::get('/{id}', [ApiSupportTicketController::class, 'getTicket']);
        Route::post('/{id}/messages', [ApiSupportTicketController::class, 'addMessage']);
        Route::put('/{id}/status', [ApiSupportTicketController::class, 'updateStatus']);
        // Tickets
        Route::get('/', [ApiSupportTicketController::class, 'getTickets']);
        Route::get('open', [ApiSupportTicketController::class, 'getOpenTickets']);
        Route::get('open/count', [ApiSupportTicketController::class, 'getOpenTicketsCount']);
        Route::get('search', [ApiSupportTicketController::class, 'searchTickets']);
        Route::get('statistics', [ApiSupportTicketController::class, 'getTicketStatistics']);
        Route::post('/', [ApiSupportTicketController::class, 'createTicket']);
        // Individual ticket operations
        Route::get('{id}', [ApiSupportTicketController::class, 'getTicket']);
        Route::put('{id}', [ApiSupportTicketController::class, 'updateTicket']);
        Route::post('{id}/close', [ApiSupportTicketController::class, 'closeTicket']);
        Route::post('{id}/reopen', [ApiSupportTicketController::class, 'reopenTicket']);

        // Messages
        Route::get('{id}/messages', [ApiSupportTicketController::class, 'getMessages']);
        Route::post('{id}/messages', [ApiSupportTicketController::class, 'addMessage']);
        Route::put('{id}/messages/{messageId}', [ApiSupportTicketController::class, 'updateMessage']);
        Route::delete('{id}/messages/{messageId}', [ApiSupportTicketController::class, 'deleteMessage']);
        Route::post('{id}/messages/{messageId}/read', [ApiSupportTicketController::class, 'markMessageAsRead']);

        // Attachments
        Route::get('{id}/attachments', [ApiSupportTicketController::class, 'getAttachments']);
        Route::post('{id}/attachments', [ApiSupportTicketController::class, 'uploadAttachment']);
        Route::get('attachments/{attachmentId}/download', [ApiSupportTicketController::class, 'downloadAttachment']);
    });

    Route::group(['prefix' => 'compare-products', 'middleware' => ['api.auth']], function () {
        Route::post('/', [ApiCompareProductController::class, 'addToCompare']);
        Route::get('/', [ApiCompareProductController::class, 'getCompareList']);
        Route::delete('/{slug}', [ApiCompareProductController::class, 'removeFromCompare']);
        Route::delete('/', [ApiCompareProductController::class, 'clearCompareList']);
        Route::get('/attributes', [ApiCompareProductController::class, 'getCompareAttributes']);
        Route::get('/similar-products', [ApiCompareProductController::class, 'getSimilarProductsForComparison']);
        Route::post('/share', [ApiCompareProductController::class, 'shareComparisonList']);
    });

    // Shipping methods routes
    Route::group(['prefix' => 'shipping', 'middleware' => ['api.auth']], function () {
        Route::get('methods', [ApiShippingMethodController::class, 'getAvailableShippingMethods']);
        Route::post('calculate', [ApiShippingMethodController::class, 'calculateShippingCost']);
        Route::post('validate-address', [ApiShippingMethodController::class, 'validateShippingAddress']);
        Route::get('tracking/{orderId}', [ApiShippingMethodController::class, 'getOrderTracking']);
        Route::get('rates/{addressId}', [ApiShippingMethodController::class, 'getShippingRates']);
        Route::post('delivery-availability', [ApiShippingMethodController::class, 'checkDeliveryAvailability']);
        Route::post('select', [ApiShippingMethodController::class, 'selectShippingMethod']);
    });

    // Public routes for shared comparison lists (no auth required)
    Route::get('compare/share/{shareId}', [ApiCompareProductController::class, 'getSharedComparisonList']);

    // Ratings routes
    Route::group(['prefix' => 'ratings', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::delete('{id}', [ApiRatingReviewController::class, 'delete_review']);
    });

    Route::group(['prefix' => 'product', 'middleware' => ['app_language']], function () {
        Route::get('details', [ApiProductController::class, 'get_product_details']);
        Route::get('description', [ApiProductController::class, 'get_product_description']);
        Route::get('specifications', [ApiProductController::class, 'get_product_specifications']);
        Route::get('ratings', [ApiRatingReviewController::class, 'get_product_ratings']);
        Route::get('reviews', [ApiRatingReviewController::class, 'get_product_reviews']);
        Route::get('{id}/similar', [ApiProductController::class, 'getSimilarProducts']);
        Route::group(['middleware' => ['api.auth']], function () {
            Route::post('write_review_rating', [ApiRatingReviewController::class, 'write_review_rating']);
        });
    });


    Route::group(['prefix' => 'stock-requests', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('list', [ApiStockRequestController::class, 'getUserStockRequests']);
        Route::get('{id}/show', [ApiStockRequestController::class, 'getUserStockRequestDetails']);
        Route::put('{id}/update', [ApiStockRequestController::class, 'updateUserStockRequestDetails']);

        Route::post('update-status', [ApiStockRequestController::class, 'updateStockRequestStatus']);
        Route::get('stats', [ApiStockRequestController::class, 'getStockRequestStats']);
        Route::post('save', [ApiStockRequestController::class, 'createStockRequest']);
        Route::delete('{id}/delete', [ApiStockRequestController::class, 'removeStockRequest']);
        Route::delete('clear-all', [ApiStockRequestController::class, 'removeAllStockRequest']);
        Route::get('check', [ApiStockRequestController::class, 'chackProductStockRequest']);
    });
    Route::group(['prefix' => 'wishlist', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('wishlist-by-user', [WishlistController::class, 'index']);
        Route::post('add', [WishlistController::class, 'store']);
        Route::post('delete-from-wishlist', [WishlistController::class, 'delete']);
        Route::get('check-product-in-wishlist', [WishlistController::class, 'checkProductInWishlist']);
        Route::delete('clear-wishlist', [WishlistController::class, 'deleteAll']);
        Route::get('stats', [WishlistController::class, 'getStats']);
        Route::post('move-to-cart', [WishlistController::class, 'moveToCart']);
    });


    Route::group(['prefix' => 'search'], function () {
        Route::get('/suggestions', [ApiSearchController::class, 'suggestions']);
        Route::get('/popular', [ApiSearchController::class, 'popular']);
        Route::get('/products', [ApiSearchController::class, 'search_products']);
        Route::get('products/search', [ApiSearchController::class, 'index']);
        Route::get('/', [ApiSearchController::class, 'search']);
    });


    // Size API
    Route::group(['prefix' => 'sizes', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiSizeController::class, 'index']);
        Route::get('/size-charts', [ApiSizeController::class, 'getAllSizeCharts']);
        Route::get('/size-charts/category/{categoryId}', [ApiSizeController::class, 'getSizeChartByCategory']);
        Route::get('/size-charts/product/{productId}', [ApiSizeController::class, 'getSizeChartByProduct']);

        // Admin routes for managing size charts
        Route::group(['middleware' => ['api.auth', 'role:admin']], function () {
            Route::post('/size-charts', [ApiSizeController::class, 'storeSizeChart']);
            Route::put('/size-charts/{id}', [ApiSizeController::class, 'updateSizeChart']);
            Route::delete('/size-charts/{id}', [ApiSizeController::class, 'deleteSizeChart']);
        });
    });

    // Tags API routes
    Route::group(['prefix' => 'tags', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiTagsController::class, 'index']);
        Route::get('/popular', [ApiTagsController::class, 'getPopularTags']);
        Route::get('/trending', [ApiTagsController::class, 'getTrendingTags']);
        Route::get('/search', [ApiTagsController::class, 'searchTags']);
        Route::get('/{slug}', [ApiTagsController::class, 'getTagBySlug']);
        Route::get('/{slug}/related', [ApiTagsController::class, 'getRelatedTags']);
        Route::get('/{slug}/products', [ApiTagsController::class, 'getProductsByTag']);
    });

    Route::group(['prefix' => 'return-request', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('{id}/show', [ApiReturnController::class, 'return_request_details']);
        Route::put('{id}/cancel', [ApiReturnController::class, 'return_request_cancel']);
        Route::post('{id}/update', [ApiReturnController::class, 'return_request_update']);
        Route::get('confirmation', [ApiReturnRefundController::class, 'return_configuration']);
        Route::get('faq', [ApiReturnRefundController::class, 'return_faq']);
        Route::get('list', [ApiReturnRefundController::class, 'index']);
        Route::get('/{return_code}', [ApiReturnRefundController::class, 'return_request_details']);
        Route::get('/{id}/shipping-label', [ApiReturnController::class, 'getShippingLabel']);
        Route::post('/{id}/reschedule-pickup', [ApiReturnController::class, 'reschedulePickup']);
        Route::post('/{id}/feedback', [ApiReturnController::class, 'submitFeedback']);
        Route::post('/{id}/submit', [ApiReturnController::class, 'submitReturnRequest']);
    });
    Route::group(['prefix' => 'refund-request', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('list', [ApiRefundController::class, 'index']);
        Route::get('{id}/show', [ApiRefundController::class, 'refund_request_details']);
        Route::post('{id}/update', [ApiRefundController::class, 'refund_request_update']);
        Route::put('{id}/cancel', [ApiRefundController::class, 'refund_request_cancel']);
        Route::post('/{id}/feedback', [ApiRefundController::class, 'submitFeedback']);
    });

    // Return Methods


    Route::group(['prefix' => 'payment-methods', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('available', [ApiPaymentMethodController::class, 'get_user_available_cards']);
        Route::get('list', [ApiPaymentMethodController::class, 'get_user_cards']);
        Route::post('create-setup-intent', [ApiPaymentMethodController::class, 'create_setup_intent']);
        Route::post('store', [ApiPaymentMethodController::class, 'store_card']);
        Route::get('{stripe_card_id}', [ApiPaymentMethodController::class, 'get_card']);
        Route::put('{stripe_card_id}/update', [ApiPaymentMethodController::class, 'update_card']);
        Route::delete('{stripe_card_id}/delete', [ApiPaymentMethodController::class, 'remove_card']);
        Route::patch('{stripe_card_id}/set-default', [ApiPaymentMethodController::class, 'set_default_card']);
        Route::post('{stripe_card_id}/process-payment', [ApiPaymentMethodController::class, 'process_payment']);
    });

    // Stripe Payment Return URL Routes (public - no auth required for redirects)
    Route::group(['prefix' => 'stripe/payment'], function () {
        Route::get('success', [ApiPaymentMethodController::class, 'payment_success']);
        Route::get('cancel', [ApiPaymentMethodController::class, 'payment_cancel']);
    });

    Route::group(['prefix' => 'notifications', 'middleware' => ['api.auth']], function () {
        // Basic notification operations
        Route::get('/', [App\Http\Controllers\Api\V3\NotificationController::class, 'index']);
        Route::get('/list', [App\Http\Controllers\Api\V3\NotificationController::class, 'index']); // Keep backward compatibility
        Route::get('/recent', [App\Http\Controllers\Api\V3\NotificationController::class, 'recent']);
        Route::get('/count', [App\Http\Controllers\Api\V3\NotificationController::class, 'unreadCount']);
        Route::get('/unread-count', [App\Http\Controllers\Api\V3\NotificationController::class, 'unreadCount']); // Alternative endpoint
        Route::get('/stats', [App\Http\Controllers\Api\V3\NotificationController::class, 'stats']);
        Route::get('/debug-sync', [App\Http\Controllers\Api\V3\NotificationController::class, 'debugSyncIssue']); // Debug endpoint

        // Enhanced notification endpoints
        Route::get('/user', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'getUserNotifications']);
        Route::get('/user-unread-count', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'getUnreadCount']);
        Route::post('/mark-read', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'markAsRead']);
        Route::post('/mark-all-read', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'markAllAsRead']);
        Route::delete('/delete', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'deleteNotification']);
        Route::get('/user-preferences', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'getPreferences']);
        Route::put('/user-preferences', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'updatePreferences']);

        // Frontend trigger endpoints for real-time notifications
        Route::post('/trigger', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'triggerNotification']);
        Route::post('/send-admin', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'sendAdminNotification']);
        Route::get('/admin-bell-data', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'getAdminBellData']);
        Route::post('/send-test', [App\Http\Controllers\Api\V3\ApiNotificationController::class, 'sendTestNotification']);

        // Individual notification operations
        Route::get('/{id}/show', [App\Http\Controllers\Api\V3\NotificationController::class, 'show']); // Keep for backward compatibility
        Route::patch('/{id}/read', [App\Http\Controllers\Api\V3\NotificationController::class, 'markAsRead']);
        Route::patch('/{id}/mark-read', [App\Http\Controllers\Api\V3\NotificationController::class, 'markAsRead']); // Alternative endpoint
        Route::patch('/{id}/unread', [App\Http\Controllers\Api\V3\NotificationController::class, 'markAsUnread']);
        Route::patch('/{id}/mark-unread', [App\Http\Controllers\Api\V3\NotificationController::class, 'markAsUnread']); // Alternative endpoint
        Route::delete('/{id}', [App\Http\Controllers\Api\V3\NotificationController::class, 'destroy']);
        Route::delete('/{id}/delete', [App\Http\Controllers\Api\V3\NotificationController::class, 'destroy']); // Keep for backward compatibility

        // Bulk operations
        Route::post('/mark-all-as-read', [App\Http\Controllers\Api\V3\NotificationController::class, 'markAllAsRead']); // Alternative endpoint
        Route::delete('/clear-read', [App\Http\Controllers\Api\V3\NotificationController::class, 'clearRead']);

        // Notification preferences
        Route::get('/preferences', [App\Http\Controllers\Api\V3\NotificationController::class, 'getPreferences']);
        Route::patch('/preferences', [App\Http\Controllers\Api\V3\NotificationController::class, 'updatePreferences']);
    });

    // Customer specific endpoints
    Route::group(['prefix' => 'customer', 'middleware' => ['api.auth']], function () {
        // Customer analytics routes
        Route::prefix('analytics')->group(function () {
            Route::get('/{timeframe}', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getAnalytics']);
            Route::get('/', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getAnalytics']);
            Route::get('/activity', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getActivity']);
            Route::get('/spending-trends', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getSpendingTrend']);
        });

        // Orders routes
        Route::prefix('orders')->group(function () {
            Route::post('{orderId}/resume-checkout', [ApiOrdersController::class, 'resumeCheckout']);
        });

        // Direct routes to match frontend requests
        Route::get('/spending-trend', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getSpendingTrend']);
        Route::get('/activity', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getActivity']);
    });

    // Bulk Order API
    Route::group(['prefix' => 'bulk-orders', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('products', [ApiOrdersController::class, 'bulk_order_products']);
        Route::get('product/{slug}', [ApiOrdersController::class, 'bulk_order_product_details']);
        Route::post('save', [ApiOrdersController::class, 'bulk_order_save']);
    });

    // Coupons API
    Route::group(['prefix' => 'coupons', 'middleware' => ['app_language']], function () {
        Route::group(['middleware' => ['api.auth']], function () {
            Route::get('/personalized', [ApiCouponController::class, 'personalized']);
        });
        Route::get('/', [ApiCouponController::class, 'index']);
        Route::get('/featured', [ApiCouponController::class, 'featured']);
        Route::get('/expiring-soon', [ApiCouponController::class, 'expiringSoon']);
        Route::get('/categories', [ApiCouponController::class, 'categories']);
        Route::get('/stats', [ApiCouponController::class, 'stats']);
        Route::get('/usage-report', [ApiCouponController::class, 'usageReport']);
        Route::post('/validate', [ApiCouponController::class, 'validateCoupon']);
        Route::get('/{id}', [ApiCouponController::class, 'show']);
        Route::get('/code/{code}', [ApiCouponController::class, 'getByCode']);
        Route::post('/apply', [ApiCouponController::class, 'applyCoupon']);
        Route::post('/remove', [ApiCouponController::class, 'removeCoupon']);
    });

    // Promotions API routes
    Route::group(['prefix' => 'promotions', 'middleware' => ['app_language']], function () {
        // Coupons
        Route::prefix('coupons')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiPromotionController::class, 'getCoupons']);
            Route::get('/{code}', [App\Http\Controllers\Api\V3\Promotions\ApiPromotionController::class, 'getCouponByCode']);
            Route::post('/apply', [App\Http\Controllers\Api\V3\Promotions\ApiPromotionController::class, 'applyCoupon']);
            Route::post('/remove', [App\Http\Controllers\Api\V3\Promotions\ApiPromotionController::class, 'removeCoupon']);
            Route::post('/validate', [App\Http\Controllers\Api\V3\Promotions\ApiPromotionController::class, 'validateCoupon']);
        });

        // Category Banners
        Route::prefix('category-banners')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'getActiveCategoryBanners']);
            Route::get('/category/{categoryId}', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'getCategoryBannersByCategory']);
            Route::get('/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'getCategoryBanner']);

            // Admin routes (protected by admin middleware)
            Route::group(['middleware' => ['api.auth', 'admin']], function () {
                Route::get('/admin/list', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminGetAllCategoryBanners']);
                Route::post('/admin/create', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminCreateCategoryBanner']);
                Route::put('/admin/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminUpdateCategoryBanner']);
                Route::delete('/admin/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminDeleteCategoryBanner']);
                Route::patch('/admin/{id}/status', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminUpdateBannerStatus']);
                Route::post('/admin/update-order', [App\Http\Controllers\Api\V3\Promotions\ApiCategoryBannerController::class, 'adminUpdateBannersOrder']);
            });
        });

        // Discounts
        Route::prefix('discounts')->group(function () {
            Route::get('/active', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getActiveDiscounts']);
            Route::get('/product/{productId}', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getProductDiscounts']);
            Route::get('/category/{categoryId}', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getCategoryDiscounts']);
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'index']);
            Route::get('/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'show']);
            Route::get('/stats', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getStats']);
            Route::get('/featured', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getFeatured']);
            Route::post('/validate', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'validateDiscount']);
            Route::post('/apply', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'applyDiscount']);
        });

        // Rewards
        Route::prefix('rewards')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiRewardController::class, 'getCustomerRewards']);
            Route::get('/history', [App\Http\Controllers\Api\V3\Promotions\ApiRewardController::class, 'getRewardHistory']);
            Route::post('/redeem', [App\Http\Controllers\Api\V3\Promotions\ApiRewardController::class, 'redeemReward']);
            Route::get('/programs', [App\Http\Controllers\Api\V3\Promotions\ApiRewardController::class, 'getRewardPrograms']);
            Route::get('/options', [App\Http\Controllers\Api\V3\Promotions\ApiRewardController::class, 'getRewardOptions']);
        });

        // Special Offers
        Route::prefix('special-offers')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiSpecialOfferController::class, 'index']);
            Route::get('/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiSpecialOfferController::class, 'show']);
            Route::post('/{id}/claim', [App\Http\Controllers\Api\V3\Promotions\ApiSpecialOfferController::class, 'claimOffer']);
        });

        // Volume Discounts
        Route::get('/volume-discounts/{productId}', [App\Http\Controllers\Api\V3\Promotions\ApiDiscountController::class, 'getVolumeDiscounts']);

        // Referral Program
        Route::prefix('referral')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\V3\Promotions\ApiReferralController::class, 'getReferralProgram']);
            Route::post('/create', [App\Http\Controllers\Api\V3\Promotions\ApiReferralController::class, 'createReferral']);
            Route::get('/history', [App\Http\Controllers\Api\V3\Promotions\ApiReferralController::class, 'getReferralHistory']);
            Route::get('/validate/{code}', [App\Http\Controllers\Api\V3\Promotions\ApiReferralController::class, 'validateReferralCode']);
        });
    });

    // Offers API
    Route::group(['prefix' => 'offers', 'middleware' => ['app_language']], function () {
        Route::get('/personalized', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getAllOffers']);
        Route::get('/type/{type}', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getOffersByType']);
        Route::get('/top-priority', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getTopPriorityOffers']);
        Route::get('/exclusive', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getExclusiveOffers']);
        Route::get('/stats', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getOfferStats']);
        Route::get('/saved', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getSavedOffers'])->middleware('api.auth');
        Route::post('/save', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'saveOffer'])->middleware('api.auth');
        Route::post('/unsave', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'unsaveOffer'])->middleware('api.auth');
        Route::get('/saved', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getSavedOffers'])->middleware('api.auth');
        Route::get('/{id}/check-validity', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'checkOfferValidity']);
        Route::post('/apply', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'applyOffer']);
        Route::get('/stats', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getOfferStats']);
    });

    // Customer Offers API - Uses offers table
    Route::group(['prefix' => 'customer/offers', 'middleware' => ['app_language']], function () {
        Route::get('/personalized', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getPersonalizedOffers']);
        Route::get('/top-priority', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getTopPriorityOffers']);
        Route::get('/exclusive', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getExclusiveOffers']);
        Route::get('/filter/{filter}', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getOffersByFilter']);
        Route::get('/expiring', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getExpiringOffers']);
        Route::get('/search', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'searchOffers']);
        Route::get('/stats', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getOfferStats']);
        Route::get('/seasonal/active', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getSeasonalActive']);
        Route::get('/seasonal', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getSeasonalOffers']);
        Route::get('/{id}', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getOfferById']);

        Route::post('/apply', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'applyOffer']);
        Route::post('/use', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'useOffer']);
        Route::get('/applied', [App\Http\Controllers\Api\V3\Customer\ApiCustomerOfferController::class, 'getUserAppliedOffers']);

    });

    // Seasonal Campaigns API
    Route::group(['prefix' => 'seasonal-campaigns', 'middleware' => ['app_language']], function () {
        Route::get('/active', [App\Http\Controllers\Api\V3\Promotions\ApiSeasonalCampaignController::class, 'getActiveSeasonalCampaigns']);
        Route::get('/upcoming', [App\Http\Controllers\Api\V3\Promotions\ApiSeasonalCampaignController::class, 'getUpcomingSeasonalCampaigns']);
        Route::get('/{id}', [App\Http\Controllers\Api\V3\Promotions\ApiSeasonalCampaignController::class, 'getSeasonalCampaignById']);
        Route::get('/type/{type}', [App\Http\Controllers\Api\V3\Promotions\ApiSeasonalCampaignController::class, 'getSeasonalCampaignByType']);
    });

    // Product-related offers
    Route::get('/products/{productId}/offers', [App\Http\Controllers\Api\V3\Promotions\ApiPersonalizedOfferController::class, 'getOffersForProduct']);

    // Dropshipper Offers API
    Route::group(['prefix' => 'dropshipper-offers', 'middleware' => ['app_language']], function () {
        // Public routes
        Route::get('/', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'index']);
        Route::get('/filter', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'getByFilter']);
        Route::get('/top-priority', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'getTopPriority']);
        Route::get('/exclusive', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'getExclusive']);
        Route::get('/expiring', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'getExpiring']);
        Route::get('/search', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'search']);
        Route::get('/{id}', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'show']);

        // Protected routes (require authentication)
        Route::group(['middleware' => ['api.auth']], function () {
            Route::post('/apply-promo-code', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'applyPromoCode']);

            // Admin routes (these should have additional middleware in production)
            Route::post('/', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'store']);
            Route::put('/{id}', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'update']);
            Route::delete('/{id}', [App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperOfferController::class, 'destroy']);
        });
    });

    // Wallet routes
    Route::group(['prefix' => '/wallet', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('details', [ApiWalletController::class, 'getWalletDetails']);
        Route::get('transactions', [ApiWalletController::class, 'getTransactions']);
        Route::get('exchange-rates', [ApiWalletController::class, 'getExchangeRates']);
        Route::get('reward-points-history', [ApiWalletController::class, 'getRewardPointsHistory']);
        Route::post('convert-points', [ApiWalletController::class, 'convertPointsToCurrency']);
        Route::post('add-funds', [ApiWalletController::class, 'addFunds']);
        Route::post('deduct-funds', [ApiWalletController::class, 'deductFunds']);
        Route::get('/', [ApiWalletController::class, 'getWalletActivities']);
        Route::get('balance', [ApiWalletController::class, 'getWalletBalance']);
        Route::get('rewards/levels/progress', [ApiWalletController::class, 'getRewardLevelProgress']);
        Route::get('rewards/redemption-options', [ApiWalletController::class, 'getRewardRedemptionOptions']);
        Route::get('rewards/redemption-history', [ApiWalletController::class, 'getRedemptionHistory']);
        Route::post('rewards/redeem', [ApiWalletController::class, 'redeemRewardPoints']);
        Route::post('rewards/apply-to-checkout', [ApiWalletController::class, 'applyRewardPointsToCheckout']);
        Route::post('apply-to-checkout', [ApiWalletController::class, 'applyWalletToCheckout']);
    });

    // Admin wallet routes
    Route::group(['prefix' => 'admin/wallet', 'middleware' => ['app_language', 'api.auth', 'admin']], function () {
        Route::get('details', [ApiAdminWalletController::class, 'getWalletDetails']);
        Route::get('transactions', [ApiAdminWalletController::class, 'getTransactions']);
        Route::get('all-wallets', [ApiAdminWalletController::class, 'getAllWallets']);
        Route::post('add-funds', [ApiAdminWalletController::class, 'addFunds']);
        Route::post('deduct-funds', [ApiAdminWalletController::class, 'deductFunds']);
        Route::post('exchange-rates', [ApiAdminWalletController::class, 'manageExchangeRates']);
        Route::post('transaction-approval', [ApiAdminWalletController::class, 'manageTransactionApproval']);
        Route::get('stats', [ApiAdminWalletController::class, 'getWalletStats']);
    });

    // User Preferences Routes
    Route::middleware('api.auth')->group(function () {
        Route::get('/user/preferences', [UserPreferencesController::class, 'index']);
        Route::patch('/user/preferences', [UserPreferencesController::class, 'update']);

        // Theme specific routes
        Route::get('/user/preferences/theme', [UserPreferencesController::class, 'getTheme']);
        Route::patch('/user/preferences/theme', [UserPreferencesController::class, 'updateTheme']);

        // Notification specific routes
        Route::get('/user/preferences/notifications', [UserPreferencesController::class, 'getNotificationPreferences']);
        Route::patch('/user/preferences/notifications', [UserPreferencesController::class, 'updateNotificationPreferences']);
    });

    Route::group(['prefix' => 'bundle-products', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiBundleProductController::class, 'index']);
        Route::get('/popular', [ApiBundleProductController::class, 'getPopularBundleProducts']);
        Route::get('/category/{categorySlug}', [ApiBundleProductController::class, 'getBundleProductsByCategory']);
        Route::get('/details/{idOrSlug}', [ApiBundleProductController::class, 'getBundleProductDetails']);

        // Admin routes (require authentication and admin role)
        Route::group(['middleware' => ['api.auth', 'role:admin']], function () {
            Route::post('/', [ApiBundleProductController::class, 'store']);
            Route::put('/{id}', [ApiBundleProductController::class, 'update']);
            Route::delete('/{id}', [ApiBundleProductController::class, 'destroy']);
        });
    });

    // Bulk order and dropshipper routes
    Route::group(['prefix' => 'bulk-order', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::post('/place-order', [ApiOrdersController::class, 'placeBulkPurchaseRequest']);
        Route::get('/products', [ApiProductController::class, 'getProductsForBulkOrder']);
        Route::get('/products/{slug}', [ApiProductController::class, 'getProductForBulkOrder']);
        Route::post('/save', [ApiOrdersController::class, 'saveBulkOrder']);
    });

    Route::group(['prefix' => 'dropshipper/bulk-order', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('cart', [ApiCartController::class, 'getBulkOrderCart']);
        Route::delete('cart', [ApiCartController::class, 'clearBulkOrderCart']);
        Route::post('cart/item/{productId}', [ApiCartController::class, 'addToBulkOrderCart']);
        Route::put('cart/item/{productId}', [ApiCartController::class, 'updateBulkOrderCartItem']);
        Route::delete('cart/item/{productId}', [ApiCartController::class, 'removeBulkOrderCartItem']);
        Route::post('checkout', [ApiCartController::class, 'checkoutBulkOrder']);
        Route::post('checkout/payment', [ApiOrdersController::class, 'processBulkOrderPayment']);
        Route::post('complete', [ApiOrdersController::class, 'completeBulkOrder']);
        Route::get('history', [ApiOrdersController::class, 'dropshipper_orders']);
        Route::get('orders/{orderId}', [ApiOrdersController::class, 'get_dropshipper_order_details']);
        Route::get('suggestions', [ApiProductController::class, 'getBulkOrderSuggestions']);
    });

    // Bulk Order direct request route
    Route::post('/bulk-order/place-order', [ApiOrdersController::class, 'placeBulkPurchaseRequest'])
        ->middleware(['app_language', 'api.auth']);

    /*
    |--------------------------------------------------------------------------
    | Wishlist Routes
    |--------------------------------------------------------------------------
    */



    // Account Management API
    Route::group(['prefix' => 'account', 'middleware' => ['app_language', 'api.auth']], function () {
        // Profile Management
        Route::get('profile', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@getProfile');
        Route::post('profile', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@updateProfile');
        Route::post('avatar', 'App\Http\Controllers\Api\V3\users\ApiUserController@uploadProfilePicture');

        // Account Settings
        Route::get('settings', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@getAccountSettings');
        Route::post('settings', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@updateAccountSettings');

        // Account Actions
        Route::post('deactivate', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@deactivateAccount');
        Route::post('reactivate', 'App\Http\Controllers\Api\V3\Account\ApiAccountController@reactivateAccount');
    });

    // Security API
    Route::group(['prefix' => 'security', 'middleware' => ['app_language', 'api.auth']], function () {
        // Security Settings
        Route::get('settings', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@getSecuritySettings');
        Route::post('settings', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@updateSecuritySettings');

        // Password Management
        Route::post('password/change', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@changePassword');
        Route::post('password/strength', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@checkPasswordStrength');

        // Two-Factor Authentication
        Route::post('2fa/generate-secret', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@generate2FASecret');
        Route::post('2fa/enable', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@enable2FA');
        Route::post('2fa/disable', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@disable2FA');

        // Session Management
        Route::get('sessions', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@getActiveSessions');
        Route::post('sessions/revoke', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@revokeSession');
        Route::post('sessions/revoke-all', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@revokeAllSessions');

        // Security Logs
        Route::get('logs', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@getSecurityLogs');

        // Login Attempts
        Route::get('login-attempts', 'App\Http\Controllers\Api\V3\Security\ApiSecurityController@getLoginAttempts');
    });

    // Privacy API
    Route::group(['prefix' => 'privacy', 'middleware' => ['app_language']], function () {
        // Public routes
        Route::get('policies', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@getPrivacyPolicies');
        Route::get('policies/active', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@getActivePrivacyPolicy');

        // Authenticated routes
        Route::group(['middleware' => ['api.auth']], function () {
            // Privacy Settings
            Route::get('settings', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@getPrivacySettings');
            Route::post('settings', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@updatePrivacySettings');

            // Consent Management
            Route::get('consents', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@getConsents');
            Route::post('consents', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@updateConsent');

            // Data Subject Rights
            Route::post('export', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@requestDataExport');
            Route::post('delete', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@requestAccountDeletion');
            Route::post('rectify', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@requestDataRectification');

            // Processing Activities
            Route::get('processing-activities', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@getProcessingActivities');

            // Accept Privacy Policy
            Route::post('policies/accept', 'App\Http\Controllers\Api\V3\Privacy\ApiPrivacyController@acceptPrivacyPolicy');
        });
    });

    // Today's Deals routes
    Route::group(['prefix' => 'deals', 'middleware' => ['app_language']], function () {
        Route::get('/today', [App\Http\Controllers\Api\V3\ApiDealsController::class, 'getTodaysDeals']);
        Route::get('/deal-of-day', [App\Http\Controllers\Api\V3\ApiDealsController::class, 'getDealOfTheDay']);
        Route::get('/category/{category}', [App\Http\Controllers\Api\V3\ApiDealsController::class, 'getDealsByCategory']);
        Route::get('/{id}', [App\Http\Controllers\Api\V3\ApiDealsController::class, 'getDealById']);
    });



    /*
     * Messaging Routes
     */
    Route::group(['prefix' => 'messaging'], function () {
        Route::get('conversations', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@index');
        Route::post('conversations', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@store');
        Route::get('conversations/{id}', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@show');
        Route::get('contacts', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@getContacts');
        Route::get('unread-counts', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@unreadCounts');
        Route::put('messages/mark-read', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@markAsRead');
    });

    /*
    |--------------------------------------------------------------------------
    | Integration Routes
    |--------------------------------------------------------------------------
    */
    Route::prefix('integrations')->middleware(['api.auth'])->group(function () {
        Route::get('/', [ApiIntegrationsController::class, 'getIntegrations']);
        Route::get('/available', [ApiIntegrationsController::class, 'getAvailableIntegrations']);
        Route::post('/connect', [ApiIntegrationsController::class, 'connectIntegration']);
        Route::post('/oauth/callback', [ApiIntegrationsController::class, 'oauthCallback']);
        Route::get('/{id}', [ApiIntegrationsController::class, 'getIntegration']);
        Route::post('/{id}/disconnect', [ApiIntegrationsController::class, 'disconnectIntegration']);
        Route::put('/{id}/settings', [ApiIntegrationsController::class, 'updateIntegrationSettings']);
        Route::post('/{id}/sync', [ApiIntegrationsController::class, 'syncIntegration']);
        Route::get('/{id}/logs', [ApiIntegrationsController::class, 'getIntegrationLogs']);
    });

    /*
    |--------------------------------------------------------------------------
    | Analytics Routes - COMMENTED OUT DUE TO MISSING CONTROLLER
    |--------------------------------------------------------------------------
    */
    /*
    Route::prefix('analytics')->middleware(['api.auth'])->group(function () {
        Route::get('/dashboard', [ApiAnalyticsController::class, 'getDashboard']);
        Route::get('/sales', [ApiAnalyticsController::class, 'getSalesAnalytics']);
        Route::get('/sales/{period}', [ApiAnalyticsController::class, 'getSalesByPeriod']);
        Route::get('/products', [ApiAnalyticsController::class, 'getProductAnalytics']);
        Route::get('/products/top', [ApiAnalyticsController::class, 'getTopProducts']);
        Route::get('/customers', [ApiAnalyticsController::class, 'getCustomerAnalytics']);
        Route::get('/customers/top', [ApiAnalyticsController::class, 'getTopCustomers']);

        // New customer analytics routes
        Route::prefix('customer')->group(function () {
            Route::get('/analytics', [ApiAnalyticsController::class, 'getCustomerAnalyticsData']);
            Route::get('/activity', [ApiAnalyticsController::class, 'getCustomerActivity']);
            Route::get('/spending-trend', [ApiAnalyticsController::class, 'getCustomerSpendingTrend']);
        });
    });
    */

    /*
    |--------------------------------------------------------------------------
    | Seller Routes
    |--------------------------------------------------------------------------
    */
    Route::prefix('seller')->middleware(['api.auth'])->group(function () {
        Route::get('/profile', [ApiSellerController::class, 'getProfile']);
        Route::get('/orders', [ApiSellerController::class, 'getOrders']);
        Route::get('/inventory', [ApiSellerController::class, 'getInventory']);
        Route::get('/analytics', [ApiSellerController::class, 'getAnalytics']);
        Route::get('/customers', [ApiSellerController::class, 'getCustomers']);
        Route::get('/commissions', [ApiSellerController::class, 'getCommissions']);

        // Rating & Review Routes
        Route::get('{sellerId}/reviews', [ApiRatingReviewController::class, 'get_seller_reviews']);
        Route::get('{sellerId}/rating-stats', [ApiRatingReviewController::class, 'get_seller_rating_stats']);
        Route::post('reviews/{reviewId}/respond', [ApiRatingReviewController::class, 'respond_to_review']);
        Route::put('reviews/{reviewId}/respond', [ApiRatingReviewController::class, 'update_review_response']);
        Route::delete('reviews/{reviewId}/respond', [ApiRatingReviewController::class, 'delete_review_response']);
    });

    // Voice Search
    Route::get('/voice-search', [ApiVoiceSearchController::class, 'search']);
    Route::post('/user/voice-search-history', [ApiVoiceSearchController::class, 'saveToHistory'])->middleware('api.auth');
    Route::get('/user/voice-search-history', [ApiVoiceSearchController::class, 'getHistory'])->middleware('api.auth');
    Route::delete('/user/voice-search-history', [ApiVoiceSearchController::class, 'clearHistory'])->middleware('api.auth');

    // FAQ routes
    Route::group(['prefix' => 'faqs', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiFaqController::class, 'index']);
        Route::get('/search', [ApiFaqController::class, 'search']);
        Route::get('/popular', [ApiFaqController::class, 'popular']);
        Route::get('/categories', [ApiFaqController::class, 'categories']);
        Route::get('/{id}', [ApiFaqController::class, 'show']);
        Route::post('/{id}/helpful', [ApiFaqController::class, 'markHelpful']);
        Route::post('/{id}/not-helpful', [ApiFaqController::class, 'markNotHelpful']);
    });

    // Live Chat routes with broadcasting support
    Route::group(['prefix' => 'live-chat', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/messages', [ApiLiveChatController::class, 'getMessages']);
        Route::post('/send', [ApiLiveChatController::class, 'sendMessage']);
        Route::get('/status', [ApiLiveChatController::class, 'getStatus']);
        Route::post('/start', [ApiLiveChatController::class, 'startChat']);
        Route::post('/end', [ApiLiveChatController::class, 'endChat']);
        Route::post('/messages/{messageId}/read', [ApiLiveChatController::class, 'markMessageAsRead']);
        Route::get('/attachment/{attachmentId}/download', [ApiLiveChatController::class, 'downloadAttachment']);
    });

    // Broadcasting authentication for real-time messaging
    Route::post('broadcasting/auth', function (Request $request) {
        return Broadcast::auth($request);
    })->middleware('api.auth');

    Route::prefix('returns')->group(function () {
        Route::get('/faqs', [ReturnFaqController::class, 'index']);
        Route::get('/knowledge-base', [ReturnKnowledgeBaseController::class, 'index']);
    });

    Route::prefix('analytics')->group(function () {
        Route::get('/', [CustomerAnalyticsController::class, 'getAnalytics']);
        Route::get('/activity', [CustomerAnalyticsController::class, 'getActivity']);
        Route::get('/spending-trend', [CustomerAnalyticsController::class, 'getSpendingTrend']);
    });


    Route::prefix('savings')->group(function () {
        Route::get('/summary', [SavingsController::class, 'getSummary']);
        Route::get('/history', [SavingsController::class, 'getHistory']);
        Route::get('/price-comparison', [SavingsController::class, 'getPriceComparison']);
        Route::post('/goal', [SavingsController::class, 'setSavingsGoal']);
        Route::get('/goal', [SavingsController::class, 'getSavingsGoal']);
    });

    // Feedback and Contact Routes
    Route::group(['prefix' => 'feedback', 'middleware' => ['app_language']], function () {
        Route::post('submit', [App\Http\Controllers\Api\V3\FeedbackController::class, 'submitFeedback']);

        // Protected routes
        Route::group(['middleware' => ['api.auth']], function () {
            Route::get('entries', [App\Http\Controllers\Api\V3\FeedbackController::class, 'getFeedbackEntries']);
        });
    });

    Route::group(['prefix' => 'contact', 'middleware' => ['app_language']], function () {
        Route::post('send', [App\Http\Controllers\Api\V3\ContactController::class, 'sendContactMessage']);
        Route::post('newsletter', [App\Http\Controllers\Api\V3\ContactController::class, 'subscribeToNewsletter']);
    });

    // Marketing/Videos Routes
    Route::group(['prefix' => 'marketing', 'middleware' => ['app_language']], function () {
        Route::group(['prefix' => 'videos'], function () {
            Route::get('/featured', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'getFeaturedVideos']);
            Route::get('/categories', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'getCategories']);
            Route::get('/category/{category}', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'getVideosByCategory']);
            Route::get('/search', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'searchVideos']);
            Route::get('/{id}', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'getVideo']);

            // Protected routes that require authentication
            Route::middleware(['api.auth'])->group(function () {
                Route::post('/upload', [App\Http\Controllers\Api\V3\Marketing\ApiVideoController::class, 'uploadVideo']);
            });
        });
    });

    // Reference data routes
    Route::group(['prefix' => 'addresses', 'middleware' => ['app_language']], function () {
        Route::get('/states/{country}', [ApiAddressController::class, 'getStates']);
        Route::get('/cities/state/{id}', [ApiAddressController::class, 'getCitiesByState']);
        Route::post('/cities/add', [ApiAddressController::class, 'storeCity']);
    });

     // Settings Routes
     Route::group(['prefix' => 'settings', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('/addresses', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'getAddresses']);
        Route::post('/addresses', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'createShippingAddress']);
        Route::get('/addresses/default', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'getDefaultAddress']);
        Route::get('/addresses/{id}', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'getAddress']);
        Route::put('/addresses/{id}', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'updateShippingAddress']);
        Route::delete('/addresses/{id}', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'deleteShippingAddress']);
        Route::post('/addresses/{id}/default', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'setDefaultAddress']);
        Route::post('/addresses/validate', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'validateAddress']);
        Route::post('/checkout/address', [App\Http\Controllers\Api\V3\ApiAddressController::class, 'setCheckoutAddress']);
     });


    // V3 Messaging API routes
    Route::group(['prefix' => 'messaging', 'middleware' => ['api.auth']], function () {
        // Threads
        Route::get('threads', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@index');
        Route::post('threads', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@store');
        Route::get('threads/{id}', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@show');
        Route::put('threads/{id}', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@update');
        Route::post('threads/{id}/mark-as-read', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@markAsRead');
        Route::post('threads/{id}/archive', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@archive');
        Route::post('threads/{id}/close', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@close');
        Route::post('threads/{id}/reopen', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@reopen');
        Route::get('threads-unread-count', 'App\Http\Controllers\Api\V3\Messaging\ThreadController@unreadCounts');

        // Messages
        Route::get('threads/{threadId}/messages', 'App\Http\Controllers\Api\V3\Messaging\MessageController@index');
        Route::post('threads/{threadId}/messages', 'App\Http\Controllers\Api\V3\Messaging\MessageController@store');
        Route::post('messages/{id}/mark-as-read', 'App\Http\Controllers\Api\V3\Messaging\MessageController@markAsRead');

        // Conversations (direct messages between users)
        Route::get('conversations', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@index');
        Route::post('conversations', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@store');
        Route::get('conversations/{id}', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@show');
        Route::get('conversations/{id}/messages', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@getMessages');
        Route::post('conversations/{id}/messages', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@sendMessage');
        Route::post('conversations/{id}/mark-as-read', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@markAsRead');
        Route::get('conversations-unread-count', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@unreadCounts');
        // Adding the missing unread-counts route that frontend expects
        Route::get('unread-counts', 'App\Http\Controllers\Api\V3\Messaging\ConversationController@unreadCounts');
        
        // Message reactions
        Route::post('messages/{id}/reactions', 'App\Http\Controllers\Api\V3\Messaging\MessageController@addReaction');
        Route::delete('messages/{id}/reactions/{reaction}', 'App\Http\Controllers\Api\V3\Messaging\MessageController@removeReaction');
        Route::get('messages/{id}/reactions', 'App\Http\Controllers\Api\V3\Messaging\MessageController@getReactions');
    });

    Route::middleware(['api.auth'])->group(function () {

        // Broadcasting authentication
        Route::post('broadcasting/auth', function (Request $request) {
            return Broadcast::auth($request);
        });

        // Users routes
        Route::prefix('user')->group(function () {
            Route::get('profile', [ApiUserController::class, 'getProfile']);
            Route::post('profile', [ApiUserController::class, 'updateProfile']);
        });

        // Live Chat routes
        Route::prefix('live-chat')->group(function () {
            Route::get('messages', [ApiLiveChatController::class, 'getMessages']);
            Route::post('send', [ApiLiveChatController::class, 'sendMessage']);
            Route::get('status', [ApiLiveChatController::class, 'getStatus']);
            Route::post('start', [ApiLiveChatController::class, 'startChat']);
            Route::post('end', [ApiLiveChatController::class, 'endChat']);
            Route::post('messages/{messageId}/read', [ApiLiveChatController::class, 'markMessageAsRead']);
            Route::get('attachment/{attachmentId}/download', [ApiLiveChatController::class, 'downloadAttachment']);
        });

        // Upload routes
        Route::prefix('upload')->group(function () {
            // Avatar upload routes
            Route::post('avatar', [ApiAvatarUploadController::class, 'uploadAvatar']);
            Route::delete('avatar', [ApiAvatarUploadController::class, 'removeAvatar']);
            Route::get('avatar', [ApiAvatarUploadController::class, 'getAvatar']);
        });
    });

    // Return Reasons and Resolution Types
    Route::group(['prefix' => 'return-reasons', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiReturnReasonsController::class, 'index']);
    });

    Route::group(['prefix' => 'resolution-types', 'middleware' => ['app_language']], function () {
        Route::get('/', [ApiResolutionTypesController::class, 'index']);
    });

    // Uncomment and update these routes to use the newly created CustomerAnalyticsController
    Route::group(['prefix' => 'customer', 'middleware' => ['app_language', 'api.auth']], function () {
        // Customer analytics routes
        Route::prefix('analytics')->group(function () {
            Route::get('/{timeframe}', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getAnalytics']);
            Route::get('/', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getAnalytics']);
            Route::get('/activity', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getActivity']);
            Route::get('/spending-trends', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getSpendingTrend']);
        });
        // Direct routes to match frontend requests
        Route::get('/spending-trend', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getSpendingTrend']);
        Route::get('/activity', [App\Http\Controllers\Api\V3\Customer\CustomerAnalyticsController::class, 'getActivity']);
    });



    Route::group(['prefix' => 'savings-tracker', 'middleware' => ['app_language','api.auth']], function () {
        Route::get('/goal', [ApiSavingsTrackerController::class, 'getGoal']);
        Route::post('/goal', [ApiSavingsTrackerController::class, 'setGoal']);
        Route::put('/amount', [ApiSavingsTrackerController::class, 'updateAmount']);
        Route::delete('/goal', [ApiSavingsTrackerController::class, 'deleteGoal']);
        Route::get('/cost-breakdown', [ApiSavingsTrackerController::class, 'getCostBreakdown']);
        Route::get('/order-breakdown', [ApiSavingsTrackerController::class, 'getSavingsOrderBreakdown']);
        Route::get('/monthly-breakdown', [ApiSavingsTrackerController::class, 'getMonthlySavingsTrend']);
        Route::get('/stats', [ApiSavingsTrackerController::class, 'getSavingsStats']);
    });

    // Include dropshipper routes within v3 group
    include __DIR__ . '/api_dropshipper.php';

    Route::group(['prefix' => 'wishlist', 'middleware' => ['app_language', 'api.auth']], function () {
        Route::get('wishlist-by-user', [WishlistController::class, 'index']);
        Route::post('add', [WishlistController::class, 'store']);
        Route::post('delete-from-wishlist', [WishlistController::class, 'delete']);
        Route::get('check-product-in-wishlist', [WishlistController::class, 'checkProductInWishlist']);
        Route::delete('clear-wishlist', [WishlistController::class, 'deleteAll']);
        Route::get('stats', [WishlistController::class, 'getStats']);
        Route::post('move-to-cart', [WishlistController::class, 'moveToCart']);
    });
});