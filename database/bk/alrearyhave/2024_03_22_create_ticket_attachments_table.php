<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('ticket_attachments')) {
            Schema::create('ticket_attachments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id')->constrained('ticket_messages')->onDelete('cascade');
                $table->string('file_name');
                $table->integer('file_size');
                $table->string('file_type');
            $table->string('file_url');
            $table->timestamp('uploaded_at')->useCurrent();

                // Indexes
                $table->index('message_id');
                $table->index('file_type');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_attachments');
    }
}; 