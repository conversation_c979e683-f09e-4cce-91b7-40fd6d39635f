<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('refund_requests')) {
            Schema::create('refund_requests', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('order_id')->constrained()->onDelete('cascade');
                $table->decimal('amount', 10, 2);
                $table->string('reason');
                $table->text('description')->nullable();
                $table->string('status')->default('pending');
                $table->foreignId('return_request_info_id')->nullable();
                $table->string('payment_charge_id')->nullable();
                $table->timestamps();
            });
        } else {
            Schema::table('refund_requests', function (Blueprint $table) {
                if (!Schema::hasColumn('refund_requests', 'return_request_info_id')) {
                    $table->foreignId('return_request_info_id')->nullable();
                }
                if (!Schema::hasColumn('refund_requests', 'payment_charge_id')) {
                    $table->string('payment_charge_id')->nullable();
                }
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('refund_requests');
    }
};