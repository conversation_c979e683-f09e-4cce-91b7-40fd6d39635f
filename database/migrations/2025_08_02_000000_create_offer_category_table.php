<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOfferCategoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //if (!Schema::hasTable('offer_category')) {
            Schema::create('offer_category', function (Blueprint $table) {
                $table->id();
                $table->foreignId('dropshipper_offer_id');//->references('id')->on('dropshipper_offers');
                $table->unsignedInteger('category_id'); // Changed to unsignedInteger which is the type used in categories table
                $table->timestamps();

                //$table->foreign('dropshipper_offer_id')->references('id')->on('dropshipper_offers')->onDelete('cascade');
                // Commented out foreign key constraint which is causing the issue
                // $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');

                $table->unique(['dropshipper_offer_id', 'category_id']);
            });
        //}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('offer_category');
    }
}
