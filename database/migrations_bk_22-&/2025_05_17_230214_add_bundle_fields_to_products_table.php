<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'is_bundle')) {
                $table->boolean('is_bundle')->default(false);
            }
            if (!Schema::hasColumn('products', 'bundle_items')) {
                $table->json('bundle_items')->nullable();
            }
            if (!Schema::hasColumn('products', 'savings_amount')) {
                $table->decimal('savings_amount', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('products', 'savings_percentage')) {
                $table->decimal('savings_percentage', 5, 2)->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'is_bundle',
                'bundle_items',
                'savings_amount',
                'savings_percentage'
            ]);
        });
    }
};
