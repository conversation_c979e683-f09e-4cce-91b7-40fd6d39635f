<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //if (!Schema::hasTable('refund_request_infos')) {
        Schema::create('refund_request_infos', function (Blueprint $table) {
            $table->id();
            $table->string('refund_code')->unique();
            $table->foreignId('user_id')->nullable();
            $table->foreignId('return_request_info_id')->nullable();
            $table->foreignId('order_id');
            $table->integer('request_date');
            $table->integer('paid_date')->nullable();
            $table->integer('paid_by')->nullable();
            $table->string('stripe_charge_id',255)->nullable();
            $table->json('payment_details')->nullable();
            $table->decimal('paid_amount', 20, 2)->nullable();
            $table->decimal('amount', 20, 2);
            $table->boolean('admin_seen')->default(0);
            $table->text('admin_note')->nullable();
            $table->string('reason_for_refund',255)->nullable();
            $table->text('user_note')->nullable();
            $table->string('refundMethod',255)->nullable();
            $table->enum('refund_status', ['pending', 'approved', 'processing','completed', 'rejected', 'cancelled', 'cancelled_by_user','paid'])->default('pending')->comment('only for admin');
            $table->string('attachments')->nullable();
            $table->json('feedback')->nullable();
            $table->timestamps();
        });
       // }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('refund_request_infos');
    }
};
