<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add performance indexes to email_verify table
        Schema::table('email_verify', function (Blueprint $table) {
            // Add index for email lookups (if not exists)
            if (!$this->indexExists('email_verify', 'idx_email_verify_email')) {
                $table->index('email', 'idx_email_verify_email');
            }
            
            // Add composite index for OTP verification queries
            if (!$this->indexExists('email_verify', 'idx_email_verify_otp_lookup')) {
                $table->index(['email', 'otp', 'is_verify', 'expire_time'], 'idx_email_verify_otp_lookup');
            }
            
            // Add index for cleanup operations
            if (!$this->indexExists('email_verify', 'idx_email_verify_cleanup')) {
                $table->index(['expire_time', 'is_verify', 'created_at'], 'idx_email_verify_cleanup');
            }
            
            // Add index for resend operations
            if (!$this->indexExists('email_verify', 'idx_email_verify_resend')) {
                $table->index(['email', 'is_verify', 'last_resend_at'], 'idx_email_verify_resend');
            }
            
            // Add index for verification status checks
            if (!$this->indexExists('email_verify', 'idx_email_verify_status')) {
                $table->index(['email', 'is_verify'], 'idx_email_verify_status');
            }
        });

        // Add performance indexes to users table
        Schema::table('users', function (Blueprint $table) {
            // Add index for email lookups (if not exists)
            if (!$this->indexExists('users', 'idx_users_email_lookup')) {
                $table->index(['email', 'user_type'], 'idx_users_email_lookup');
            }
            
            // Add index for email verification status
            if (!$this->indexExists('users', 'idx_users_email_verified')) {
                $table->index(['email', 'email_verified_at'], 'idx_users_email_verified');
            }
        });

        // Create performance monitoring table
        if (!Schema::hasTable('email_verification_performance_logs')) {
            Schema::create('email_verification_performance_logs', function (Blueprint $table) {
                $table->id();
                $table->string('operation_type', 50); // 'email_check', 'otp_verify', 'otp_resend', etc.
                $table->string('email_hash', 64); // SHA256 hash of email for privacy
                $table->decimal('execution_time_ms', 8, 2); // Execution time in milliseconds
                $table->boolean('cache_hit')->default(false);
                $table->json('metadata')->nullable(); // Additional performance data
                $table->timestamp('created_at');
                
                // Indexes for performance monitoring
                $table->index(['operation_type', 'created_at'], 'idx_perf_logs_operation');
                $table->index(['email_hash', 'created_at'], 'idx_perf_logs_email');
                $table->index(['cache_hit', 'created_at'], 'idx_perf_logs_cache');
            });
        }

        // Create cache statistics table
        if (!Schema::hasTable('email_verification_cache_stats')) {
            Schema::create('email_verification_cache_stats', function (Blueprint $table) {
                $table->id();
                $table->string('cache_key_pattern', 100);
                $table->integer('total_requests')->default(0);
                $table->integer('cache_hits')->default(0);
                $table->integer('cache_misses')->default(0);
                $table->decimal('average_response_time_ms', 8, 2)->default(0);
                $table->timestamp('last_updated');
                $table->timestamps();
                
                // Indexes
                $table->index(['cache_key_pattern', 'last_updated'], 'idx_cache_stats_pattern');
            });
        }

        // Insert initial cache statistics records
        DB::table('email_verification_cache_stats')->insertOrIgnore([
            [
                'cache_key_pattern' => 'email_exists:*',
                'total_requests' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'average_response_time_ms' => 0,
                'last_updated' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'cache_key_pattern' => 'verification_status:*',
                'total_requests' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'average_response_time_ms' => 0,
                'last_updated' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'cache_key_pattern' => 'otp_record:*',
                'total_requests' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'average_response_time_ms' => 0,
                'last_updated' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop performance indexes from email_verify table
        Schema::table('email_verify', function (Blueprint $table) {
            $table->dropIndex('idx_email_verify_email');
            $table->dropIndex('idx_email_verify_otp_lookup');
            $table->dropIndex('idx_email_verify_cleanup');
            $table->dropIndex('idx_email_verify_resend');
            $table->dropIndex('idx_email_verify_status');
        });

        // Drop performance indexes from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_email_lookup');
            $table->dropIndex('idx_users_email_verified');
        });

        // Drop performance monitoring tables
        Schema::dropIfExists('email_verification_performance_logs');
        Schema::dropIfExists('email_verification_cache_stats');
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            return !empty($indexes);
        } catch (\Exception $e) {
            return false;
        }
    }
};