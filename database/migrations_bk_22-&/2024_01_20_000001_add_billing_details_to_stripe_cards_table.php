    <?php

    use Illuminate\Database\Migrations\Migration;
    use Illuminate\Database\Schema\Blueprint;
    use Illuminate\Support\Facades\Schema;

    class AddBillingDetailsToStripeCardsTable extends Migration
    {
        /**
         * Run the migrations.
         *
         * @return void
         */
        public function up()
        {
            Schema::table('stripe_cards', function (Blueprint $table) {
                if (!Schema::hasColumn('stripe_cards', 'billing_details')) {
                    $table->json('billing_details')->nullable()->after('id');
                }
                if (!Schema::hasColumn('stripe_cards', 'type')) {
                    $table->string('type')->nullable()->after('billing_details');
                }
                if (!Schema::hasColumn('stripe_cards', 'isDefault')) {
                    $table->boolean('isDefault')->default(false)->after('type');
                }
            });
        }

        /**
         * Reverse the migrations.
         *
         * @return void
         */
        public function down()
        {
            Schema::table('stripe_cards', function (Blueprint $table) {
                $table->dropColumn(['billing_details', 'type', 'isDefault']);
            });
        }
    }
