<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('offers', function (Blueprint $table) {
            // Add missing columns
            if (!Schema::hasColumn('offers', 'discount_type')) {
                $table->string('discount_type')->default('percentage')->after('promo_code');
            }
            if (!Schema::hasColumn('offers', 'status')) {
                $table->string('status')->default('active')->after('expiration_date');
            }
            if (!Schema::hasColumn('dropshipper_offers', 'user_type')) {
                $table->string('user_type')->default('all')->after('is_dropshipper_only');
            }
            if (!Schema::hasColumn('offers', 'created_by')) {
                $table->bigInteger('created_by')->nullable()->after('user_type');
            }
        });

        // Handle column renaming separately to avoid JSON constraint issues
        if (Schema::hasColumn('offers', 'expiration_date')) {
            Schema::table('offers', function (Blueprint $table) {
                $table->renameColumn('expiration_date', 'end_date');
            });
        }

        // For JSON columns, we need to be more careful with the renaming
        if (Schema::hasColumn('offers', 'terms_and_conditions')) {
            // Add new column with correct type
            Schema::table('offers', function (Blueprint $table) {
                $table->json('terms_conditions')->nullable()->after('tags');
            });

            // Copy data from old column to new column
            DB::statement('UPDATE offers SET terms_conditions = terms_and_conditions');

            // Drop old column
            Schema::table('offers', function (Blueprint $table) {
                $table->dropColumn('terms_and_conditions');
            });
        }

        if (Schema::hasColumn('offers', 'bulk_discount_thresholds')) {
            // Add new column with correct type
            Schema::table('offers', function (Blueprint $table) {
                $table->json('bulk_discounts')->nullable()->after('user_type');
            });

            // Copy data from old column to new column
            DB::statement('UPDATE offers SET bulk_discounts = bulk_discount_thresholds');

            // Drop old column
            Schema::table('offers', function (Blueprint $table) {
                $table->dropColumn('bulk_discount_thresholds');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('offers', function (Blueprint $table) {
            // Remove added columns
            if (Schema::hasColumn('offers', 'discount_type')) {
                $table->dropColumn('discount_type');
            }
            if (Schema::hasColumn('offers', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('offers', 'user_type')) {
                $table->dropColumn('user_type');
            }
            if (Schema::hasColumn('offers', 'created_by')) {
                $table->dropColumn('created_by');
            }
        });

        // Rename columns back to original names
        if (Schema::hasColumn('offers', 'end_date')) {
            Schema::table('offers', function (Blueprint $table) {
                $table->renameColumn('end_date', 'expiration_date');
            });
        }

        if (Schema::hasColumn('offers', 'terms_conditions')) {
            Schema::table('offers', function (Blueprint $table) {
                $table->json('terms_and_conditions')->nullable()->after('tags');
            });

            DB::statement('UPDATE offers SET terms_and_conditions = terms_conditions');

            Schema::table('offers', function (Blueprint $table) {
                $table->dropColumn('terms_conditions');
            });
        }
        
        if (Schema::hasColumn('dropshipper_offers', 'bulk_discounts')) {
            Schema::table('dropshipper_offers', function (Blueprint $table) {
                $table->json('bulk_discount_thresholds')->nullable()->after('is_dropshipper_only');
            });

            DB::statement('UPDATE offers SET bulk_discount_thresholds = bulk_discounts');

            Schema::table('offers', function (Blueprint $table) {
                $table->dropColumn('bulk_discounts');
            });
        }
    }
};
