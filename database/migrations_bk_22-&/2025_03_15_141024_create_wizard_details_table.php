<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('wizard_details')) {
        Schema::create('wizard_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wizard_info_id');
            $table->string('wizard_detail_title', 255)->nullable();
            $table->string('wizard_placement', 255)->nullable()->comment('middle,top,bottom');
            $table->string('wizard_detail_sub_title', 255)->nullable();
            $table->longText('wizard_detail_description')->nullable();
            $table->string('wizard_detail_thumbnail_url', 255)->nullable();
            $table->string('wizard_detail_images', 255)->nullable();
            $table->string('wizard_detail_images_url', 255)->nullable();
            $table->string('wizard_detail_mobile_images', 255)->nullable();
            $table->string('wizard_detail_mobile_images_url', 255)->nullable();
            $table->string('wizard_detail_redirect_url', 255)->nullable();
            $table->integer('wizard_detail_position')->default(0)->nullable();

            $table->string('wizard_detail_button_text', 255)->nullable();
            $table->string('wizard_detail_button_link', 255)->nullable();
            $table->string('wizard_detail_background_color', 255)->nullable();

            $table->foreignId('video_url')->nullable();
            $table->foreignId('category_id')->nullable();
            $table->foreignId('product_id')->nullable();
            $table->foreignId('blog_id')->nullable();
            $table->foreignId('brand_id')->nullable();
            $table->foreignId('collection_id')->nullable();
            $table->longText('wizard_detail_html')->nullable();
            $table->integer('wizard_video_duration')->nullable();
            $table->integer('wizard_view_count')->nullable();
            $table->integer('publish_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wizard_details');
    }
};
