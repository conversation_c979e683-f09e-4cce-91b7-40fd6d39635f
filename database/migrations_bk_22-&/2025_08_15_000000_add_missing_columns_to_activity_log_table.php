<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddMissingColumnsToActivityLogTable extends Migration
{
    public function up()
    {
        Schema::connection(config('activitylog.database_connection'))->table(config('activitylog.table_name'), function (Blueprint $table) {
            if (!Schema::hasColumn(config('activitylog.table_name'), 'log_name')) {
                $table->string('log_name')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'action')) {
                $table->string('action')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'old_status')) {
                $table->string('old_status')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'new_status')) {
                $table->string('new_status')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'reason')) {
                $table->text('reason')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'email_end_time')) {
                $table->timestamp('email_end_time')->nullable();
            }
            if (!Schema::hasColumn(config('activitylog.table_name'), 'details')) {
                $table->json('details')->nullable();
            }
        });
    }

    public function down()
    {
        Schema::connection(config('activitylog.database_connection'))->table(config('activitylog.table_name'), function (Blueprint $table) {
            $table->dropColumn([
                'log_name',
                'action',
                'old_status',
                'new_status',
                'reason',
                'email_end_time',
                'details'
            ]);
        });
    }
} 