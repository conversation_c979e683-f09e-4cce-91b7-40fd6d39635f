<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('order_message_attachments')) {
            Schema::create('order_message_attachments', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('message_id');
                $table->unsignedInteger('attachment_id');
                $table->timestamps();

            $table->foreign('message_id')->references('id')->on('order_messages')->onDelete('cascade');
            // Index for attachment_id without foreign key constraint
            $table->index('attachment_id');
                $table->unique(['message_id', 'attachment_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_message_attachments');
    }
};
