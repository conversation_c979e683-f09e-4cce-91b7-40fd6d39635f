<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create live chat message attachments table
        if (!Schema::hasTable('live_chat_message_attachments')) {
            Schema::create('live_chat_message_attachments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id')->constrained('live_chat_messages')->onDelete('cascade');
                $table->string('name'); // Original file name
                $table->string('url'); // File URL/path
                $table->string('type'); // MIME type
                $table->unsignedInteger('size'); // File size in bytes
                $table->timestamps();
                
                $table->index('message_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('live_chat_message_attachments');
    }
}; 