<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\EmailVerify;
use App\Models\B2BProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class EmailVerificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create email verification record for testing
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'verified_at' => now(),
            'user_type' => 'customer'
        ]);
    }

    /** @test */
    public function customer_registration_with_auto_login_works()
    {
        $userData = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer',
            'temp_user_id' => 'temp_123'
        ];

        $response = $this->postJson('/api/v3/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'user_type'
                        ],
                        'token' => [
                            'access_token',
                            'token_type',
                            'expires_in'
                        ]
                    ]
                ]);

        // Verify user was created and email is verified
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->email_verified_at);
        $this->assertEquals('customer', $user->user_type);
        
        // Verify response contains auto-login message
        $response->assertJson([
            'message' => 'Registration successful. You have been automatically logged in.'
        ]);
    }

    /** @test */
    public function seller_registration_with_auto_login_works()
    {
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'verified_at' => now(),
            'user_type' => 'seller'
        ]);

        $sellerData = [
            'name' => 'Jane Seller',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'business_name' => 'Test Business',
            'business_address' => [
                'street' => '123 Main St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zipCode' => '12345',
                'country' => 'Test Country'
            ],
            'tax_id' => 'TAX123',
            'business_type' => 'LLC',
            'website' => 'https://example.com',
            'category' => 'Electronics',
            'temp_user_id' => 'temp_456'
        ];

        $response = $this->postJson('/api/v3/auth/register/seller', $sellerData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'user_type'
                        ],
                        'seller' => [
                            'id',
                            'name',
                            'business_type'
                        ],
                        'token' => [
                            'access_token',
                            'token_type',
                            'expires_in'
                        ]
                    ]
                ]);

        // Verify user and seller profile were created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->email_verified_at);
        $this->assertEquals('seller', $user->user_type);
        
        $seller = B2BProfile::where('user_id', $user->id)->first();
        $this->assertNotNull($seller);
        $this->assertEquals('Test Business', $seller->name);
    }

    /** @test */
    public function dropshipper_registration_with_auto_login_works()
    {
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'verified_at' => now(),
            'user_type' => 'dropshipper'
        ]);

        $dropshipperData = [
            'name' => 'Bob Dropshipper',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'company_name' => 'Test Company',
            'business_address' => [
                'street' => '123 Business St',
                'city' => 'Business City',
                'state' => 'Business State',
                'zipCode' => '12345',
                'country' => 'Business Country'
            ],
            'shipping_address' => [
                'street' => '456 Shipping St',
                'city' => 'Shipping City',
                'state' => 'Shipping State',
                'zipCode' => '67890',
                'country' => 'Shipping Country'
            ],
            'tax_id' => 'TAX456',
            'business_type' => 'Corporation',
            'website' => 'https://dropshipper.com',
            'user_type' => 'b2b',
            'temp_user_id' => 'temp_789'
        ];

        $response = $this->postJson('/api/v3/auth/register/dropshipper', $dropshipperData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'user_type'
                        ],
                        'dropshipper' => [
                            'id',
                            'name',
                            'business_type'
                        ],
                        'token' => [
                            'access_token',
                            'token_type',
                            'expires_in'
                        ]
                    ]
                ]);

        // Verify user and dropshipper profile were created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->email_verified_at);
        $this->assertEquals('b2b', $user->user_type);
        
        $dropshipper = B2BProfile::where('user_id', $user->id)->first();
        $this->assertNotNull($dropshipper);
        $this->assertEquals('Test Company', $dropshipper->name);
    }

    /** @test */
    public function registration_fails_without_email_verification()
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer'
        ];

        $response = $this->postJson('/api/v3/auth/register', $userData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Email not verified'
                ]);

        // Verify user was not created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNull($user);
    }

    /** @test */
    public function registration_validates_required_fields()
    {
        $response = $this->postJson('/api/v3/auth/register', []);

        $response->assertStatus(400)
                ->assertJsonValidationErrors(['name', 'email', 'password', 'confirmPassword', 'user_type']);
    }

    /** @test */
    public function registration_prevents_duplicate_emails()
    {
        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'email_verified_at' => now()
        ]);

        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer'
        ];

        $response = $this->postJson('/api/v3/auth/register', $userData);

        $response->assertStatus(400)
                ->assertJsonValidationErrors(['email']);
    }
}