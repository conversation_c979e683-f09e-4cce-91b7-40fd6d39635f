<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First add the session_id column
        Schema::table('recently_viewed_products', function (Blueprint $table) {
            $table->string('session_id')->nullable()->after('user_id');
            $table->index('session_id');
        });
        
        // Then update user_id to be nullable
        DB::statement('ALTER TABLE recently_viewed_products MODIFY user_id BIGINT UNSIGNED NULL');
        
        // Add unique constraint
        Schema::table('recently_viewed_products', function (Blueprint $table) {
            // Create a composite unique index to ensure we don't have duplicate entries
            // This constraint allows either user_id or session_id to be null, but not both
            $table->unique(['product_id', 'user_id', 'session_id'], 'unique_recently_viewed_product');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('recently_viewed_products', function (Blueprint $table) {
            // Remove the unique constraint
            $table->dropUnique('unique_recently_viewed_product');
            
            // Remove the index
            $table->dropIndex(['session_id']);
        });
        
        // Make user_id required again
        DB::statement('ALTER TABLE recently_viewed_products MODIFY user_id BIGINT UNSIGNED NOT NULL');
        
        // Remove the session_id column
        Schema::table('recently_viewed_products', function (Blueprint $table) {
            $table->dropColumn('session_id');
        });
    }
};
