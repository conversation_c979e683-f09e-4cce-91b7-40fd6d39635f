<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('roles')) {
        Schema::table('roles', function (Blueprint $table) {
            $table->boolean('view_wallet_management')->default(false);
        });
        }
    }

    public function down()
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn('view_wallet_management');
        });
    }
}; 