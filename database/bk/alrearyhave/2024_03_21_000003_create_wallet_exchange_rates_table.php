<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('wallet_exchange_rates')) {
            Schema::create('wallet_exchange_rates', function (Blueprint $table) {
                $table->id();
                $table->integer('points');
                $table->decimal('currency_amount', 10, 2);
                $table->string('status')->default('active');
                $table->timestamp('valid_from')->nullable();
                $table->timestamp('valid_until')->nullable();
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet_exchange_rates');
    }
}; 