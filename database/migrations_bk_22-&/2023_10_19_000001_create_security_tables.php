<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSecurityTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        // Add missing fields to users table
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'two_factor_enabled')) {
                $table->boolean('two_factor_enabled')->default(false);
            }
            if (!Schema::hasColumn('users', 'two_factor_secret')) {
                $table->string('two_factor_secret')->nullable();
            }
            if (!Schema::hasColumn('users', 'two_factor_recovery_codes')) {
                $table->text('two_factor_recovery_codes')->nullable();
            }
            if (!Schema::hasColumn('users', 'two_factor_enabled_at')) {
                $table->timestamp('two_factor_enabled_at')->nullable();
            }
            if (!Schema::hasColumn('users', 'two_factor_last_used_at')) {
                $table->timestamp('two_factor_last_used_at')->nullable();
            }
            if (!Schema::hasColumn('users', 'password_changed_at')) {
                $table->timestamp('password_changed_at')->nullable();
            }
            if (!Schema::hasColumn('users', 'notify_on_new_login')) {
                $table->boolean('notify_on_new_login')->default(true);
            }
            if (!Schema::hasColumn('users', 'notify_on_password_change')) {
                $table->boolean('notify_on_password_change')->default(true);
            }
            if (!Schema::hasColumn('users', 'notify_on_security_alert')) {
                $table->boolean('notify_on_security_alert')->default(true);
            }
            if (!Schema::hasColumn('users', 'session_timeout')) {
                $table->integer('session_timeout')->default(30);
            }
            if (!Schema::hasColumn('users', 'allowed_ips')) {
                $table->text('allowed_ips')->nullable();
            }
            if (!Schema::hasColumn('users', 'is_active')) {
                $table->boolean('is_active')->default(true);
            }
            if (!Schema::hasColumn('users', 'deactivated_at')) {
                $table->timestamp('deactivated_at')->nullable();
            }
            if (!Schema::hasColumn('users', 'scheduled_for_deletion')) {
                $table->boolean('scheduled_for_deletion')->default(false);
            }
            if (!Schema::hasColumn('users', 'deletion_scheduled_at')) {
                $table->timestamp('deletion_scheduled_at')->nullable();
            }
            if (!Schema::hasColumn('users', 'deletion_reason')) {
                $table->text('deletion_reason')->nullable();
            }
            if (!Schema::hasColumn('users', 'bio')) {
                $table->string('bio')->nullable();
            }
            if (!Schema::hasColumn('users', 'date_of_birth')) {
                $table->date('date_of_birth')->nullable();
            }
            if (!Schema::hasColumn('users', 'gender')) {
                $table->string('gender')->nullable();
            }
            if (!Schema::hasColumn('users', 'language')) {
                $table->string('language')->default('en');
            }
            if (!Schema::hasColumn('users', 'country')) {
                $table->string('country')->default('US');
            }
            if (!Schema::hasColumn('users', 'timezone')) {
                $table->string('timezone')->default('UTC');
            }
        });

        // Create security logs table
        if (!Schema::hasTable('security_logs')) {
            Schema::create('security_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('event_type');
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->text('details')->nullable();
                $table->string('location')->nullable();
                $table->string('status')->default('success');
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        // Create login attempts table
        if (!Schema::hasTable('login_attempts')) {
            Schema::create('login_attempts', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id')->nullable();
                $table->string('email')->nullable();
                $table->string('ip_address');
                $table->text('user_agent')->nullable();
                $table->boolean('success')->default(false);
                $table->string('location')->nullable();
                $table->string('failure_reason')->nullable();
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        // Create API keys table
        if (!Schema::hasTable('api_keys')) {
            Schema::create('api_keys', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('name');
                $table->string('key', 64);
                $table->text('permissions')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamp('last_used_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->index('key');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop API keys table
        Schema::dropIfExists('api_keys');

        // Drop login attempts table
        Schema::dropIfExists('login_attempts');

        // Drop security logs table
        Schema::dropIfExists('security_logs');

        // Drop added columns from users table
        if (Schema::hasColumn('users', 'two_factor_enabled')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn([
                    'two_factor_enabled',
                    'two_factor_secret',
                    'two_factor_recovery_codes',
                    'two_factor_enabled_at',
                    'two_factor_last_used_at',
                    'password_changed_at',
                    'notify_on_new_login',
                    'notify_on_password_change',
                    'notify_on_security_alert',
                    'session_timeout',
                    'allowed_ips',
                    'is_active',
                    'deactivated_at',
                    'scheduled_for_deletion',
                    'deletion_scheduled_at',
                    'deletion_reason',
                    'bio',
                    'date_of_birth',
                    'gender',
                    'language',
                    'country',
                    'timezone'
                ]);
            });
        }
    }
}