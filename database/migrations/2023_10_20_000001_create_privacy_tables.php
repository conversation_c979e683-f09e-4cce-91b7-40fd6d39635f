<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrivacyTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create user privacy settings table
        if (!Schema::hasTable('user_privacy_settings')) {
            Schema::create('user_privacy_settings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id');
                //$table->unsignedInteger('user_id');
                $table->boolean('marketing_emails')->default(false);
                $table->boolean('third_party_cookies')->default(false);
                $table->boolean('analytics_tracking')->default(false);
                $table->boolean('data_sharing')->default(false);
                $table->boolean('personalization')->default(false);
                $table->timestamps();

               // $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }






    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop privacy policies table
        Schema::dropIfExists('privacy_policies');

        // Drop privacy consents table
        Schema::dropIfExists('privacy_consents');

        // Drop user privacy settings table
        Schema::dropIfExists('user_privacy_settings');

        // Drop user preferences table
        Schema::dropIfExists('user_preferences');
    }
}
