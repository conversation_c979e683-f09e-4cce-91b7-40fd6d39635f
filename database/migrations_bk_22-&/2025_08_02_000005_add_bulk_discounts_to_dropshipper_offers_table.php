<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBulkDiscountsToDropshipperOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('offers')) {
            Schema::table('offers', function (Blueprint $table) {
                $table->json('bulk_discounts')->nullable()->after('tags');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('offers', function (Blueprint $table) {
            if (Schema::hasColumn('offers', 'bulk_discounts')) {
                $table->dropColumn('bulk_discounts');
            }
        });
    }
}
