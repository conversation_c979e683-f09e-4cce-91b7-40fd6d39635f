<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('message_read_status')) {
            Schema::create('message_read_status', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('message_id');
                $table->unsignedInteger('user_id');
                $table->timestamp('read_at')->useCurrent();
                $table->timestamps();
                $table->unique(['message_id', 'user_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('message_read_status');
    }
};
