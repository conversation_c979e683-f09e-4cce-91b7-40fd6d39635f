<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewColumnsToTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add full_name column to payku_transactions table if it doesn't exist
        Schema::table('payku_transactions', function (Blueprint $table) {
            if (!Schema::hasColumn('payku_transactions', 'full_name')) {
                $table->string('full_name')->nullable()->after('email');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payku_transactions', function (Blueprint $table) {
            if (Schema::hasColumn('payku_transactions', 'full_name')) {
                $table->dropColumn('full_name');
            }
        });
    }
} 