<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('savings_goals')) {
            Schema::create('savings_goals', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->foreignId('user_id')->unique();
                $table->decimal('goal_amount', 10, 2)->default(0);
                $table->decimal('current_amount', 10, 2)->default(0);
                $table->date('start_date');
                $table->date('target_date');
                $table->enum('status', ['in_progress', 'completed', 'expired'])->default('in_progress');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('savings_goals');
    }
};
