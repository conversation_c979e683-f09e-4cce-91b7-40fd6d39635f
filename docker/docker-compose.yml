version: '3.8'

services:
  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: buzfi_nginx
    ports:
      - "80:80"
    volumes:
      - ../:/var/www/html
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - buzfi_network
    restart: unless-stopped

  # PHP-FPM Service
  php:
    build:
      context: ./php
      dockerfile: Dockerfile
    container_name: buzfi_php
    volumes:
      - ../:/var/www/html
      - ./php/php.ini:/usr/local/etc/php/php.ini
    depends_on:
      - mysql
      - redis
    networks:
      - buzfi_network
    restart: unless-stopped
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=buzfi
      - DB_USERNAME=buzfi_user
      - DB_PASSWORD=buzfi_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: buzfi_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: buzfi
      MYSQL_USER: buzfi_user
      MYSQL_PASSWORD: buzfi_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ../import_database:/docker-entrypoint-initdb.d
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - buzfi_network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: buzfi_phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - mysql
    networks:
      - buzfi_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: buzfi_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - buzfi_network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf

  # Redis Commander (Redis GUI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: buzfi_redis_commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - buzfi_network
    restart: unless-stopped

networks:
  buzfi_network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
