<?php

namespace Tests\Feature;

use App\Models\EmailVerify;
use App\Models\User;
use App\Models\B2BProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;
use Carbon\Carbon;

class EmailVerificationCompleteIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    /**
     * Test complete customer registration flow with email verification
     */
    public function test_complete_customer_registration_flow()
    {
        $email = $this->faker->unique()->safeEmail;
        $password = 'password123';
        $name = $this->faker->name;

        // Step 1: Check email and send OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'email' => $email,
                        'otp_sent' => true
                    ]
                ]);

        // Step 2: Get the OTP from database
        $emailVerifyRecord = EmailVerify::where('email', $email)->first();
        $this->assertNotNull($emailVerifyRecord);
        $otp = $emailVerifyRecord->otp;

        // Step 3: Verify OTP
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => $otp
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'email' => $email,
                        'verified' => true
                    ]
                ]);

        // Step 4: Complete registration with auto-login
        $response = $this->postJson('/api/v3/auth/register', [
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'password_confirmation' => $password,
            'user_type' => 'customer'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'user_type'
                        ],
                        'token' => [
                            'access_token',
                            'token_type',
                            'expires_in'
                        ]
                    ]
                ]);

        // Verify user was created and auto-logged in
        $user = User::where('email', $email)->first();
        $this->assertNotNull($user);
        $this->assertEquals($name, $user->name);
        $this->assertEquals('customer', $user->user_type);
        $this->assertNotNull($user->email_verified_at);

        // Verify email verification record is marked as verified
        $emailVerifyRecord->refresh();
        $this->assertTrue($emailVerifyRecord->is_verify);
    }

    /**
     * Test complete seller registration flow with email verification
     */
    public function test_complete_seller_registration_flow()
    {
        $email = $this->faker->unique()->safeEmail;
        $password = 'password123';
        $name = $this->faker->name;
        $businessName = $this->faker->company;
        $phone = '+1234567890';

        // Step 1: Check email and send OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'seller'
        ]);

        $response->assertStatus(200);

        // Step 2: Verify OTP
        $emailVerifyRecord = EmailVerify::where('email', $email)->first();
        $otp = $emailVerifyRecord->otp;

        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => $otp
        ]);

        $response->assertStatus(200);

        // Step 3: Complete seller registration
        $response = $this->postJson('/api/v3/auth/register/seller', [
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'password_confirmation' => $password,
            'phone' => $phone,
            'business_name' => $businessName,
            'business_address' => [
                'street' => '123 Business St',
                'city' => 'Business City',
                'state' => 'Business State',
                'zipCode' => '12345',
                'country' => 'US'
            ],
            'tax_id' => 'TAX123456',
            'business_type' => 'LLC',
            'category' => 'Electronics'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'user',
                        'seller',
                        'token'
                    ]
                ]);

        // Verify user and seller profile were created
        $user = User::where('email', $email)->first();
        $this->assertNotNull($user);
        $this->assertEquals('seller', $user->user_type);

        $sellerProfile = B2BProfile::where('user_id', $user->id)->first();
        $this->assertNotNull($sellerProfile);
        $this->assertEquals($businessName, $sellerProfile->name);
    }

    /**
     * Test existing email detection and proper error response
     */
    public function test_existing_email_detection()
    {
        // Create existing user
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        // Try to register with existing email
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        $response->assertStatus(409)
                ->assertJson([
                    'success' => false,
                    'message' => 'An account with this email already exists. Please login to your customer account.'
                ]);
    }

    /**
     * Test OTP expiration and resend functionality
     */
    public function test_otp_expiration_and_resend()
    {
        $email = $this->faker->unique()->safeEmail;

        // Step 1: Send initial OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ]);

        $response->assertStatus(200);

        // Step 2: Manually expire the OTP
        $emailVerifyRecord = EmailVerify::where('email', $email)->first();
        $emailVerifyRecord->update([
            'expire_time' => Carbon::now()->subMinutes(15)
        ]);

        // Step 3: Try to verify expired OTP
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => $emailVerifyRecord->otp
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'The verification code has expired. Please request a new one.'
                ]);

        // Step 4: Resend OTP (should work after cooldown)
        $emailVerifyRecord->update([
            'created_at' => Carbon::now()->subMinutes(6) // Past cooldown period
        ]);

        $response = $this->postJson('/api/v3/auth/resend-registration-otp', [
            'email' => $email
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'email' => $email,
                        'otp_sent' => true
                    ]
                ]);
    }

    /**
     * Test rate limiting and cooldown functionality
     */
    public function test_rate_limiting_and_cooldown()
    {
        $email = $this->faker->unique()->safeEmail;

        // Step 1: Send initial OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ]);

        $response->assertStatus(200);

        // Step 2: Try to resend immediately (should be blocked by cooldown)
        $response = $this->postJson('/api/v3/auth/resend-registration-otp', [
            'email' => $email
        ]);

        $response->assertStatus(429)
                ->assertJsonFragment([
                    'success' => false
                ]);
    }

    /**
     * Test forgot password integration with email verification
     */
    public function test_forgot_password_integration()
    {
        // Create existing user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('oldpassword')
        ]);

        // Step 1: Request password reset
        $response = $this->postJson('/api/v3/auth/forgot-password', [
            'email' => $user->email
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        // Step 2: Get OTP and verify
        $emailVerifyRecord = EmailVerify::where('email', $user->email)->first();
        $otp = $emailVerifyRecord->otp;

        // Step 3: Reset password with OTP
        $newPassword = 'newpassword123';
        $response = $this->postJson('/api/v3/auth/reset-password', [
            'email' => $user->email,
            'otp' => $otp,
            'password' => $newPassword,
            'password_confirmation' => $newPassword
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        // Verify password was changed
        $user->refresh();
        $this->assertTrue(password_verify($newPassword, $user->password));
    }

    /**
     * Test cart merge functionality during auto-login
     */
    public function test_cart_merge_during_auto_login()
    {
        $email = $this->faker->unique()->safeEmail;
        $tempUserId = 'temp_' . uniqid();

        // Complete email verification
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ]);

        $emailVerifyRecord = EmailVerify::where('email', $email)->first();
        $otp = $emailVerifyRecord->otp;

        $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => $otp
        ]);

        // Register with temp_user_id for cart merge
        $response = $this->postJson('/api/v3/auth/register', [
            'name' => $this->faker->name,
            'email' => $email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'user_type' => 'customer',
            'temp_user_id' => $tempUserId
        ]);

        $response->assertStatus(201);

        // Verify user was created with auto-login
        $user = User::where('email', $email)->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->email_verified_at);
    }

    /**
     * Test security measures and logging
     */
    public function test_security_measures_and_logging()
    {
        Log::shouldReceive('channel')
            ->with('api_registration')
            ->andReturnSelf();
        
        Log::shouldReceive('info')
            ->once();

        $email = $this->faker->unique()->safeEmail;

        // Test with invalid OTP format
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => '12345' // Invalid format (5 digits instead of 6)
        ]);

        $response->assertStatus(400);

        // Test with non-existent email
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => '123456'
        ]);

        $response->assertStatus(400);
    }

    /**
     * Test error handling and recovery scenarios
     */
    public function test_error_handling_scenarios()
    {
        $email = $this->faker->unique()->safeEmail;

        // Test registration without email verification
        $response = $this->postJson('/api/v3/auth/register', [
            'name' => $this->faker->name,
            'email' => $email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'user_type' => 'customer'
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Email not verified'
                ]);

        // Test invalid validation data
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => 'invalid-email',
            'user_type' => 'invalid-type'
        ]);

        $response->assertStatus(400);
    }
}