<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailVerify extends Model
{
    use HasFactory;

    protected $table = 'email_verify';

    protected $fillable = [
        'email',
        'otp',
        'generate_date',
        'expire_time',
        'duration',
        'is_verify',
        'resend_count',
        'last_resend_at'
    ];

    protected $casts = [
        'generate_date' => 'datetime',
        'expire_time' => 'datetime',
        'last_resend_at' => 'datetime',
        'is_verify' => 'boolean',
        'resend_count' => 'integer',
        'duration' => 'integer'
    ];

    /**
     * Generate a new OTP for the email with enhanced security
     */
    public static function generateOTP($email, $duration = 15)
    {
        // Generate cryptographically secure OTP
        $otp = self::generateSecureOTP();
        $generateDate = Carbon::now();
        $expireTime = $generateDate->copy()->addMinutes($duration);

        // Get existing record to increment resend count properly
        $existingRecord = self::where('email', $email)->first();
        $resendCount = $existingRecord ? $existingRecord->resend_count + 1 : 1;

        // Invalidate any existing OTPs for this email (single-use enforcement)
        self::where('email', $email)
            ->where('is_verify', false)
            ->update(['otp' => null, 'expire_time' => Carbon::now()]);

        return self::updateOrCreate(
            ['email' => $email],
            [
                'otp' => $otp,
                'generate_date' => $generateDate,
                'expire_time' => $expireTime,
                'duration' => $duration,
                'is_verify' => false,
                'last_resend_at' => $generateDate,
                'resend_count' => $resendCount
            ]
        );
    }

    /**
     * Generate cryptographically secure OTP
     */
    private static function generateSecureOTP()
    {
        try {
            // Use cryptographically secure random number generation
            $bytes = random_bytes(3); // 3 bytes = 24 bits
            $number = unpack('N', "\x00" . $bytes)[1]; // Convert to integer
            $otp = str_pad($number % 1000000, 6, '0', STR_PAD_LEFT);
            
            // Ensure OTP doesn't start with 0 (for better user experience)
            if ($otp[0] === '0') {
                $otp = '1' . substr($otp, 1);
            }
            
            return $otp;
        } catch (\Exception $e) {
            // Fallback to less secure but still acceptable method
            \Log::warning('Failed to generate secure OTP, using fallback method: ' . $e->getMessage());
            return str_pad(mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Verify OTP for the email with enhanced security
     */
    public static function verifyOTP($email, $otp)
    {
        // Add timing attack protection by always performing the same operations
        $startTime = microtime(true);
        
        $record = self::where('email', $email)
                     ->where('otp', $otp)
                     ->where('is_verify', false)
                     ->where('expire_time', '>', Carbon::now())
                     ->first();

        $isValid = false;
        
        if ($record) {
            // Double-check expiration to prevent race conditions
            if (!$record->isExpired()) {
                // Mark as verified and immediately invalidate OTP (single-use)
                $record->update([
                    'is_verify' => true,
                    'otp' => null, // Immediately invalidate to prevent reuse
                    'verified_at' => Carbon::now()
                ]);
                
                $isValid = true;
                
                // Log successful verification
                \Log::info('OTP verification successful', [
                    'email' => hash('sha256', $email),
                    'timestamp' => Carbon::now()->toISOString()
                ]);
            } else {
                // OTP expired, clean it up
                $record->update(['otp' => null]);
                
                \Log::warning('OTP verification failed - expired', [
                    'email' => hash('sha256', $email),
                    'expired_at' => $record->expire_time->toISOString(),
                    'timestamp' => Carbon::now()->toISOString()
                ]);
            }
        } else {
            // Log failed verification attempt
            \Log::warning('OTP verification failed - invalid OTP', [
                'email' => hash('sha256', $email),
                'timestamp' => Carbon::now()->toISOString()
            ]);
        }

        // Timing attack protection - ensure consistent response time
        $elapsedTime = microtime(true) - $startTime;
        $minTime = 0.1; // Minimum 100ms response time
        
        if ($elapsedTime < $minTime) {
            usleep(($minTime - $elapsedTime) * 1000000);
        }

        return $isValid;
    }

    /**
     * Check if email is already verified
     */
    public static function isEmailVerified($email)
    {
        return self::where('email', $email)
                  ->where('is_verify', true)
                  ->exists();
    }

    /**
     * Clean up expired OTPs
     */
    public static function cleanupExpiredOTPs()
    {
        return self::where('expire_time', '<', Carbon::now())
                  ->where('is_verify', false)
                  ->update(['otp' => null]);
    }

    /**
     * Check if OTP is expired
     */
    public function isExpired()
    {
        return Carbon::now()->greaterThan($this->expire_time);
    }

    /**
     * Get remaining time in minutes
     */
    public function getRemainingTimeAttribute()
    {
        if ($this->isExpired()) {
            return 0;
        }
        
        return Carbon::now()->diffInMinutes($this->expire_time);
    }

    /**
     * Check if resend limit is reached (max 5 attempts)
     */
    public function canResend()
    {
        return $this->resend_count < 5;
    }

    /**
     * Check if the email is in 5-minute cooldown period
     */
    public function isInCooldownPeriod()
    {
        if (!$this->last_resend_at) {
            return false;
        }

        return Carbon::now()->diffInMinutes($this->last_resend_at) < 5;
    }

    /**
     * Get remaining cooldown minutes
     */
    public function getRemainingCooldownMinutes()
    {
        if (!$this->last_resend_at || !$this->isInCooldownPeriod()) {
            return 0;
        }

        $elapsedMinutes = Carbon::now()->diffInMinutes($this->last_resend_at);
        return 5 - $elapsedMinutes;
    }

    /**
     * Get the next time when resend will be available
     */
    public function getNextResendTime()
    {
        if (!$this->last_resend_at) {
            return Carbon::now();
        }

        return $this->last_resend_at->copy()->addMinutes(5);
    }

    /**
     * Reset resend count after 1 hour
     */
    public static function resetExpiredResendCounts()
    {
        return self::where('last_resend_at', '<', Carbon::now()->subHour())
                  ->where('resend_count', '>=', 5)
                  ->update(['resend_count' => 0]);
    }

    /**
     * Batch cleanup expired OTPs for better performance
     */
    public static function batchCleanupExpiredOTPs(int $batchSize = 1000): int
    {
        $totalCleaned = 0;
        
        do {
            $cleaned = self::where('expire_time', '<', Carbon::now())
                          ->where('is_verify', false)
                          ->whereNotNull('otp')
                          ->limit($batchSize)
                          ->update(['otp' => null]);
            
            $totalCleaned += $cleaned;
        } while ($cleaned > 0);

        return $totalCleaned;
    }

    /**
     * Delete old unverified records in batches
     */
    public static function batchDeleteOldRecords(int $daysOld = 1, int $batchSize = 1000): int
    {
        $totalDeleted = 0;
        
        do {
            $deleted = self::where('created_at', '<', Carbon::now()->subDays($daysOld))
                          ->where('is_verify', false)
                          ->whereNull('otp')
                          ->limit($batchSize)
                          ->delete();
            
            $totalDeleted += $deleted;
        } while ($deleted > 0);

        return $totalDeleted;
    }

    /**
     * Get records that need cleanup (for monitoring)
     */
    public static function getCleanupStatistics(): array
    {
        return [
            'expired_otps_count' => self::where('expire_time', '<', Carbon::now())
                                       ->where('is_verify', false)
                                       ->whereNotNull('otp')
                                       ->count(),
            'old_unverified_count' => self::where('created_at', '<', Carbon::now()->subDay())
                                         ->where('is_verify', false)
                                         ->whereNull('otp')
                                         ->count(),
            'high_resend_count' => self::where('resend_count', '>=', 5)
                                      ->where('last_resend_at', '<', Carbon::now()->subHour())
                                      ->count(),
            'total_verified' => self::where('is_verify', true)->count(),
            'total_pending' => self::where('is_verify', false)->whereNotNull('otp')->count()
        ];
    }

    /**
     * Optimize email lookup with proper indexing
     */
    public static function findByEmailOptimized(string $email)
    {
        return self::where('email', $email)
                  ->orderBy('created_at', 'desc')
                  ->first();
    }

    /**
     * Check if email exists in users table (for compatibility)
     */
    public static function emailExistsInUsers(string $email): bool
    {
        return \App\Models\User::where('email', $email)->exists();
    }

    /**
     * Ensure database compatibility with existing auth structure
     */
    public static function ensureAuthCompatibility(): bool
    {
        try {
            // Check if required columns exist
            $requiredColumns = ['email', 'otp', 'generate_date', 'expire_time', 'is_verify', 'resend_count', 'last_resend_at'];
            $tableColumns = \Schema::getColumnListing('email_verify');
            
            foreach ($requiredColumns as $column) {
                if (!in_array($column, $tableColumns)) {
                    return false;
                }
            }

            // Check if users table has required auth columns
            $userColumns = \Schema::getColumnListing('users');
            $requiredUserColumns = ['id', 'email', 'password', 'email_verified_at'];
            
            foreach ($requiredUserColumns as $column) {
                if (!in_array($column, $userColumns)) {
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Auth compatibility check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get performance metrics for monitoring
     */
    public static function getPerformanceMetrics(): array
    {
        $start = microtime(true);
        
        // Test email lookup performance
        self::where('email', '<EMAIL>')->first();
        $emailLookupTime = (microtime(true) - $start) * 1000;

        $start = microtime(true);
        
        // Test OTP verification performance
        self::where('email', '<EMAIL>')
           ->where('otp', '123456')
           ->where('is_verify', false)
           ->first();
        $otpVerificationTime = (microtime(true) - $start) * 1000;

        $start = microtime(true);
        
        // Test cleanup query performance
        self::where('expire_time', '<', Carbon::now())
           ->where('is_verify', false)
           ->count();
        $cleanupQueryTime = (microtime(true) - $start) * 1000;

        return [
            'email_lookup_ms' => round($emailLookupTime, 2),
            'otp_verification_ms' => round($otpVerificationTime, 2),
            'cleanup_query_ms' => round($cleanupQueryTime, 2),
            'total_records' => self::count(),
            'index_usage' => self::getIndexUsage()
        ];
    }

    /**
     * Get index usage statistics
     */
    private static function getIndexUsage(): array
    {
        try {
            $indexes = \DB::select("SHOW INDEX FROM email_verify");
            return collect($indexes)->map(function ($index) {
                return [
                    'name' => $index->Key_name,
                    'column' => $index->Column_name,
                    'unique' => $index->Non_unique == 0
                ];
            })->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }
} 