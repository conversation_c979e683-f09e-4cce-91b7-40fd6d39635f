<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            if (!Schema::hasColumn('reviews', 'dropshipper_response')) {
                $table->text('dropshipper_response')->nullable()->after('comment');
            }
            if (!Schema::hasColumn('reviews', 'dropshipper_response_date')) {
                $table->timestamp('dropshipper_response_date')->nullable()->after('dropshipper_response');
            }
            if (!Schema::hasColumn('reviews', 'helpful_count')) {
                $table->integer('helpful_count')->default(0)->after('dropshipper_response_date');
            }
            if (!Schema::hasColumn('reviews', 'tags')) {
                $table->json('tags')->nullable()->after('helpful_count');
            }
            if (!Schema::hasColumn('reviews', 'verified')) {
                $table->boolean('verified')->default(false)->after('tags');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn([
                'dropshipper_response',
                'dropshipper_response_date',
                'helpful_count',
                'tags',
                'verified'
            ]);
        });
    }
}; 