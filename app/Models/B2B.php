<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class B2B extends Model
{
    protected $with = ['user', 'user.b2b_profile'];
    public function user(){
        return $this->belongsTo(User::class);
    }

    public function payments(){
        return $this->hasMany(Payment::class);
    }

    public function seller_package(){
      return $this->belongsTo(SellerPackage::class);
  }

    public function b2b_profile(){
        return $this->belongsTo(B2BProfile::class);
    }
}
