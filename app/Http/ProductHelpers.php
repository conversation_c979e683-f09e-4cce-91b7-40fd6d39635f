<?php

use Illuminate\Support\Facades\Log;

if (!function_exists('product_price_object')) {
    function product_price_object($product): array
    {


        return [
            'base_price' => (float)product_base_price($product),
            'base_discounted_price' => (float)home_discounted_base_price_for_user($product),
            'discount_amount' => (float)($product->discount_type == 'amount'? $product->discount : 0),
            'discount_percent' => (float)($product->discount_type == 'percent'?$product->discount : 0),
        ] ;
    }
}
if (!function_exists('product_sku_by_product_id')) {
    function product_sku_by_product_id($product, $variant=null): string|null
    {
        if ($variant) {
            return \App\Models\ProductStock::where('variant', $variant)->where('product_id', $product->id)->first()->sku;
        } else {
            return \App\Models\ProductStock::where('product_id', $product->id)->first()->sku;
        }
    }
}
if (!function_exists('product_dropshipper_price_object')) {
    function product_dropshipper_price_object($product): array
    {
        return [
            'base_price' => (float)product_base_price($product),
            'base_discounted_price' => (float)home_discounted_base_price_for_dropshipper($product),
            'discount_amount' => (float)($product->discount_type == 'amount'?$product->discount : 0),
            'discount_percent' => (float)($product->discount_type == 'percent'?$product->discount : 0),
        ];
    }
}
if (!function_exists('product_wholesales_price_object')) {
    function product_wholesales_price_object($product): array
    {
        return [
            'base_price' => (float)product_base_price($product),
            'base_discounted_price' => (float)home_discounted_base_price_for_user($product),
            'discount_amount' => (float)($product->discount_type == 'amount'?$product->discount : 0),
            'discount_percent' => (float)($product->discount_type == 'percent'?$product->discount : 0),
        ];
    }
}
if (!function_exists('product_category_object')) {
    function product_category_object($product): array
    {
        if ($product->category != null) {
            return [
                'name' => $product->category->name,
                'banner' => uploaded_asset($product->category->banner),
                'icon' => $product->category->icon ? uploaded_asset($product->category->icon) : "" ,
            ];
        } else {
            return [
                'name' => "",
                'banner' => "",
                'icon' => "",
            ];
        }
    }
}
if (!function_exists('product_brand_object')) {
    function product_brand_object($product): array
    {
        if ($product->brand != null) {
            $brand = [
                'id' => $product->brand->id,
                'name' => $product->brand->getTranslation('name'),
                'logo' => uploaded_asset($product->brand->logo),
            ];
        }else{
            $brand = [
                'id' => 0,
                'name' => "",
                'logo' => "",
            ];
        }
        return $brand;
    }
}

if (!function_exists('home_discounted_base_price_for_user')) {
    function home_discounted_base_price_for_user($product,$variant=null,$with_tax = true,$with_discount = true, $formatted = true)
    {
        $product_discount=0;
        $product_tax=0;
        if($variant !="" && $product->variant_product == 1){
            $productStock = \App\Models\ProductStock::where('variant', $variant)->first();
            $price = $productStock->price;
        }else{
            $price = $product->unit_price;
        }
        if($with_discount){
            $product_discount = product_discount($product,$price);
        }
        if($with_tax){
            $product_tax=product_tax($product,$price);
        }
        $price += $product_tax;
        $price -= $product_discount;
        return $formatted ? number_format($price, get_setting('no_of_decimals')) : $price;
    }
}
if (!function_exists('home_discounted_base_price_for_dropshipper')) {
    function home_discounted_base_price_for_dropshipper($product,$variant=null,$with_tax = true,$with_discount = true, $formatted = true)
    {
        $product_discount=0;
        $product_tax=0;
        $productStock= null;
            if($variant !=null && $product->variant_product == 1){
                $productStock = \App\Models\ProductStock::where('variant', $variant)->first();
            }
            if (auth()->user() && auth()->user()->user_type == 'b2b') {
                if($productStock){
                    $price = $productStock->dropshipper_price;
                }else if ($product->dropshipper_price != 0) {
                    $price = $product->dropshipper_price;
                } else {
                    $price = $product->unit_price;
                }
            } else {
                $price = $product->unit_price;
            }

            if($with_discount){
                $product_discount = product_discount($product,$price);
            }

            if($with_tax){
                $product_tax=product_tax($product,$price);
            }
        $price += $product_tax;
        $price -= $product_discount;
        return $formatted ? number_format($price, get_setting('no_of_decimals')) : $price;
    }
}
if (!function_exists('product_base_price')) {
    function product_base_price($product, $formatted = true)
    {
        $price = $product->unit_price;
        $tax = 0;

        foreach ($product->taxes as $product_tax) {
            if ($product_tax->tax_type == 'percent') {
                $tax += ($price * $product_tax->tax) / 100;
            } elseif ($product_tax->tax_type == 'amount') {
                $tax += $product_tax->tax;
            }
        }
        $price += $tax;
        return $formatted ? number_format($price, get_setting('no_of_decimals')) : $price;
    }
}
if (!function_exists('product_stock_quantity')) {
    function product_stock_quantity($product,$variant=null)
    {
        if ($product->variant_product == 1 && $variant != null) {
            $product_stock = $product->stocks->where('variant', $variant)->first();
        } else {
            $product_stock = $product->stocks->where('product_id', $product->id)->first();
        }
        return $product_stock ? $product_stock->qty : 0;
    }
}
if (!function_exists('product_discount')) {
    function product_discount($product,$price)
    {
        $product_discount=0;
        $discount_applicable = false;
        if ($product->discount_start_date == null) {
            $discount_applicable = true;
        } elseif (
            strtotime(date('d-m-Y H:i:s')) >= $product->discount_start_date &&
            strtotime(date('d-m-Y H:i:s')) <= $product->discount_end_date
        ) {
            $discount_applicable = true;
        }
        if ($discount_applicable) {
            if ($product->discount_type == 'percent') {
                $product_discount = ($price * $product->discount) / 100;
            } elseif ($product->discount_type == 'amount') {
                $product_discount = $product->discount;
            }
        }
        return  $product_discount ;
    }
}
if (!function_exists('product_tax')) {
    function product_tax($product,$price)
    {
        $tax = 0;
        foreach ($product->taxes as $product_tax) {
            if ($product_tax->tax_type == 'percent') {
                $tax += ($price * $product_tax->tax) / 100;
            } elseif ($product_tax->tax_type == 'amount') {
                $tax += $product_tax->tax;
            }
        }
        return $tax;
    }
}
if (!function_exists('product_shipping_cost')) {
    function product_shipping_cost($product,$price=null)
    {
        $product_shipping_cost = 0;
        if($product->shipping_type == 'free'){
            $product_shipping_cost = 0;
        }else if($product->shipping_type == 'flat_rate') {
            $product_shipping_cost = $product->shipping_cost;
        }
        return $product_shipping_cost;
    }
}


