<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Only add columns if they don't exist
            if (!Schema::hasColumn('products', 'meta_specifications')) {
                $table->json('meta_specifications')->nullable()->comment('Additional specifications for enhanced product details');
            }
            
            if (!Schema::hasColumn('products', 'meta_features')) {
                $table->json('meta_features')->nullable()->comment('Detailed product features');
            }
            
            if (!Schema::hasColumn('products', 'meta_highlights')) {
                $table->json('meta_highlights')->nullable()->comment('Product highlights for marketing');
            }
            
            if (!Schema::hasColumn('products', 'meta_estimated_delivery')) {
                $table->string('meta_estimated_delivery')->nullable()->comment('Estimated delivery time');
            }
            
            if (!Schema::hasColumn('products', 'meta_return_policy')) {
                $table->string('meta_return_policy')->nullable()->comment('Return policy for this product');
            }
            
            if (!Schema::hasColumn('products', 'meta_free_shipping')) {
                $table->boolean('meta_free_shipping')->default(false)->comment('Whether this product has free shipping');
            }
            
            if (!Schema::hasColumn('products', 'meta_warranty')) {
                $table->string('meta_warranty')->nullable()->comment('Warranty information');
            }
            
            if (!Schema::hasColumn('products', 'meta_materials')) {
                $table->json('meta_materials')->nullable()->comment('Materials used in product');
            }
            
            if (!Schema::hasColumn('products', 'meta_dimensions')) {
                $table->json('meta_dimensions')->nullable()->comment('Product dimensions');
            }
            
            if (!Schema::hasColumn('products', 'meta_video_urls')) {
                $table->json('meta_video_urls')->nullable()->comment('Product video URLs');
            }
            
            if (!Schema::hasColumn('products', 'meta_faqs')) {
                $table->json('meta_faqs')->nullable()->comment('Product-specific FAQs');
            }
            
            if (!Schema::hasColumn('products', 'stock_alert')) {
                $table->string('stock_alert')->nullable()->comment('Stock alert message');
            }
            
            if (!Schema::hasColumn('products', 'certifications')) {
                $table->string('certifications')->nullable()->comment('Product certifications (comma separated)');
            }
            
            if (!Schema::hasColumn('products', 'packaging_type')) {
                $table->string('packaging_type')->nullable()->comment('Product packaging type');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'meta_specifications',
                'meta_features',
                'meta_highlights',
                'meta_estimated_delivery',
                'meta_return_policy',
                'meta_free_shipping',
                'meta_warranty',
                'meta_materials',
                'meta_dimensions',
                'meta_video_urls',
                'meta_faqs',
                'stock_alert',
                'certifications',
                'packaging_type'
            ]);
        });
    }
};
