<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\EmailVerify;
use App\Models\B2BProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Carbon\Carbon;

class EmailVerificationCompleteFlowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    /** @test */
    public function complete_customer_registration_flow_works()
    {
        // Step 1: Check email and send OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'exists' => false,
                        'otp_sent' => true
                    ]
                ]);

        // Verify OTP was created in database
        $emailVerify = EmailVerify::where('email', '<EMAIL>')->first();
        $this->assertNotNull($emailVerify);
        $this->assertEquals('customer', $emailVerify->user_type);
        $this->assertNotNull($emailVerify->otp);
        $this->assertNull($emailVerify->verified_at);

        // Step 2: Verify OTP
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => $emailVerify->otp
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Email verified successfully'
                ]);

        // Verify email verification was marked as verified
        $emailVerify->refresh();
        $this->assertNotNull($emailVerify->verified_at);

        // Step 3: Complete registration with auto-login
        $response = $this->postJson('/api/v3/auth/register', [
            'name' => 'John Customer',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer',
            'temp_user_id' => 'temp_123'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'user_type',
                            'email_verified_at'
                        ],
                        'token' => [
                            'access_token',
                            'token_type',
                            'expires_in'
                        ]
                    ]
                ])
                ->assertJson([
                    'message' => 'Registration successful. You have been automatically logged in.'
                ]);

        // Verify user was created correctly
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('John Customer', $user->name);
        $this->assertEquals('customer', $user->user_type);
        $this->assertNotNull($user->email_verified_at);
        $this->assertTrue(Hash::check('password123', $user->password));

        // Verify token was returned
        $responseData = $response->json('data');
        $this->assertNotNull($responseData['token']['access_token']);
        $this->assertEquals('Bearer', $responseData['token']['token_type']);
    }

    /** @test */
    public function complete_seller_registration_flow_works()
    {
        // Step 1: Check email and send OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'seller'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'exists' => false,
                        'otp_sent' => true
                    ]
                ]);

        $emailVerify = EmailVerify::where('email', '<EMAIL>')->first();
        $this->assertEquals('seller', $emailVerify->user_type);

        // Step 2: Verify OTP
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => $emailVerify->otp
        ]);

        $response->assertStatus(200);

        // Step 3: Complete seller registration
        $sellerData = [
            'name' => 'Jane Seller',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'business_name' => 'Jane\'s Electronics',
            'business_address' => [
                'street' => '123 Business St',
                'city' => 'Business City',
                'state' => 'Business State',
                'zipCode' => '12345',
                'country' => 'USA'
            ],
            'tax_id' => 'TAX123456',
            'business_type' => 'LLC',
            'website' => 'https://janeselectronics.com',
            'category' => 'Electronics',
            'temp_user_id' => 'temp_seller_456'
        ];

        $response = $this->postJson('/api/v3/auth/register/seller', $sellerData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'seller',
                        'token'
                    ]
                ]);

        // Verify user and seller profile were created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('seller', $user->user_type);
        $this->assertNotNull($user->email_verified_at);

        $seller = B2BProfile::where('user_id', $user->id)->first();
        $this->assertNotNull($seller);
        $this->assertEquals('Jane\'s Electronics', $seller->name);
        $this->assertEquals('LLC', $seller->business_type);
    }

    /** @test */
    public function complete_dropshipper_registration_flow_works()
    {
        // Step 1: Check email and send OTP
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'dropshipper'
        ]);

        $response->assertStatus(200);

        $emailVerify = EmailVerify::where('email', '<EMAIL>')->first();
        $this->assertEquals('dropshipper', $emailVerify->user_type);

        // Step 2: Verify OTP
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => $emailVerify->otp
        ]);

        $response->assertStatus(200);

        // Step 3: Complete dropshipper registration
        $dropshipperData = [
            'name' => 'Bob Dropshipper',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'company_name' => 'Bob\'s Dropship Co',
            'business_address' => [
                'street' => '456 Dropship Ave',
                'city' => 'Dropship City',
                'state' => 'Dropship State',
                'zipCode' => '67890',
                'country' => 'USA'
            ],
            'shipping_address' => [
                'street' => '789 Shipping Blvd',
                'city' => 'Shipping City',
                'state' => 'Shipping State',
                'zipCode' => '11111',
                'country' => 'USA'
            ],
            'tax_id' => 'TAX789',
            'business_type' => 'Corporation',
            'website' => 'https://bobsdropship.com',
            'user_type' => 'b2b',
            'temp_user_id' => 'temp_dropshipper_789'
        ];

        $response = $this->postJson('/api/v3/auth/register/dropshipper', $dropshipperData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'dropshipper',
                        'token'
                    ]
                ]);

        // Verify user and dropshipper profile were created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('b2b', $user->user_type);
        $this->assertNotNull($user->email_verified_at);

        $dropshipper = B2BProfile::where('user_id', $user->id)->first();
        $this->assertNotNull($dropshipper);
        $this->assertEquals('Bob\'s Dropship Co', $dropshipper->name);
        $this->assertEquals('Corporation', $dropshipper->business_type);
    }

    /** @test */
    public function existing_email_check_redirects_to_login()
    {
        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'email_verified_at' => now()
        ]);

        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'exists' => true,
                        'otp_sent' => false,
                        'user_type' => 'customer'
                    ],
                    'message' => 'Account already exists with this email. Please login instead.'
                ]);

        // Verify no OTP was created
        $emailVerify = EmailVerify::where('email', '<EMAIL>')->first();
        $this->assertNull($emailVerify);
    }

    /** @test */
    public function otp_resend_with_cooldown_works()
    {
        // Create initial email verification
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer',
            'resend_count' => 1,
            'last_resend_at' => now()->subMinutes(3) // 3 minutes ago
        ]);

        // Try to resend too soon (should fail)
        $response = $this->postJson('/api/v3/auth/resend-registration-otp', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'data' => [
                        'remaining_minutes' => 2,
                        'resend_count' => 1
                    ]
                ]);

        // Update last resend time to allow resend
        $emailVerify = EmailVerify::where('email', '<EMAIL>')->first();
        $emailVerify->update(['last_resend_at' => now()->subMinutes(6)]);

        // Now resend should work
        $response = $this->postJson('/api/v3/auth/resend-registration-otp', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'remaining_minutes' => 5,
                        'resend_count' => 2
                    ]
                ]);

        // Verify resend count was incremented
        $emailVerify->refresh();
        $this->assertEquals(2, $emailVerify->resend_count);
        $this->assertNotNull($emailVerify->last_resend_at);
    }

    /** @test */
    public function otp_verification_fails_with_expired_otp()
    {
        // Create expired OTP
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->subMinutes(1), // Expired 1 minute ago
            'user_type' => 'customer'
        ]);

        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => '123456'
        ]);

        $response->assertStatus(410)
                ->assertJson([
                    'success' => false,
                    'message' => 'OTP has expired. Please request a new one.'
                ]);
    }

    /** @test */
    public function otp_verification_fails_with_invalid_otp()
    {
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => '<EMAIL>',
            'otp' => '654321' // Wrong OTP
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid OTP'
                ]);
    }

    /** @test */
    public function registration_fails_without_email_verification()
    {
        $response = $this->postJson('/api/v3/auth/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer'
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Email not verified'
                ]);

        // Verify user was not created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNull($user);
    }

    /** @test */
    public function rate_limiting_prevents_too_many_email_checks()
    {
        $email = '<EMAIL>';

        // Make multiple requests rapidly
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
                'email' => $email,
                'user_type' => 'customer'
            ]);

            if ($i < 5) {
                $response->assertStatus(200);
            } else {
                // 6th request should be rate limited
                $response->assertStatus(429)
                        ->assertJson([
                            'success' => false,
                            'message' => 'Too many requests. Please try again later.'
                        ]);
            }
        }
    }

    /** @test */
    public function forgot_password_flow_integration_works()
    {
        // Create existing user
        $user = User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
            'user_type' => 'customer',
            'email_verified_at' => now()
        ]);

        // Step 1: Request password reset
        $response = $this->postJson('/api/v3/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password reset OTP sent to your email'
                ]);

        // Verify OTP was created
        $emailVerify = EmailVerify::where('email', '<EMAIL>')
                                 ->where('user_type', 'password_reset')
                                 ->first();
        $this->assertNotNull($emailVerify);

        // Step 2: Reset password with OTP
        $response = $this->postJson('/api/v3/auth/reset-password', [
            'email' => '<EMAIL>',
            'otp' => $emailVerify->otp,
            'new_password' => 'newpassword123'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password reset successful. Please login with your new password.'
                ]);

        // Verify password was changed
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));
        $this->assertFalse(Hash::check('oldpassword', $user->password));
    }

    /** @test */
    public function security_measures_prevent_enumeration_attacks()
    {
        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'email_verified_at' => now()
        ]);

        // Check existing email
        $response1 = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        // Check non-existing email
        $response2 = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);

        // Both responses should have similar timing and structure
        $this->assertEquals(200, $response1->status());
        $this->assertEquals(200, $response2->status());

        // Response structure should be consistent
        $response1->assertJsonStructure(['success', 'data', 'message']);
        $response2->assertJsonStructure(['success', 'data', 'message']);
    }

    /** @test */
    public function database_cleanup_removes_expired_records()
    {
        // Create expired email verification records
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->subHours(2),
            'user_type' => 'customer'
        ]);

        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '654321',
            'expires_at' => now()->subDays(1),
            'user_type' => 'seller'
        ]);

        // Create valid record
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '111111',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        // Run cleanup command
        $this->artisan('email-verification:cleanup');

        // Verify expired records were removed
        $this->assertDatabaseMissing('email_verifies', [
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseMissing('email_verifies', [
            'email' => '<EMAIL>'
        ]);

        // Verify valid record remains
        $this->assertDatabaseHas('email_verifies', [
            'email' => '<EMAIL>'
        ]);
    }
}