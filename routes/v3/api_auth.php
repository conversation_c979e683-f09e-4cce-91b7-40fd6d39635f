<?php

use App\Http\Controllers\Api\V3\Auth\ApiRegisterController;
use App\Http\Controllers\Api\V3\Auth\ApiResetPasswordController;
use App\Http\Controllers\Api\V3\Auth\AuthController;
use App\Http\Controllers\Api\V3\Auth\EmailVerificationController;
use App\Http\Controllers\Api\V3\Auth\MonitoringController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'v3/auth', 'middleware' => ['app_language']], function () {

    Route::post('check-email-send-otp', [EmailVerificationController::class, 'checkEmailAndSendOTP']);
    Route::post('verify-registration-otp', [EmailVerificationController::class, 'verifyOTP']);
    Route::post('resend-registration-otp', [EmailVerificationController::class, 'resendOTP'])
        ->middleware('email.verification.rate.limit');
    Route::post('check-verification-status', [EmailVerificationController::class, 'checkVerificationStatus']);


    // Email verification for registration - placing these at the top to ensure they're registered first

    // Test route to verify auth routes are loading
    Route::get('test', function() {
        return response()->json(['message' => 'Auth routes are working']);
    });

    // Test POST route to debug the issue
    Route::post('test-post', function() {
        return response()->json(['message' => 'POST route is working']);
    });

    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [ApiRegisterController::class, 'register']);
    Route::post('register/seller', [ApiRegisterController::class, 'register_seller']);
    Route::post('register/dropshipper', [ApiRegisterController::class, 'register_dropshipper']);
    Route::post('social-login', [AuthController::class, 'socialLogin']);
    Route::post('forgot-password', [ApiResetPasswordController::class,'forgetRequest']);
    Route::post('reset-password', [ApiResetPasswordController::class,'confirmReset']);
    Route::post('resend-reset-otp', [ApiResetPasswordController::class,'resendResetOTP']);

    // Monitoring endpoints (protected by admin middleware in production)
    Route::get('monitoring/health', [MonitoringController::class, 'getHealthMetrics']);
    Route::get('monitoring/daily-report', [MonitoringController::class, 'getDailyReport']);
    Route::get('monitoring/performance', [MonitoringController::class, 'getPerformanceMetrics']);
    Route::get('monitoring/security', [MonitoringController::class, 'getSecurityMetrics']);
    Route::post('monitoring/log-event', [MonitoringController::class, 'logEvent']);

    Route::middleware('api.auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('account-deletion', [AuthController::class, 'account_deletion']);
        Route::get('user', [AuthController::class, 'user']);
        Route::get('me', [AuthController::class, 'user']);
        Route::post('resend_code', [ApiRegisterController::class,'resendCode']);
        Route::post('confirm_code', [ApiRegisterController::class,'confirmCode']);
        Route::post('verify_otp', [ApiRegisterController::class,'verifyOtp']);
        Route::post('resend_otp', [ApiRegisterController::class,'resendOtp']);
    });
});
