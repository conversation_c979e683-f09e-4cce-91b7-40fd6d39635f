<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('return_request_infos', function (Blueprint $table) {
            if (!Schema::hasColumn('return_request_infos', 'pickupDetails')) {
                $table->json('pickupDetails')->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('return_request_infos', function (Blueprint $table) {
            $table->dropColumn('pickupDetails');
        });
    }
};
