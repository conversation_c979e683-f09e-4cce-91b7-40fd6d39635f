<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserOfferUsageTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_offer_usage', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('offer_id');
            $table->string('coupon_code')->unique();
            $table->integer('usage_count')->default(0);
            $table->integer('usage_limit')->default(1);
            $table->decimal('minimum_purchase_amount', 10, 2)->default(0.00);
            $table->decimal('discount_amount_used', 10, 2)->default(0.00);
            $table->decimal('total_savings', 10, 2)->default(0.00);
            $table->timestamp('first_used_at')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('usage_history')->nullable(); // Store each usage with timestamp and order details
            $table->timestamps();

            // Indexes (foreign keys will be added after table rename)
            $table->index(['user_id', 'offer_id']);
            $table->index('coupon_code');
            $table->index('is_active');
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_offer_usage');
    }
} 