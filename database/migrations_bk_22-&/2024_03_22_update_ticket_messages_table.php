<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ticket_messages', function (Blueprint $table) {
            // Drop the file_path column as we use attachments table
            if (Schema::hasColumn('ticket_messages', 'file_path')) {
                $table->dropColumn('file_path');
            }

            // Update user_role enum to match frontend requirements
            if (Schema::hasColumn('ticket_messages', 'user_role')) {
                $table->dropColumn('user_role');
            }
            if (!Schema::hasColumn('ticket_messages', 'user_role')) {
                $table->enum('user_role', ['customer', 'admin', 'dropshipper', 'seller'])->default('customer');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ticket_messages', function (Blueprint $table) {
            // Revert user_role changes
            if (Schema::hasColumn('ticket_messages', 'user_role')) {
                $table->dropColumn('user_role');
            }
            if (!Schema::hasColumn('ticket_messages', 'user_role')) {
                $table->enum('user_role', ['customer', 'admin', 'dropshipper', 'seller'])->default('customer')->after('content');
            }

            // Add back file_path column
            if (!Schema::hasColumn('ticket_messages', 'file_path')) {
                $table->string('file_path')->nullable()->after('content');
            }
        });
    }
}; 