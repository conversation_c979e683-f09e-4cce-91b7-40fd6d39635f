<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('user_notifications');
        Schema::create('user_notifications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('user_id');
            $table->string('notification_type')->index()->comment('Enum: order, product, system, commission, support, stock - See App\Enums\NotificationType');
            $table->string('type')->index();
            $table->unsignedBigInteger('subject_id');
            $table->string('subject_type')->index()->comment('App\Models\ModelName');
            $table->string('title');
            $table->text('message');
            $table->string('priority')->default('medium')->comment('Enum: high, medium, low - See App\Enums\NotificationPriority');
            $table->boolean('read')->default(false)->index();
            $table->string('link')->nullable();
            $table->string('link_text')->nullable();
            $table->json('data')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notifications');
    }
};
