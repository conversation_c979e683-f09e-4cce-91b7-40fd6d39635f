#!/bin/bash

# Build script with better error handling and options

set -e

echo "🔨 Building Buzfi Docker containers..."

# Navigate to docker directory
cd "$(dirname "$0")"

# Check available memory and adjust build accordingly
MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
echo "Available memory: ${MEMORY_GB}GB"

if [ "$MEMORY_GB" -lt 4 ]; then
    echo "⚠️ Low memory detected. Using single-threaded build..."
    export DOCKER_BUILDKIT=0
    docker compose build --no-cache --parallel=false
else
    echo "✅ Sufficient memory. Using optimized build..."
    export DOCKER_BUILDKIT=1
    docker compose build --no-cache
fi

echo "✅ Build completed successfully!"
