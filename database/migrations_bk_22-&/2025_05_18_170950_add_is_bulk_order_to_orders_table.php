<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'is_bulk_order')) {
                $table->boolean('is_bulk_order')->default(0)->after('delivery_status');
            }
            
            if (!Schema::hasColumn('orders', 'additional_info')) {
                $table->json('additional_info')->nullable()->after('notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            if (Schema::hasColumn('orders', 'is_bulk_order')) {
                $table->dropColumn('is_bulk_order');
            }
            
            if (Schema::hasColumn('orders', 'additional_info')) {
                $table->dropColumn('additional_info');
            }
        });
    }
}; 