<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::create('conversation_messages', function (Blueprint $table) {
                $table->id();
                //$table->foreignId('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                $table->foreignId('conversation_id');
                $table->foreignId('sender_id');
                //$table->unsignedInteger('sender_id');
                $table->text('content');
                $table->timestamps();
                $table->softDeletes();
                //$table->foreign('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                //$table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('conversation_messages');
    }
};
