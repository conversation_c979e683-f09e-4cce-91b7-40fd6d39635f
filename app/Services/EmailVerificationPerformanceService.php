<?php

namespace App\Services;

use App\Models\EmailVerify;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class EmailVerificationPerformanceService
{
    /**
     * Cache TTL for email existence checks (5 minutes)
     */
    const EMAIL_EXISTENCE_CACHE_TTL = 300;

    /**
     * Cache TTL for verification status (2 minutes)
     */
    const VERIFICATION_STATUS_CACHE_TTL = 120;

    /**
     * Cache TTL for OTP records (1 minute)
     */
    const OTP_RECORD_CACHE_TTL = 60;

    /**
     * Optimized email existence check with caching
     */
    public function checkEmailExistsOptimized(string $email): ?User
    {
        $cacheKey = "email_exists:" . hash('sha256', $email);

        //return Cache::remember($cacheKey, self::EMAIL_EXISTENCE_CACHE_TTL, function () use ($email) {
            // Use optimized query with proper indexing
            return User::select('id', 'email', 'user_type', 'email_verified_at')
                      ->where('email', $email)
                      ->first();
       // });
    }

    /**
     * Optimized verification status check with caching
     */
    public function getVerificationStatusOptimized(string $email): array
    {
        $cacheKey = "verification_status:" . hash('sha256', $email);

        return Cache::remember($cacheKey, self::VERIFICATION_STATUS_CACHE_TTL, function () use ($email) {
            $isVerified = EmailVerify::isEmailVerified($email);

            $data = [
                'email' => $email,
                'is_verified' => $isVerified
            ];

            if (!$isVerified) {
                $pendingRecord = EmailVerify::select([
                    'email', 'expire_time', 'resend_count', 'last_resend_at', 'created_at'
                ])
                ->where('email', $email)
                ->where('is_verify', false)
                ->whereNotNull('otp')
                ->orderBy('created_at', 'desc')
                ->first();

                if ($pendingRecord) {
                    $data['has_pending_otp'] = true;
                    $data['otp_expires_at'] = $pendingRecord->expire_time->toISOString();
                    $data['otp_remaining_minutes'] = $pendingRecord->remaining_time;
                    $data['is_otp_expired'] = $pendingRecord->isExpired();
                    $data['can_resend'] = $pendingRecord->canResend() && !$pendingRecord->isInCooldownPeriod();
                    $data['resend_count'] = $pendingRecord->resend_count;
                    $data['max_resend_attempts'] = 5;
                    $data['is_in_cooldown'] = $pendingRecord->isInCooldownPeriod();
                    $data['cooldown_remaining_minutes'] = $pendingRecord->getRemainingCooldownMinutes();
                    $data['next_resend_available_at'] = $pendingRecord->getNextResendTime()->toISOString();
                } else {
                    $data['has_pending_otp'] = false;
                    $data['can_resend'] = false;
                }
            }

            return $data;
        });
    }

    /**
     * Optimized OTP record retrieval with caching
     */
    public function getOTPRecordOptimized(string $email, string $otp): ?EmailVerify
    {
        $cacheKey = "otp_record:" . hash('sha256', $email . $otp);

        return Cache::remember($cacheKey, self::OTP_RECORD_CACHE_TTL, function () use ($email, $otp) {
            return EmailVerify::select([
                'id', 'email', 'otp', 'expire_time', 'is_verify', 'resend_count', 'last_resend_at'
            ])
            ->where('email', $email)
            ->where('otp', $otp)
            ->where('is_verify', false)
            ->where('expire_time', '>', Carbon::now())
            ->first();
        });
    }

    /**
     * Batch process OTP operations for better performance
     */
    public function batchProcessOTPOperations(): array
    {
        $results = [
            'expired_cleaned' => 0,
            'old_records_deleted' => 0,
            'resend_counts_reset' => 0,
            'cache_cleared' => 0
        ];

        DB::transaction(function () use (&$results) {
            // Clean expired OTPs in batches
            $results['expired_cleaned'] = EmailVerify::batchCleanupExpiredOTPs(1000);

            // Delete old unverified records
            $results['old_records_deleted'] = EmailVerify::batchDeleteOldRecords(1, 1000);

            // Reset expired resend counts
            $results['resend_counts_reset'] = EmailVerify::resetExpiredResendCounts();
        });

        // Clear related caches
        $results['cache_cleared'] = $this->clearExpiredCaches();

        return $results;
    }

    /**
     * Clear expired caches
     */
    public function clearExpiredCaches(): int
    {
        $cleared = 0;
        $driver = config('cache.default');

        if ($driver === 'redis') {
            // Get all cache keys that might be expired
            $patterns = [
                'email_exists:*',
                'verification_status:*',
                'otp_record:*'
            ];

            foreach ($patterns as $pattern) {
                try {
                    $keys = Cache::getRedis()->keys($pattern);
                    if (!empty($keys)) {
                        Cache::getRedis()->del($keys);
                        $cleared += count($keys);
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to clear cache pattern: ' . $pattern, ['error' => $e->getMessage()]);
                }
            }
        } else {
            // For file cache or other drivers, we can't easily clear by pattern
            // So we'll just flush the entire cache (be careful in production)
            try {
                Cache::flush();
                $cleared = 1; // Indicate that cache was cleared
                Log::info('Cache flushed due to non-Redis driver: ' . $driver);
            } catch (\Exception $e) {
                Log::warning('Failed to flush cache: ' . $e->getMessage());
            }
        }

        return $cleared;
    }

    /**
     * Invalidate specific email caches
     */
    public function invalidateEmailCaches(string $email): void
    {
        $emailHash = hash('sha256', $email);

        $cacheKeys = [
            "email_exists:{$emailHash}",
            "verification_status:{$emailHash}"
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Warm up cache for frequently accessed emails
     */
    public function warmUpEmailCaches(array $emails): int
    {
        $warmedUp = 0;

        foreach ($emails as $email) {
            try {
                // Pre-load email existence check
                $this->checkEmailExistsOptimized($email);

                // Pre-load verification status
                $this->getVerificationStatusOptimized($email);

                $warmedUp++;
            } catch (\Exception $e) {
                Log::warning('Failed to warm up cache for email', [
                    'email' => hash('sha256', $email),
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $warmedUp;
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $start = microtime(true);

        // Test cached email lookup
        $this->checkEmailExistsOptimized('<EMAIL>');
        $cachedEmailLookupTime = (microtime(true) - $start) * 1000;

        $start = microtime(true);

        // Test direct database lookup
        User::where('email', '<EMAIL>')->first();
        $directEmailLookupTime = (microtime(true) - $start) * 1000;

        $start = microtime(true);

        // Test cached verification status
        $this->getVerificationStatusOptimized('<EMAIL>');
        $cachedVerificationTime = (microtime(true) - $start) * 1000;

        // Get cache statistics
        $cacheStats = $this->getCacheStatistics();

        return [
            'cached_email_lookup_ms' => round($cachedEmailLookupTime, 2),
            'direct_email_lookup_ms' => round($directEmailLookupTime, 2),
            'cached_verification_ms' => round($cachedVerificationTime, 2),
            'cache_hit_improvement' => round(
                (($directEmailLookupTime - $cachedEmailLookupTime) / $directEmailLookupTime) * 100, 2
            ),
            'cache_statistics' => $cacheStats
        ];
    }

    /**
     * Get cache statistics
     */
    private function getCacheStatistics(): array
    {
        try {
            $driver = config('cache.default');

            if ($driver === 'redis') {
                $redis = Cache::getRedis();
                $info = $redis->info('memory');

                return [
                    'driver' => 'redis',
                    'used_memory' => $info['used_memory_human'] ?? 'N/A',
                    'used_memory_peak' => $info['used_memory_peak_human'] ?? 'N/A',
                    'keyspace_hits' => $redis->info('stats')['keyspace_hits'] ?? 0,
                    'keyspace_misses' => $redis->info('stats')['keyspace_misses'] ?? 0,
                    'hit_rate' => $this->calculateHitRate($redis)
                ];
            } else {
                // For file cache or other drivers
                return [
                    'driver' => $driver,
                    'used_memory' => 'N/A',
                    'used_memory_peak' => 'N/A',
                    'keyspace_hits' => 'N/A',
                    'keyspace_misses' => 'N/A',
                    'hit_rate' => 'N/A'
                ];
            }
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'error' => 'Unable to retrieve cache statistics',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate cache hit rate
     */
    private function calculateHitRate($redis): string
    {
        try {
            $stats = $redis->info('stats');
            $hits = $stats['keyspace_hits'] ?? 0;
            $misses = $stats['keyspace_misses'] ?? 0;
            $total = $hits + $misses;

            if ($total === 0) {
                return '0%';
            }

            return round(($hits / $total) * 100, 2) . '%';
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    /**
     * Optimize database queries with proper indexing
     */
    public function optimizeDatabaseQueries(): array
    {
        $optimizations = [];

        try {
            // Check if email index exists on users table
            $userIndexes = DB::select("SHOW INDEX FROM users WHERE Column_name = 'email'");
            if (empty($userIndexes)) {
                DB::statement("CREATE INDEX idx_users_email ON users(email)");
                $optimizations[] = 'Created email index on users table';
            }

            // Check if composite index exists on email_verify table
            $emailVerifyIndexes = DB::select("SHOW INDEX FROM email_verify WHERE Column_name IN ('email', 'otp', 'is_verify', 'expire_time')");

            $hasEmailIndex = false;
            $hasCompositeIndex = false;

            foreach ($emailVerifyIndexes as $index) {
                if ($index->Column_name === 'email' && $index->Key_name !== 'PRIMARY') {
                    $hasEmailIndex = true;
                }
                if (strpos($index->Key_name, 'composite') !== false) {
                    $hasCompositeIndex = true;
                }
            }

            if (!$hasEmailIndex) {
                DB::statement("CREATE INDEX idx_email_verify_email ON email_verify(email)");
                $optimizations[] = 'Created email index on email_verify table';
            }

            if (!$hasCompositeIndex) {
                DB::statement("CREATE INDEX idx_email_verify_composite ON email_verify(email, otp, is_verify, expire_time)");
                $optimizations[] = 'Created composite index on email_verify table';
            }

            // Create index for cleanup operations
            $cleanupIndexes = DB::select("SHOW INDEX FROM email_verify WHERE Key_name = 'idx_email_verify_cleanup'");
            if (empty($cleanupIndexes)) {
                DB::statement("CREATE INDEX idx_email_verify_cleanup ON email_verify(expire_time, is_verify, created_at)");
                $optimizations[] = 'Created cleanup index on email_verify table';
            }

        } catch (\Exception $e) {
            $optimizations[] = 'Error creating indexes: ' . $e->getMessage();
            Log::error('Database optimization failed', ['error' => $e->getMessage()]);
        }

        return $optimizations;
    }

    /**
     * Monitor query performance
     */
    public function monitorQueryPerformance(): array
    {
        $queries = [
            'email_existence' => "SELECT id, email, user_type FROM users WHERE email = '<EMAIL>'",
            'otp_verification' => "SELECT * FROM email_verify WHERE email = '<EMAIL>' AND otp = '123456' AND is_verify = 0 AND expire_time > NOW()",
            'verification_status' => "SELECT * FROM email_verify WHERE email = '<EMAIL>' AND is_verify = 1",
            'cleanup_expired' => "SELECT COUNT(*) FROM email_verify WHERE expire_time < NOW() AND is_verify = 0"
        ];

        $results = [];

        foreach ($queries as $name => $query) {
            $start = microtime(true);

            try {
                DB::select($query);
                $executionTime = (microtime(true) - $start) * 1000;
                $results[$name] = [
                    'execution_time_ms' => round($executionTime, 2),
                    'status' => 'success'
                ];
            } catch (\Exception $e) {
                $results[$name] = [
                    'execution_time_ms' => 0,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
