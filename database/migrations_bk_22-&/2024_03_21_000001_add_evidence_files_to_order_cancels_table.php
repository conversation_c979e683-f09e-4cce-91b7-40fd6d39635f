<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_cancels', function (Blueprint $table) {
            if (!Schema::hasColumn('order_cancels', 'evidence_files')) {
                $table->json('evidence_files')->nullable()->after('reason');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_cancels', function (Blueprint $table) {
            if (Schema::hasColumn('order_cancels', 'evidence_files')) {
                $table->dropColumn('evidence_files');
            }
        });
    }
}; 