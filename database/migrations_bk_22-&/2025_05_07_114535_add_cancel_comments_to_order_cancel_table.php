<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_cancels', function (Blueprint $table) {
            if (!Schema::hasColumn('order_cancels', 'cancel_comments')) {
                $table->text('cancel_comments')->nullable()->after('reason'); // adjust 'after' field as needed
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_cancel', function (Blueprint $table) {
            //
        });
    }
};
