<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('category_size_charts', function (Blueprint $table) {
            $table->id();

            //$table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
            $table->foreignId('category_id');

            //$table->foreignId('size_chart_id')->constrained('size_charts')->onDelete('cascade');
            $table->foreignId('size_chart_id');
            $table->timestamps();

            $table->unique(['category_id', 'size_chart_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('category_size_charts');
    }
};
