<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('stock_requests')) {
        Schema::create('stock_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id');
            $table->foreignId('user_id');
            $table->string('note')->nullable();
            $table->string('priority_level')->comment('low, medium, high')->nullable();
            $table->string('notification_email')->nullable();
            $table->string('notification_phone')->nullable();
            $table->string('notification_method')->comment('email, sms','push_notification','all')->nullable();
            $table->integer('quantity');
            $table->enum('status', ['pending', 'rejected', 'restock'])->default('pending');
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    public function down()
    {
        Schema::dropIfExists('stock_requests');
    }
};
