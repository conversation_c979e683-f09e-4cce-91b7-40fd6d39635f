<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPaykuPaymentFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payku_payments', function (Blueprint $table) {
            if (!Schema::hasColumn('payku_payments', 'payment_key')) {
                $table->string('payment_key')->nullable();
            }
            if (!Schema::hasColumn('payku_payments', 'transaction_key')) {
                $table->string('transaction_key')->nullable();
            }
            if (!Schema::hasColumn('payku_payments', 'deposit_date')) {
                $table->datetime('deposit_date')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payku_payments', function (Blueprint $table) {
            $table->dropColumn(['payment_key', 'transaction_key', 'deposit_date']);
        });
    }
} 