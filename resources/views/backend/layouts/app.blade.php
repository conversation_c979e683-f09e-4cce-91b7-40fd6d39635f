<!doctype html>
@if (\App\Models\Language::where('code', Session::get('locale', Config::get('app.locale')))->first()->rtl == 1)
    <html dir="rtl" lang="{{ str_replace('_', '-', app()->getLocale()) }}">
@else
    <html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
@endif

<head>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="app-url" content="{{ getBaseURL() }}">
    <meta name="file-base-url" content="{{ getFileBaseURL() }}">

    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Favicon -->
    <link rel="icon" href="{{ uploaded_asset(get_setting('site_icon')) }}">
    <link rel="apple-touch-icon" href="{{ uploaded_asset(get_setting('site_icon')) }}">
    <title>{{ get_setting('website_name') . ' | ' . get_setting('site_motto') }}</title>

    <!-- google font -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />

    <!-- aiz core css -->
    <link rel="stylesheet" href="{{ static_asset('assets/css/vendors.css') }}">
    @if (\App\Models\Language::where('code', Session::get('locale', Config::get('app.locale')))->first()->rtl == 1)
        <link rel="stylesheet" href="{{ static_asset('assets/css/bootstrap-rtl.min.css') }}">
    @endif
    <link rel="stylesheet" href="{{ static_asset('assets/css/aiz-core.css?v=') }}{{ rand(1000, 9999) }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        body {
            font-size: 12px;
        }
    </style>
    <script>
        var AIZ = AIZ || {};
        AIZ.local = {
            nothing_selected: '{!! translate('Nothing selected', null, true) !!}',
            nothing_found: '{!! translate('Nothing found', null, true) !!}',
            choose_file: '{{ translate('Choose file') }}',
            file_selected: '{{ translate('File selected') }}',
            files_selected: '{{ translate('Files selected') }}',
            add_more_files: '{{ translate('Add more files') }}',
            adding_more_files: '{{ translate('Adding more files') }}',
            drop_files_here_paste_or: '{{ translate('Drop files here, paste or') }}',
            browse: '{{ translate('Browse') }}',
            upload_complete: '{{ translate('Upload complete') }}',
            upload_paused: '{{ translate('Upload paused') }}',
            resume_upload: '{{ translate('Resume upload') }}',
            pause_upload: '{{ translate('Pause upload') }}',
            retry_upload: '{{ translate('Retry upload') }}',
            cancel_upload: '{{ translate('Cancel upload') }}',
            uploading: '{{ translate('Uploading') }}',
            processing: '{{ translate('Processing') }}',
            complete: '{{ translate('Complete') }}',
            file: '{{ translate('File') }}',
            files: '{{ translate('Files') }}',
        }
    </script>
    @yield('css')
</head>

<body class="">

    <div class="aiz-main-wrapper">
        @include('backend.inc.admin_sidenav')
        <div class="aiz-content-wrapper">
            @include('backend.inc.admin_nav')
            <div class="aiz-main-content">
                <div class="px-15px px-lg-25px">
                    @yield('content')
                </div>
                <div class="bg-white text-center py-3 px-15px px-lg-25px mt-auto">
                    <p class="mb-0">&copy; {{ get_setting('site_name') }} v{{ get_setting('current_version') }}</p>
                </div>
            </div><!-- .aiz-main-content -->
        </div><!-- .aiz-content-wrapper -->
    </div><!-- .aiz-main-wrapper -->

    @yield('modal')


    <script src="{{ static_asset('assets/js/vendors.js') }}"></script>
    <script src="{{ static_asset('assets/js/aiz-core.js?v=') }}{{ rand(1000, 9999) }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    @yield('script')

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle flash notifications
        @foreach (session('flash_notification', collect())->toArray() as $message)
            if (typeof AIZ !== 'undefined' && AIZ.plugins && AIZ.plugins.notify) {
                AIZ.plugins.notify('{{ $message['level'] }}', '{{ $message['message'] }}');
            }
        @endforeach

        // Replace jQuery tab functionality
        const dropdownMenuItems = document.querySelectorAll('.dropdown-menu a[data-toggle="tab"]');
        dropdownMenuItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                // Bootstrap tab functionality
                if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                    new bootstrap.Tab(this).show();
                }
            });
        });

        // Language change functionality
        const langChange = document.getElementById('lang-change');
        if (langChange) {
            const langLinks = langChange.querySelectorAll('.dropdown-menu a');
            langLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.getAttribute('data-flag');
                    if (lang) {
                        // Handle language change via form submission or AJAX
                        fetch('{{ route('language.change') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify({
                                _token: '{{ csrf_token() }}',
                                locale: lang
                            })
                        }).then(() => {
                            location.reload();
                        }).catch(() => {
                            // Fallback to href navigation
                            window.location.href = this.href;
                        });
                    }
                });
            });
        }

        // Search functionality
        const menuSearch = document.getElementById('menu-search');
        if (menuSearch) {
            menuSearch.addEventListener('input', function() {
                const filter = this.value.toUpperCase();
                const mainMenu = document.getElementById('main-menu');
                const searchMenu = document.getElementById('search-menu');
                const items = mainMenu.querySelectorAll('a');

                if (filter) {
                    const filteredItems = Array.from(items).filter(function(item) {
                        const textElement = item.querySelector('.aiz-side-nav-text');
                        return textElement && textElement.innerText.toUpperCase().indexOf(filter) > -1 && 
                               item.hasAttribute('href') && item.getAttribute('href') !== '#';
                    });

                    if (filteredItems.length === 0) {
                        mainMenu.classList.add('d-none');
                        searchMenu.innerHTML = '<li class="aiz-side-nav-item"><span class="text-center text-muted d-block">{{ translate("Nothing Found") }}</span></li>';
                    } else {
                        mainMenu.classList.add('d-none');
                        let searchHTML = '';
                        filteredItems.forEach(function(item) {
                            const textElement = item.querySelector('.aiz-side-nav-text');
                            const text = textElement ? textElement.innerText : '';
                            const link = item.getAttribute('href');
                            if (text && link) {
                                searchHTML += `<li class="aiz-side-nav-item"><a href="${link}" class="aiz-side-nav-link"><i class="las la-ellipsis-h aiz-side-nav-icon"></i><span>${text}</span></a></li>`;
                            }
                        });
                        
                        searchMenu.innerHTML = searchHTML;
                    }
                } else {
                    mainMenu.classList.remove('d-none');
                    searchMenu.innerHTML = '';
                }
            });
        }
    });

    // Global menuSearch function for compatibility
    function menuSearch() {
        const menuSearchInput = document.getElementById('menu-search');
        if (menuSearchInput) {
            menuSearchInput.dispatchEvent(new Event('input'));
        }
    }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    
    @if(Auth::check() && Auth::user()->user_type == 'admin')
    <div id="admin-messaging-widget">
        <div class="admin-messaging-button" id="adminMessagingButton">
            <i class="las la-comment"></i>
            <span class="message-count" id="adminUnreadCount">0</span>
        </div>
        
        <div class="admin-messaging-panel" id="adminMessagingPanel">
            <div class="admin-messaging-header">
                <h4>{{ translate('Messages') }}</h4>
                <button class="close-button" id="adminMessagingCloseBtn">
                    <i class="las la-times"></i>
                </button>
            </div>
            
            <div class="admin-messaging-tabs">
                <button class="tab-button active" id="conversationsTabBtn">{{ translate('Chats') }}</button>
                <button class="tab-button" id="contactsTabBtn">{{ translate('Contacts') }}</button>
            </div>
            
            <div class="admin-messaging-search">
                <div class="input-group">
                    <input type="text" class="form-control" id="adminMessageSearch" placeholder="{{ translate('Search') }}...">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button">
                            <i class="las la-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="conversationsTab">
                <div class="conversation-list" id="adminConversationList">
                    <!-- Conversations will be loaded here -->
                    <div class="loading-indicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">{{ translate('Loading') }}...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-content d-none" id="contactsTab">
                <div class="contact-list" id="adminContactList">
                    <!-- Contacts will be loaded here -->
                    <div class="loading-indicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">{{ translate('Loading') }}...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="conversation-area d-none" id="adminConversationArea">
                <div class="conversation-header">
                    <button class="back-button" id="adminBackToListBtn">
                        <i class="las la-arrow-left"></i>
                    </button>
                    <div class="user-info">
                        <img src="{{ static_asset('assets/img/placeholder.jpg') }}" id="adminConversationUserImg" alt="User">
                        <div>
                            <h5 id="adminConversationUserName"></h5>
                            <small id="adminConversationUserStatus"></small>
                        </div>
                    </div>
                </div>
                
                <div class="messages-container" id="adminMessagesContainer">
                    <!-- Messages will be loaded here -->
                    <div class="loading-indicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">{{ translate('Loading') }}...</span>
                        </div>
                    </div>
                </div>
                
                <div class="message-input">
                    <form id="adminMessageForm">
                        <div class="input-group">
                            <input type="text" class="form-control" id="adminMessageInput" placeholder="{{ translate('Type a message') }}...">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="las la-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
    .admin-messaging-widget {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1050;
    }

    .admin-messaging-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #3B5998;
        color: white;
        border: none;
        font-size: 24px;
        position: fixed;
        bottom: 30px;
        right: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        z-index: 1050;
    }

    .admin-messaging-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    .message-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: #e41e3f;
        color: white;
        border-radius: 50%;
        min-width: 22px;
        height: 22px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        border: 2px solid #fff;
    }

    .admin-messaging-panel {
        position: fixed;
        bottom: 100px;
        right: 30px;
        width: 360px;
        height: 600px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.2);
        display: none;
        flex-direction: column;
        overflow: hidden;
        z-index: 1040;
        transition: all 0.3s ease;
    }

    .admin-messaging-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
        background-color: #3B5998;
        color: white;
    }

    .admin-messaging-header h4 {
        margin: 0;
        font-weight: bold;
        font-size: 18px;
    }

    .close-button {
        background: transparent;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.2s;
    }

    .close-button:hover {
        opacity: 1;
    }

    .admin-messaging-tabs {
        display: flex;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
    }

    .tab-button {
        flex: 1;
        background: none;
        border: none;
        padding: 12px 0;
        font-weight: bold;
        color: #777;
        cursor: pointer;
        transition: all 0.2s;
    }

    .tab-button.active {
        color: #3B5998;
        border-bottom: 3px solid #3B5998;
        background-color: #fff;
    }

    .admin-messaging-search {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .tab-content {
        flex: 1;
        overflow-y: auto;
    }

    .conversation-list, .contact-list {
        padding: 10px 0;
    }

    .conversation-item, .contact-item {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        cursor: pointer;
        transition: background-color 0.2s;
        border-bottom: 1px solid #f5f5f5;
    }

    .conversation-item:hover, .contact-item:hover {
        background-color: #f5f7fa;
    }

    .conversation-item.unread {
        background-color: #e7f3ff;
    }

    .conversation-item img, .contact-item img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 15px;
        object-fit: cover;
    }

    .conversation-info, .contact-info {
        flex: 1;
        overflow: hidden;
    }

    .conversation-name, .contact-name {
        font-weight: bold;
        margin-bottom: 3px;
        font-size: 14px;
    }

    .conversation-last-message {
        color: #777;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .conversation-time {
        font-size: 12px;
        color: #999;
        white-space: nowrap;
    }

    .conversation-unread {
        min-width: 20px;
        height: 20px;
        background-color: #3B5998;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
        margin-left: 5px;
    }

    .conversation-area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        display: flex;
        flex-direction: column;
    }

    .conversation-header {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
        background-color: #3B5998;
        color: white;
    }

    .back-button {
        background: transparent;
        border: none;
        color: white;
        font-size: 18px;
        margin-right: 15px;
        cursor: pointer;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-info img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        object-fit: cover;
        border: 2px solid rgba(255,255,255,0.5);
    }

    .user-info h5 {
        margin: 0;
        font-weight: bold;
        font-size: 16px;
    }

    .user-info small {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        display: flex;
        flex-direction: column;
        background-color: #f0f2f5;
    }

    .message {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 18px;
        margin-bottom: 10px;
        word-break: break-word;
        position: relative;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .message-received {
        background-color: white;
        color: #333;
        align-self: flex-start;
        border-bottom-left-radius: 5px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .message-sent {
        background-color: #0084ff;
        color: white;
        align-self: flex-end;
        border-bottom-right-radius: 5px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .message-time {
        font-size: 11px;
        margin-top: 5px;
        text-align: right;
        opacity: 0.7;
    }

    .message-input {
        padding: 15px;
        border-top: 1px solid #eee;
        background-color: white;
    }

    .message-input .form-control {
        border-radius: 30px;
        padding-left: 20px;
        padding-right: 20px;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .message-input .btn {
        border-radius: 30px;
        margin-left: 10px;
        background-color: #0084ff;
        border-color: #0084ff;
        padding-left: 20px;
        padding-right: 20px;
    }

    .loading-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        height: 100%;
    }

    .empty-state {
        text-align: center;
        padding: 30px;
        color: #777;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #ddd;
    }

    /* Typing indicator */
    .typing-indicator {
        background-color: #E6E7ED;
        will-change: transform;
        width: auto;
        border-radius: 50px;
        padding: 10px;
        display: table;
        margin: 0 auto;
        position: relative;
        animation: 2s bulge infinite ease-out;
        align-self: flex-start;
    }

    .typing-indicator::before,
    .typing-indicator::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: -2px;
        height: 10px;
        width: 10px;
        border-radius: 50%;
        background-color: #E6E7ED;
    }
    .typing-indicator::after {
        height: 5px;
        width: 5px;
        left: -5px;
        bottom: -5px;
    }
    .typing-indicator span {
        height: 8px;
        width: 8px;
        float: left;
        margin: 0 1px;
        background-color: #9E9EA1;
        display: block;
        border-radius: 50%;
        opacity: 0.4;
    }
    .typing-indicator span:nth-of-type(1) {
        animation: 1s blink infinite 0.3333s;
    }
    .typing-indicator span:nth-of-type(2) {
        animation: 1s blink infinite 0.6666s;
    }
    .typing-indicator span:nth-of-type(3) {
        animation: 1s blink infinite 0.9999s;
    }

    @keyframes blink {
        50% {
            opacity: 1;
        }
    }
    @keyframes bulge {
        50% {
            transform: scale(1.05);
        }
    }

    /* Mobile responsiveness */
    @media (max-width: 576px) {
        .admin-messaging-panel {
            width: 100%;
            height: 100%;
            bottom: 0;
            right: 0;
            border-radius: 0;
        }
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables
        const messagingButton = document.getElementById('adminMessagingButton');
        const messagingPanel = document.getElementById('adminMessagingPanel');
        const closeButton = document.getElementById('adminMessagingCloseBtn');
        const conversationsTabBtn = document.getElementById('conversationsTabBtn');
        const contactsTabBtn = document.getElementById('contactsTabBtn');
        const conversationsTab = document.getElementById('conversationsTab');
        const contactsTab = document.getElementById('contactsTab');
        const conversationArea = document.getElementById('adminConversationArea');
        const backButton = document.getElementById('adminBackToListBtn');
        const messageForm = document.getElementById('adminMessageForm');
        const messageInput = document.getElementById('adminMessageInput');
        const messageSearch = document.getElementById('adminMessageSearch');
        const conversationList = document.getElementById('adminConversationList');
        const contactList = document.getElementById('adminContactList');
        const messagesContainer = document.getElementById('adminMessagesContainer');
        const unreadCountElement = document.getElementById('adminUnreadCount');
        
        // State
        let currentConversation = null;
        let conversations = [];
        let contacts = [];
        let messages = [];
        let unreadCount = 0;
        
        // Event Listeners
        messagingButton.addEventListener('click', toggleMessagingPanel);
        closeButton.addEventListener('click', toggleMessagingPanel);
        conversationsTabBtn.addEventListener('click', () => switchTab('conversations'));
        contactsTabBtn.addEventListener('click', () => switchTab('contacts'));
        backButton.addEventListener('click', backToList);
        messageForm.addEventListener('submit', sendMessage);
        messageSearch.addEventListener('input', handleSearch);
        
        // Initialize messaging widget
        loadUnreadCount();
        
        // Functions
        function toggleMessagingPanel() {
            if (messagingPanel.style.display === 'flex') {
                messagingPanel.style.display = 'none';
            } else {
                messagingPanel.style.display = 'flex';
                loadConversations();
            }
        }
        
        function switchTab(tab) {
            if (tab === 'conversations') {
                conversationsTab.classList.remove('d-none');
                contactsTab.classList.add('d-none');
                conversationsTabBtn.classList.add('active');
                contactsTabBtn.classList.remove('active');
                loadConversations();
            } else {
                conversationsTab.classList.add('d-none');
                contactsTab.classList.remove('d-none');
                conversationsTabBtn.classList.remove('active');
                contactsTabBtn.classList.add('active');
                loadContacts();
            }
        }
        
        function loadUnreadCount() {
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/unread-counts')
                .then(response => response.json())
                .then(data => {
                    unreadCount = data.data?.total || 0;
                    unreadCountElement.textContent = unreadCount;
                    unreadCountElement.style.display = unreadCount > 0 ? 'flex' : 'none';
                })
                .catch(error => console.error('Error loading unread count:', error));
                
            // Poll for updates every 30 seconds
            setTimeout(loadUnreadCount, 30000);
        }
        
        function loadConversations() {
            conversationList.innerHTML = `
                <div class="loading-indicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            `;
            
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/conversations')
                .then(response => response.json())
                .then(data => {
                    conversations = data.conversations || [];
                    renderConversations();
                })
                .catch(error => {
                    console.error('Error loading conversations:', error);
                    conversationList.innerHTML = `
                        <div class="empty-state">
                            <i class="las la-comment-slash"></i>
                            <p>Failed to load conversations</p>
                        </div>
                    `;
                });
        }
        
        function renderConversations() {
            if (conversations.length === 0) {
                conversationList.innerHTML = `
                    <div class="empty-state">
                        <i class="las la-comments"></i>
                        <p>No conversations yet</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            conversations.forEach(conversation => {
                const participant = conversation.participant;
                const lastMessage = conversation.last_message;
                const unreadCount = conversation.unread_count || 0;
                
                html += `
                    <div class="conversation-item ${unreadCount > 0 ? 'unread' : ''}" data-id="${conversation.id}">
                        <img src="${participant.avatar || '{{ static_asset('assets/img/placeholder.jpg') }}'}" alt="${participant.name}">
                        <div class="conversation-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="conversation-name">${participant.name}</div>
                                <div class="conversation-time">${formatTime(lastMessage?.created_at)}</div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="conversation-last-message">${lastMessage?.content || 'No messages yet'}</div>
                                ${unreadCount > 0 ? `<div class="conversation-unread">${unreadCount}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            conversationList.innerHTML = html;
            
            // Add event listeners to conversation items
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.addEventListener('click', () => {
                    const id = item.dataset.id;
                    const conversation = conversations.find(c => c.id == id);
                    openConversation(conversation);
                });
            });
        }
        
        function loadContacts() {
            contactList.innerHTML = `
                <div class="loading-indicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            `;
            
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/contacts')
                .then(response => response.json())
                .then(data => {
                    contacts = data.contacts || [];
                    renderContacts();
                })
                .catch(error => {
                    console.error('Error loading contacts:', error);
                    contactList.innerHTML = `
                        <div class="empty-state">
                            <i class="las la-user-slash"></i>
                            <p>Failed to load contacts</p>
                        </div>
                    `;
                });
        }
        
        function renderContacts() {
            if (contacts.length === 0) {
                contactList.innerHTML = `
                    <div class="empty-state">
                        <i class="las la-users"></i>
                        <p>No contacts found</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            contacts.forEach(contact => {
                html += `
                    <div class="contact-item" data-id="${contact.id}">
                        <img src="${contact.avatar || '{{ static_asset('assets/img/placeholder.jpg') }}'}" alt="${contact.name}">
                        <div class="contact-info">
                            <div class="contact-name">${contact.name}</div>
                            <div class="contact-type">${contact.type || 'User'}</div>
                        </div>
                    </div>
                `;
            });
            
            contactList.innerHTML = html;
            
            // Add event listeners to contact items
            document.querySelectorAll('.contact-item').forEach(item => {
                item.addEventListener('click', () => {
                    const id = item.dataset.id;
                    startConversation(id);
                });
            });
        }
        
        function openConversation(conversation) {
            currentConversation = conversation;
            
            // Update UI
            document.getElementById('adminConversationUserName').textContent = conversation.participant.name;
            document.getElementById('adminConversationUserImg').src = conversation.participant.avatar || '{{ static_asset('assets/img/placeholder.jpg') }}';
            document.getElementById('adminConversationUserStatus').textContent = conversation.participant.user_type || 'User';
            
            // Show conversation area
            conversationsTab.classList.add('d-none');
            contactsTab.classList.add('d-none');
            conversationArea.classList.remove('d-none');
            
            // Load messages
            loadMessages();
            
            // Mark as read if there are unread messages
            if (conversation.unread_count > 0) {
                markAsRead(conversation.id);
            }
        }
        
        function loadMessages() {
            if (!currentConversation) return;
            
            messagesContainer.innerHTML = `
                <div class="loading-indicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            `;
            
            fetch(`/buzfi-n-main/buzfi-backend-new/api/v3/messaging/conversations/${currentConversation.id}`)
                .then(response => response.json())
                .then(data => {
                    messages = data.messages || [];
                    renderMessages();
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    messagesContainer.innerHTML = `
                        <div class="empty-state">
                            <i class="las la-comment-slash"></i>
                            <p>Failed to load messages</p>
                        </div>
                    `;
                });
        }
        
        function renderMessages() {
            if (messages.length === 0) {
                messagesContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="las la-comments"></i>
                        <p>No messages yet</p>
                        <p>Send a message to start the conversation</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            messages.forEach(message => {
                const isAdmin = message.user_id === {{ Auth::id() }};
                
                html += `
                    <div class="message ${isAdmin ? 'message-sent' : 'message-received'}">
                        ${message.content}
                        <div class="message-time">${formatTime(message.created_at)}</div>
                    </div>
                `;
            });
            
            messagesContainer.innerHTML = html;
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function sendMessage(event) {
            event.preventDefault();
            
            const content = messageInput.value.trim();
            if (!content || !currentConversation) return;
            
            const messageData = {
                conversation_id: currentConversation.id,
                content: content
            };
            
            messageInput.value = '';
            
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(messageData)
            })
                .then(response => response.json())
                .then(data => {
                    // Refresh messages
                    loadMessages();
                    
                    // Refresh conversations list
                    loadConversations();
                })
                .catch(error => console.error('Error sending message:', error));
        }
        
        function startConversation(contact) {
            const messageData = {
                recipient_id: contact.id,
                content: messageInput.value.trim() || 'Hello!'
            };
            
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(messageData)
            })
                .then(response => response.json())
                .then(data => {
                    // Switch to conversations tab
                    switchTab('conversations');
                    
                    // Refresh conversations
                    loadConversations();
                })
                .catch(error => console.error('Error starting conversation:', error));
        }
        
        function markAsRead(conversationId) {
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/messages/mark-read', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    conversation_id: conversationId
                })
            })
                .then(() => {
                    // Update unread count
                    loadUnreadCount();
                    
                    // Update conversation in list
                    const conversation = conversations.find(c => c.id == conversationId);
                    if (conversation) {
                        conversation.unread_count = 0;
                    }
                })
                .catch(error => console.error('Error marking as read:', error));
        }
        
        function backToList() {
            currentConversation = null;
            conversationArea.classList.add('d-none');
            conversationsTab.classList.remove('d-none');
            
            // Refresh conversations
            loadConversations();
        }
        
        function handleSearch(event) {
            const query = event.target.value.trim().toLowerCase();
            
            if (conversationsTab.classList.contains('d-none')) {
                // Search contacts
                const filteredContacts = contacts.filter(contact => 
                    contact.name.toLowerCase().includes(query) ||
                    (contact.email && contact.email.toLowerCase().includes(query))
                );
                
                renderFilteredContacts(filteredContacts);
            } else {
                // Search conversations
                const filteredConversations = conversations.filter(conversation => 
                    conversation.participant.name.toLowerCase().includes(query)
                );
                
                renderFilteredConversations(filteredConversations);
            }
        }
        
        function renderFilteredContacts(filteredContacts) {
            if (filteredContacts.length === 0) {
                contactList.innerHTML = `
                    <div class="empty-state">
                        <i class="las la-search"></i>
                        <p>No contacts found matching your search</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            filteredContacts.forEach(contact => {
                html += `
                    <div class="contact-item" data-id="${contact.id}">
                        <img src="${contact.avatar || '{{ static_asset('assets/img/placeholder.jpg') }}'}" alt="${contact.name}">
                        <div class="contact-info">
                            <div class="contact-name">${contact.name}</div>
                            <div class="contact-type">${contact.type || 'User'}</div>
                        </div>
                    </div>
                `;
            });
            
            contactList.innerHTML = html;
            
            // Add event listeners to contact items
            document.querySelectorAll('.contact-item').forEach(item => {
                item.addEventListener('click', () => {
                    const id = item.dataset.id;
                    startConversation(id);
                });
            });
        }
        
        function renderFilteredConversations(filteredConversations) {
            if (filteredConversations.length === 0) {
                conversationList.innerHTML = `
                    <div class="empty-state">
                        <i class="las la-search"></i>
                        <p>No conversations found matching your search</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            filteredConversations.forEach(conversation => {
                const participant = conversation.participant;
                const lastMessage = conversation.last_message;
                const unreadCount = conversation.unread_count || 0;
                
                html += `
                    <div class="conversation-item ${unreadCount > 0 ? 'unread' : ''}" data-id="${conversation.id}">
                        <img src="${participant.avatar || '{{ static_asset('assets/img/placeholder.jpg') }}'}" alt="${participant.name}">
                        <div class="conversation-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="conversation-name">${participant.name}</div>
                                <div class="conversation-time">${formatTime(lastMessage?.created_at)}</div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="conversation-last-message">${lastMessage?.content || 'No messages yet'}</div>
                                ${unreadCount > 0 ? `<div class="conversation-unread">${unreadCount}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            conversationList.innerHTML = html;
            
            // Add event listeners to conversation items
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.addEventListener('click', () => {
                    const id = item.dataset.id;
                    const conversation = filteredConversations.find(c => c.id == id);
                    openConversation(conversation);
                });
            });
        }
        
        function formatTime(timestamp) {
            if (!timestamp) return '';
            
            const date = new Date(timestamp);
            const now = new Date();
            
            // Today, show time only
            if (date.toDateString() === now.toDateString()) {
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
            
            // Yesterday
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            if (date.toDateString() === yesterday.toDateString()) {
                return 'Yesterday';
            }
            
            // This week, show day name
            const weekAgo = new Date(now);
            weekAgo.setDate(now.getDate() - 7);
            if (date > weekAgo) {
                return date.toLocaleDateString([], { weekday: 'short' });
            }
            
            // Older, show date
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
        
        // Real-time messaging with Pusher
        if (window.Echo) {
            window.Echo.private(`user.{{ Auth::id() }}.conversations`)
                .listen('.message.new', (data) => {
                    // If we're viewing the conversation that received a message
                    if (currentConversation && currentConversation.id === data.conversation_id) {
                        // Add the message to the current view
                        messages.push({
                            id: data.message.id,
                            content: data.message.content,
                            user_id: data.message.sender_id,
                            created_at: data.message.created_at
                        });
                        renderMessages();
                        
                        // Mark as read
                        markAsRead(data.conversation_id);
                    } else {
                        // Update unread count
                        loadUnreadCount();
                        
                        // Play notification sound
                        const audio = new Audio('{{ static_asset('assets/sounds/notification.mp3') }}');
                        audio.play().catch(e => console.log('Auto-play prevented:', e));
                        
                        // Show browser notification
                        if ('Notification' in window && Notification.permission === 'granted') {
                            const notification = new Notification('New Message', {
                                body: data.message.content,
                                icon: '{{ static_asset('assets/img/logo.png') }}'
                            });
                            
                            notification.onclick = function() {
                                window.focus();
                                messagingPanel.style.display = 'flex';
                                loadConversations();
                            };
                        }
                        
                        // Refresh conversations if panel is open
                        if (messagingPanel.style.display === 'flex' && conversationsTab.classList.contains('d-none') === false) {
                            loadConversations();
                        }
                    }
                });
        }
        
        // Request notification permission
        if ('Notification' in window && Notification.permission !== 'granted' && Notification.permission !== 'denied') {
            Notification.requestPermission();
        }
    });
    </script>
    @endif
</body>

</html>
