<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->boolean('is_top_tier')->default(false)->after('is_early_access');
            $table->boolean('is_limited_offer')->default(false)->after('is_top_tier');
            $table->timestamp('offer_start_date')->nullable()->after('is_limited_offer');
            $table->timestamp('offer_end_date')->nullable()->after('offer_start_date');
            $table->decimal('margin', 8, 2)->default(0)->after('offer_end_date')->comment('Profit margin percentage');
            $table->decimal('original_price', 10, 2)->nullable()->after('margin')->comment('Original price before discount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'is_top_tier',
                'is_limited_offer',
                'offer_start_date',
                'offer_end_date',
                'margin',
                'original_price'
            ]);
        });
    }
};
