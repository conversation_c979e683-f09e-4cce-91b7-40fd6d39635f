<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => [
        'api/*',
        'api/v3/*',
        'sanctum/csrf-cookie'
    ],

    'allowed_methods' => ['*'],

    'allowed_origins' => [
        'http://localhost:3000',
        'https://localhost:3000',
        'http://localhost/buzfi-n-main/buzfi',
        'https://buzfi.com',
        'https://www.buzfi.com',
        'http://buzfi.com',
        'http://www.buzfi.com',
        'http://127.0.0.1:3000',
        'https://127.0.0.1:3000',
    ],

    'allowed_origins_patterns' => [
        '/^https?:\/\/(localhost|127\.0\.0\.1):\d+$/',
        '/^https?:\/\/.*\.buzfi\.com$/',
    ],

    'allowed_headers' => [
        'Accept',
        'Authorization',
        'Content-Type',
        'X-Requested-With',
        'X-CSRF-TOKEN',
        'Origin',
        'X-Api-Key',
        'X-Cart-Id',
        'Cache-Control',
        'Pragma',
        'Expires',
    ],

    'exposed_headers' => [
        'Authorization',
        'X-Pagination-Count',
        'X-Pagination-Page',
        'X-Pagination-Limit',
    ],

    'max_age' => 86400,

    'supports_credentials' => true,

];
