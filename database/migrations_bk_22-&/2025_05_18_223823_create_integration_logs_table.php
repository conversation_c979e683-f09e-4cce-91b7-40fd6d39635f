<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIntegrationLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('integration_logs')) {
            Schema::create('integration_logs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('integration_id');
                $table->string('event');
                $table->enum('status', ['success', 'error', 'warning', 'info']);
                $table->string('message');
            $table->longText('details')->nullable();
            $table->timestamp('timestamp');
            $table->timestamps();
            
            $table->foreign('integration_id')->references('id')->on('integrations')->onDelete('cascade');
            $table->index('integration_id');
                $table->index(['integration_id', 'event']);
                $table->index(['integration_id', 'status']);
                $table->index('timestamp');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('integration_logs');
    }
}
