<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class FeatureFlagService
{
    /**
     * Cache key prefix for feature flags
     */
    const CACHE_PREFIX = 'feature_flag:';
    
    /**
     * Cache TTL for feature flags (5 minutes)
     */
    const CACHE_TTL = 300;

    /**
     * Default feature flags configuration
     */
    protected $defaultFlags = [
        'email_verification_auth' => [
            'enabled' => true,
            'rollout_percentage' => 100,
            'user_types' => ['customer', 'seller', 'dropshipper'],
            'environments' => ['production', 'staging', 'development'],
            'description' => 'New email verification and authentication system'
        ],
        'auto_login_after_registration' => [
            'enabled' => true,
            'rollout_percentage' => 100,
            'user_types' => ['customer', 'seller', 'dropshipper'],
            'environments' => ['production', 'staging', 'development'],
            'description' => 'Automatic login after successful registration'
        ],
        'enhanced_security_monitoring' => [
            'enabled' => true,
            'rollout_percentage' => 100,
            'user_types' => ['customer', 'seller', 'dropshipper'],
            'environments' => ['production', 'staging', 'development'],
            'description' => 'Enhanced security monitoring and logging'
        ],
        'performance_optimizations' => [
            'enabled' => true,
            'rollout_percentage' => 100,
            'user_types' => ['customer', 'seller', 'dropshipper'],
            'environments' => ['production', 'staging', 'development'],
            'description' => 'Performance optimizations for email verification'
        ],
        'legacy_auth_fallback' => [
            'enabled' => false,
            'rollout_percentage' => 0,
            'user_types' => ['customer', 'seller', 'dropshipper'],
            'environments' => ['production', 'staging', 'development'],
            'description' => 'Fallback to legacy authentication system'
        ]
    ];

    /**
     * Check if a feature flag is enabled
     */
    public function isEnabled(string $flagName, array $context = []): bool
    {
        $cacheKey = self::CACHE_PREFIX . $flagName;
        
        $flag = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($flagName) {
            return $this->getFlagConfiguration($flagName);
        });

        if (!$flag || !$flag['enabled']) {
            return false;
        }

        // Check environment
        $currentEnv = app()->environment();
        if (!in_array($currentEnv, $flag['environments'])) {
            return false;
        }

        // Check user type if provided
        if (isset($context['user_type']) && !in_array($context['user_type'], $flag['user_types'])) {
            return false;
        }

        // Check rollout percentage
        if ($flag['rollout_percentage'] < 100) {
            $userId = $context['user_id'] ?? null;
            $email = $context['email'] ?? null;
            
            if (!$this->isInRollout($flagName, $flag['rollout_percentage'], $userId, $email)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get feature flag configuration
     */
    public function getFlagConfiguration(string $flagName): ?array
    {
        // First check environment configuration
        $envFlags = config('features.flags', []);
        if (isset($envFlags[$flagName])) {
            return array_merge($this->defaultFlags[$flagName] ?? [], $envFlags[$flagName]);
        }

        // Fall back to default configuration
        return $this->defaultFlags[$flagName] ?? null;
    }

    /**
     * Update feature flag configuration
     */
    public function updateFlag(string $flagName, array $configuration): bool
    {
        try {
            // Update cache
            $cacheKey = self::CACHE_PREFIX . $flagName;
            Cache::put($cacheKey, $configuration, self::CACHE_TTL);

            // Log the change
            Log::info('Feature flag updated', [
                'flag_name' => $flagName,
                'configuration' => $configuration,
                'updated_by' => auth()->user()->id ?? 'system',
                'timestamp' => now()->toISOString()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update feature flag: ' . $e->getMessage(), [
                'flag_name' => $flagName,
                'configuration' => $configuration
            ]);
            return false;
        }
    }

    /**
     * Disable a feature flag (emergency rollback)
     */
    public function emergencyDisable(string $flagName, string $reason = ''): bool
    {
        try {
            $flag = $this->getFlagConfiguration($flagName);
            if (!$flag) {
                return false;
            }

            $flag['enabled'] = false;
            $flag['emergency_disabled'] = true;
            $flag['disabled_reason'] = $reason;
            $flag['disabled_at'] = now()->toISOString();

            $this->updateFlag($flagName, $flag);

            // Log emergency disable
            Log::critical('Feature flag emergency disabled', [
                'flag_name' => $flagName,
                'reason' => $reason,
                'disabled_by' => auth()->user()->id ?? 'system',
                'timestamp' => now()->toISOString()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to emergency disable feature flag: ' . $e->getMessage(), [
                'flag_name' => $flagName,
                'reason' => $reason
            ]);
            return false;
        }
    }

    /**
     * Get all feature flags status
     */
    public function getAllFlags(): array
    {
        $flags = [];
        
        foreach (array_keys($this->defaultFlags) as $flagName) {
            $flags[$flagName] = [
                'configuration' => $this->getFlagConfiguration($flagName),
                'is_enabled' => $this->isEnabled($flagName),
                'cache_key' => self::CACHE_PREFIX . $flagName
            ];
        }

        return $flags;
    }

    /**
     * Clear all feature flag caches
     */
    public function clearCache(): bool
    {
        try {
            foreach (array_keys($this->defaultFlags) as $flagName) {
                Cache::forget(self::CACHE_PREFIX . $flagName);
            }

            Log::info('Feature flag cache cleared');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear feature flag cache: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user/email is in rollout percentage
     */
    protected function isInRollout(string $flagName, int $percentage, ?int $userId = null, ?string $email = null): bool
    {
        // Use user ID or email hash to determine rollout
        $identifier = $userId ?? ($email ? hash('crc32', $email) : random_int(1, 100));
        
        // Convert to percentage (0-99)
        $userPercentile = abs($identifier) % 100;
        
        return $userPercentile < $percentage;
    }

    /**
     * Gradual rollout helper - increase rollout percentage
     */
    public function increaseRollout(string $flagName, int $newPercentage): bool
    {
        $flag = $this->getFlagConfiguration($flagName);
        if (!$flag) {
            return false;
        }

        $currentPercentage = $flag['rollout_percentage'];
        
        if ($newPercentage <= $currentPercentage) {
            Log::warning('Attempted to decrease rollout percentage', [
                'flag_name' => $flagName,
                'current' => $currentPercentage,
                'new' => $newPercentage
            ]);
            return false;
        }

        $flag['rollout_percentage'] = min($newPercentage, 100);
        
        return $this->updateFlag($flagName, $flag);
    }

    /**
     * Check if email verification auth is enabled for user
     */
    public function isEmailVerificationAuthEnabled(array $context = []): bool
    {
        return $this->isEnabled('email_verification_auth', $context);
    }

    /**
     * Check if auto-login after registration is enabled
     */
    public function isAutoLoginEnabled(array $context = []): bool
    {
        return $this->isEnabled('auto_login_after_registration', $context);
    }

    /**
     * Check if enhanced security monitoring is enabled
     */
    public function isSecurityMonitoringEnabled(array $context = []): bool
    {
        return $this->isEnabled('enhanced_security_monitoring', $context);
    }

    /**
     * Check if performance optimizations are enabled
     */
    public function isPerformanceOptimizationsEnabled(array $context = []): bool
    {
        return $this->isEnabled('performance_optimizations', $context);
    }

    /**
     * Check if legacy auth fallback is enabled
     */
    public function isLegacyAuthFallbackEnabled(array $context = []): bool
    {
        return $this->isEnabled('legacy_auth_fallback', $context);
    }
}