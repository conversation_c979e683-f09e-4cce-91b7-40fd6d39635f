# Buzfi Laravel Project - Docker Setup

This document provides comprehensive instructions for running the Buzfi Laravel project using Docker.

## 📋 Prerequisites

Before starting, ensure you have the following installed on your system:

- **Docker** (version 20.10 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Make** (for using shortcuts)
- **Git** (for version control)

### Installation Links:
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## 🚀 Quick Start

### Method 1: Using the Start Script (Easiest)
```bash
./docker/start.sh
```

### Method 2: Using Make Commands (Recommended)
```bash
make setup
```
This command will:
- Build all Docker containers
- Start all services
- Install Laravel dependencies
- Import buzfi.sql database (if exists)
- Set up the application

### Method 3: Manual Setup
If you prefer step-by-step setup:

```bash
# Build containers
make build

# Start services
make up

# Install dependencies
make install

# Import database (if buzfi.sql exists)
make import-db
```

### Stopping the Project
```bash
# Using stop script
./docker/stop.sh

# Using make command
make down
```

## 🌐 Access URLs

After starting the services, you can access:

| Service | URL | Description |
|---------|-----|-------------|
| **Main Application** | http://localhost/buzfi-new-backend | Laravel application |
| **phpMyAdmin** | http://localhost:8080 | Database management |
| **Redis Commander** | http://localhost:8081 | Redis management |

### Database Credentials for phpMyAdmin:
- **Server**: mysql
- **Username**: root
- **Password**: root_password
- **Database**: buzfi

## 📁 Project Structure

```
buzfi-new-backend/
├── docker/                    # Docker configuration files
│   ├── docker-compose.yml     # Main Docker Compose file
│   ├── nginx/                 # Nginx configuration
│   ├── php/                   # PHP-FPM configuration
│   ├── mysql/                 # MySQL configuration
│   └── redis/                 # Redis configuration
├── import_database/           # Database import files
│   ├── 01-init.sql           # Database initialization
│   └── buzfi.sql             # Main database dump
├── Makefile                   # Shortcuts for Docker commands
└── DOCKER_README.md          # This documentation
```

## 🛠️ Available Commands

### Basic Operations
```bash
make help          # Show all available commands
make up            # Start all services
make down          # Stop all services
make restart       # Restart all services
make status        # Show container status
make logs          # Show logs from all containers
```

### Development Commands
```bash
make install       # Install Laravel dependencies
make import-db     # Import buzfi.sql database if it exists
make migrate       # Run database migrations (if not using SQL import)
make seed          # Run database seeders
make fresh         # Fresh migration with seeders
make artisan cmd="command"  # Run artisan commands
make composer cmd="command" # Run composer commands
```

### Container Access
```bash
make shell         # Access PHP container shell
make shell-nginx   # Access Nginx container shell
make shell-mysql   # Access MySQL container shell
make mysql         # Access MySQL CLI
make redis-cli     # Access Redis CLI
```

### Maintenance
```bash
make clean         # Clean Docker resources
make clean-all     # Clean everything including images
make backup-db     # Backup database
make restore-db file="backup.sql"  # Restore database
```

## 🗄️ Database Management

### Automatic Database Import
The `buzfi.sql` file in the `import_database/` folder is automatically imported during the setup process if it exists. The database `buzfi` is created automatically.

### Manual Database Operations
```bash
# Access MySQL CLI
make mysql

# Import buzfi.sql database
make import-db

# Backup database
make backup-db

# Restore from backup
make restore-db file="buzfi_backup_20240101_120000.sql"

# Run migrations (if not using SQL import)
make migrate

# Fresh migration
make migrate-fresh
```

## 🔧 Configuration Details

### Services Configuration

#### Nginx
- **Port**: 80
- **Document Root**: `/var/www/html/public`
- **Configuration**: `docker/nginx/default.conf`

#### PHP-FPM
- **Version**: PHP 8.1
- **Extensions**: All Laravel required extensions
- **Configuration**: `docker/php/php.ini`

#### MySQL
- **Version**: 8.0
- **Port**: 3306
- **Database**: buzfi
- **Root Password**: root_password
- **User**: buzfi_user
- **Password**: buzfi_password

#### Redis
- **Version**: 7-alpine
- **Port**: 6379
- **Configuration**: `docker/redis/redis.conf`

#### phpMyAdmin
- **Port**: 8080
- **Access**: http://localhost:8080

#### Redis Commander
- **Port**: 8081
- **Access**: http://localhost:8081

## 🐛 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
sudo lsof -i :80
sudo lsof -i :3306
sudo lsof -i :8080

# Stop conflicting services
sudo systemctl stop apache2
sudo systemctl stop mysql
```

#### 2. Permission Issues
```bash
# Fix storage permissions
make artisan cmd="storage:link"
sudo chmod -R 775 storage bootstrap/cache
```

#### 3. Database Connection Issues
```bash
# Check MySQL container
make logs-mysql

# Restart MySQL
docker restart buzfi_mysql

# Check database credentials in .env file
make shell
cat .env | grep DB_
```

#### 4. Composer Issues
```bash
# Clear composer cache
make composer cmd="clear-cache"

# Reinstall dependencies
make composer cmd="install --no-cache"
```

### Container Management
```bash
# Check container status
make status

# View specific container logs
make logs-app      # PHP application logs
make logs-nginx    # Nginx logs
make logs-mysql    # MySQL logs

# Restart specific container
docker restart buzfi_nginx
docker restart buzfi_php
docker restart buzfi_mysql
```

## 🔄 Development Workflow

### Daily Development
```bash
# Start development environment
make dev-up

# Stop development environment
make dev-down

# Monitor logs during development
make monitor
```

### Code Changes
- PHP code changes are reflected immediately (no restart needed)
- Nginx configuration changes require: `docker restart buzfi_nginx`
- Environment changes require: `make restart`

### Database Changes
```bash
# Create new migration
make artisan cmd="make:migration create_new_table"

# Run migrations
make migrate

# Rollback migration
make artisan cmd="migrate:rollback"
```

## 📊 Monitoring

### Resource Usage
```bash
# Show container resource usage
make top

# Show detailed container information
docker inspect buzfi_php
docker inspect buzfi_mysql
```

### Logs
```bash
# Follow all logs
make logs

# Follow specific service logs
make logs-app
make logs-nginx
make logs-mysql
```

## 🔒 Security Notes

- Default passwords are used for development. Change them for production.
- The application runs on HTTP. Use HTTPS for production.
- Database is accessible from host for development. Restrict access for production.

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Review container logs: `make logs`
3. Verify all services are running: `make status`
4. Try restarting services: `make restart`

For additional help, check the Laravel documentation or Docker documentation.
