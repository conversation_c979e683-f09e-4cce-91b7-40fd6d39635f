<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_sizes', function (Blueprint $table) {
            $table->id();
            //$table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('product_id');
            $table->foreignId('size_id');
            //$table->foreignId('size_id')->constrained('sizes')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['product_id', 'size_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_sizes');
    }
};
