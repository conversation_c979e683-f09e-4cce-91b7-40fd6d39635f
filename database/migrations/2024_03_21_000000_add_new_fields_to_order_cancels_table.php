<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //if (Schema::hasTable('order_cancels')) {
            Schema::table('order_cancels', function (Blueprint $table) {
                if (!Schema::hasColumn('order_cancels', 'processing_notes')) {
                    $table->text('processing_notes')->nullable();
            }
            if (!Schema::hasColumn('order_cancels', 'refund_amount')) {
                $table->decimal('refund_amount', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('order_cancels', 'refund_method')) {
                $table->string('refund_method', 50)->nullable();
            }
            if (!Schema::hasColumn('order_cancels', 'processed_by')) {
                $table->unsignedInteger('processed_by')->nullable();
                $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('order_cancels', 'processed_date')) {
                    $table->timestamp('processed_date')->nullable();
                }
            });
       // }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_cancels', function (Blueprint $table) {
            $table->dropColumn([
                'processing_notes',
                'refund_amount',
                'refund_method',
                'processed_date'
            ]);
            $table->dropForeign(['processed_by']);
            $table->dropColumn('processed_by');
        });
    }
};
