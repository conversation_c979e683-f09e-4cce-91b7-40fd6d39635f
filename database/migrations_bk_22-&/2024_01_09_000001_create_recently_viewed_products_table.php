<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('recently_viewed_products')) {
        Schema::create('recently_viewed_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->timestamp('viewed_at');
            $table->unsignedInteger('view_count')->default(0);
            $table->timestamps();

            // Add unique constraint to prevent duplicate entries
            $table->unique(['user_id', 'product_id']);
        });
        }
    }

    public function down()
    {
        Schema::dropIfExists('recently_viewed_products');
    }
};
