<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First check if the table exists
        if (!Schema::hasTable('cart_items')) {
            Schema::create('cart_items', function (Blueprint $table) {
                $table->id();
                $table->foreignId('cart_info_id')->references('id')->on('cart_infos')->onDelete('cascade');
                $table->foreignId('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->string('temp_user_id')->nullable();
                $table->foreignId('product_id')->references('id')->on('products')->onDelete('cascade');
                $table->text('variation')->nullable();
                $table->decimal('price', 10, 2)->default(0);
                $table->decimal('tax', 10, 2)->default(0);
                $table->decimal('shipping_cost', 10, 2)->default(0);
                $table->string('shipping_type')->nullable();
                $table->integer('pickup_point')->nullable();
                $table->integer('carrier_id')->nullable();
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('product_referral_code')->nullable();
                $table->string('coupon_code')->nullable();
                $table->boolean('coupon_applied')->default(false);
                $table->integer('quantity')->default(1);
                $table->timestamps();
            });
        } else {
            Schema::table('cart_items', function (Blueprint $table) {
                // Add cart_info_id column if it doesn't exist
                if (!Schema::hasColumn('cart_items', 'cart_info_id')) {
                    $table->uuid('cart_info_id')->after('id');
                }
                
                // Add foreign key if it doesn't exist
                if (!Schema::hasColumn('cart_items', 'user_id')) {
                    $table->foreignId('user_id')->nullable()->after('cart_info_id')->constrained()->onDelete('cascade');
                }
                
                if (!Schema::hasColumn('cart_items', 'product_id')) {
                    $table->foreignId('product_id')->after('user_id')->constrained()->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Don't drop the table as it may contain important data
    }
};
