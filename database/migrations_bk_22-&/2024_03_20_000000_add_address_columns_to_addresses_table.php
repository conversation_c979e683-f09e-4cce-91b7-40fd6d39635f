<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAddressColumnsToAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('addresses', function (Blueprint $table) {
            if (!Schema::hasColumn('addresses', 'street_address')) {
                $table->string('street_address', 255)->nullable()->after('address');
            }
            if (!Schema::hasColumn('addresses', 'apartment_address')) {
                $table->string('apartment_address', 255)->nullable()->after('street_address');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropColumn(['street_address', 'apartment_address']);
        });
    }
}