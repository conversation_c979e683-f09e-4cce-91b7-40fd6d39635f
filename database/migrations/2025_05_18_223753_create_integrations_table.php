<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIntegrationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       // if (!Schema::hasTable('integrations')) {
            Schema::create('integrations', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('type');
                $table->string('provider');
                $table->string('status');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('authType');
            $table->string('iconUrl')->nullable();
            $table->longText('authData')->nullable();
            $table->longText('settings')->nullable();
            $table->timestamp('connectedAt')->nullable();
            $table->timestamp('lastSyncedAt')->nullable();
            $table->timestamp('expiresAt')->nullable();
            $table->longText('metadata')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->index(['user_id', 'provider']);
                $table->index(['user_id', 'type']);
                $table->index('status');
            });
        //}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('integrations');
    }
}
