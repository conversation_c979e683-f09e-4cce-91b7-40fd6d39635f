<?php

namespace Tests\Unit;

use App\Services\EmailVerificationDatabaseService;
use App\Services\EmailVerificationSecurityService;
use App\Models\User;
use App\Models\EmailVerify;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use Carbon\Carbon;

class EmailVerificationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $databaseService;
    protected $securityService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->databaseService = new EmailVerificationDatabaseService();
        $this->securityService = new EmailVerificationSecurityService();
        Mail::fake();
    }

    /** @test */
    public function database_service_can_check_if_email_exists()
    {
        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'email_verified_at' => now()
        ]);

        $result = $this->databaseService->checkEmailExists('<EMAIL>');
        $this->assertTrue($result['exists']);
        $this->assertEquals('customer', $result['user_type']);

        $result = $this->databaseService->checkEmailExists('<EMAIL>');
        $this->assertFalse($result['exists']);
        $this->assertNull($result['user_type']);
    }

    /** @test */
    public function database_service_can_create_email_verification_record()
    {
        $email = '<EMAIL>';
        $userType = 'customer';

        $result = $this->databaseService->createEmailVerification($email, $userType);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['otp']);
        $this->assertEquals(6, strlen($result['otp']));
        $this->assertIsNumeric($result['otp']);

        // Verify database record
        $this->assertDatabaseHas('email_verifies', [
            'email' => $email,
            'user_type' => $userType,
            'otp' => $result['otp']
        ]);

        $emailVerify = EmailVerify::where('email', $email)->first();
        $this->assertNotNull($emailVerify->expires_at);
        $this->assertTrue($emailVerify->expires_at->isFuture());
    }

    /** @test */
    public function database_service_can_verify_otp()
    {
        $email = '<EMAIL>';
        $otp = '123456';

        // Create email verification record
        EmailVerify::create([
            'email' => $email,
            'otp' => $otp,
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        // Valid OTP verification
        $result = $this->databaseService->verifyOTP($email, $otp);
        $this->assertTrue($result['valid']);
        $this->assertNull($result['error']);

        // Verify record was marked as verified
        $emailVerify = EmailVerify::where('email', $email)->first();
        $this->assertNotNull($emailVerify->verified_at);

        // Try to verify again (should fail - single use)
        $result = $this->databaseService->verifyOTP($email, $otp);
        $this->assertFalse($result['valid']);
        $this->assertEquals('OTP already used', $result['error']);
    }

    /** @test */
    public function database_service_rejects_expired_otp()
    {
        $email = '<EMAIL>';
        $otp = '123456';

        // Create expired email verification record
        EmailVerify::create([
            'email' => $email,
            'otp' => $otp,
            'expires_at' => now()->subMinutes(1), // Expired
            'user_type' => 'customer'
        ]);

        $result = $this->databaseService->verifyOTP($email, $otp);
        $this->assertFalse($result['valid']);
        $this->assertEquals('OTP expired', $result['error']);
    }

    /** @test */
    public function database_service_rejects_invalid_otp()
    {
        $email = '<EMAIL>';

        // Create email verification record
        EmailVerify::create([
            'email' => $email,
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        $result = $this->databaseService->verifyOTP($email, '654321'); // Wrong OTP
        $this->assertFalse($result['valid']);
        $this->assertEquals('Invalid OTP', $result['error']);
    }

    /** @test */
    public function database_service_can_handle_otp_resend()
    {
        $email = '<EMAIL>';

        // Create initial email verification record
        $emailVerify = EmailVerify::create([
            'email' => $email,
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer',
            'resend_count' => 1,
            'last_resend_at' => now()->subMinutes(6) // 6 minutes ago
        ]);

        $result = $this->databaseService->handleOTPResend($email);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['new_otp']);
        $this->assertEquals(2, $result['resend_count']);

        // Verify database was updated
        $emailVerify->refresh();
        $this->assertEquals($result['new_otp'], $emailVerify->otp);
        $this->assertEquals(2, $emailVerify->resend_count);
        $this->assertNotNull($emailVerify->last_resend_at);
    }

    /** @test */
    public function database_service_enforces_resend_cooldown()
    {
        $email = '<EMAIL>';

        // Create email verification record with recent resend
        EmailVerify::create([
            'email' => $email,
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer',
            'resend_count' => 1,
            'last_resend_at' => now()->subMinutes(3) // 3 minutes ago (within 5-minute cooldown)
        ]);

        $result = $this->databaseService->handleOTPResend($email);

        $this->assertFalse($result['success']);
        $this->assertEquals('Resend cooldown active', $result['error']);
        $this->assertEquals(2, $result['remaining_minutes']);
    }

    /** @test */
    public function database_service_enforces_max_resend_attempts()
    {
        $email = '<EMAIL>';

        // Create email verification record with max resends
        EmailVerify::create([
            'email' => $email,
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer',
            'resend_count' => 5, // Max attempts reached
            'last_resend_at' => now()->subMinutes(6)
        ]);

        $result = $this->databaseService->handleOTPResend($email);

        $this->assertFalse($result['success']);
        $this->assertEquals('Maximum resend attempts reached', $result['error']);
    }

    /** @test */
    public function database_service_can_check_verification_status()
    {
        $email = '<EMAIL>';

        // Unverified email
        EmailVerify::create([
            'email' => $email,
            'otp' => '123456',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        $result = $this->databaseService->checkVerificationStatus($email);
        $this->assertFalse($result['verified']);
        $this->assertNull($result['verified_at']);

        // Verified email
        $emailVerify = EmailVerify::where('email', $email)->first();
        $emailVerify->update(['verified_at' => now()]);

        $result = $this->databaseService->checkVerificationStatus($email);
        $this->assertTrue($result['verified']);
        $this->assertNotNull($result['verified_at']);
    }

    /** @test */
    public function database_service_can_cleanup_expired_records()
    {
        // Create expired records
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'expires_at' => now()->subHours(2),
            'user_type' => 'customer'
        ]);

        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '654321',
            'expires_at' => now()->subDays(1),
            'user_type' => 'seller'
        ]);

        // Create valid record
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '111111',
            'expires_at' => now()->addMinutes(10),
            'user_type' => 'customer'
        ]);

        $deletedCount = $this->databaseService->cleanupExpiredRecords();

        $this->assertEquals(2, $deletedCount);

        // Verify expired records were removed
        $this->assertDatabaseMissing('email_verifies', [
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseMissing('email_verifies', [
            'email' => '<EMAIL>'
        ]);

        // Verify valid record remains
        $this->assertDatabaseHas('email_verifies', [
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function security_service_can_generate_secure_otp()
    {
        $otp = $this->securityService->generateOTP();

        $this->assertEquals(6, strlen($otp));
        $this->assertIsNumeric($otp);
        $this->assertGreaterThanOrEqual(100000, (int)$otp);
        $this->assertLessThanOrEqual(999999, (int)$otp);
    }

    /** @test */
    public function security_service_generates_unique_otps()
    {
        $otps = [];
        for ($i = 0; $i < 100; $i++) {
            $otps[] = $this->securityService->generateOTP();
        }

        // All OTPs should be unique (very high probability)
        $uniqueOtps = array_unique($otps);
        $this->assertGreaterThan(95, count($uniqueOtps)); // Allow for small chance of collision
    }

    /** @test */
    public function security_service_can_check_rate_limits()
    {
        $email = '<EMAIL>';
        $action = 'email_check';

        // First few attempts should be allowed
        for ($i = 0; $i < 5; $i++) {
            $result = $this->securityService->checkRateLimit($email, $action);
            $this->assertTrue($result['allowed']);
            $this->securityService->recordAttempt($email, $action);
        }

        // 6th attempt should be rate limited
        $result = $this->securityService->checkRateLimit($email, $action);
        $this->assertFalse($result['allowed']);
        $this->assertGreaterThan(0, $result['retry_after']);
    }

    /** @test */
    public function security_service_can_record_and_track_attempts()
    {
        $email = '<EMAIL>';
        $action = 'otp_verification';

        // Record multiple attempts
        for ($i = 0; $i < 3; $i++) {
            $this->securityService->recordAttempt($email, $action);
        }

        $attempts = $this->securityService->getAttemptCount($email, $action);
        $this->assertEquals(3, $attempts);
    }

    /** @test */
    public function security_service_can_validate_email_format()
    {
        $validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        $invalidEmails = [
            'invalid-email',
            '@example.com',
            'test@',
            '<EMAIL>',
            'test@example',
            ''
        ];

        foreach ($validEmails as $email) {
            $this->assertTrue($this->securityService->validateEmailFormat($email));
        }

        foreach ($invalidEmails as $email) {
            $this->assertFalse($this->securityService->validateEmailFormat($email));
        }
    }

    /** @test */
    public function security_service_can_sanitize_input()
    {
        $maliciousInputs = [
            '<script>alert("xss")</script><EMAIL>',
            '<EMAIL>\'; DROP TABLE users; --',
            '<EMAIL><img src=x onerror=alert(1)>',
            '<EMAIL>" onmouseover="alert(1)"'
        ];

        foreach ($maliciousInputs as $input) {
            $sanitized = $this->securityService->sanitizeInput($input);
            $this->assertStringNotContainsString('<script>', $sanitized);
            $this->assertStringNotContainsString('DROP TABLE', $sanitized);
            $this->assertStringNotContainsString('onerror', $sanitized);
            $this->assertStringNotContainsString('onmouseover', $sanitized);
        }
    }

    /** @test */
    public function security_service_can_detect_suspicious_patterns()
    {
        $suspiciousEmails = [
            'test+' . str_repeat('a', 100) . '@example.com', // Very long local part
            'test@' . str_repeat('sub.', 50) . 'example.com', // Many subdomains
            '<EMAIL>' . str_repeat('.', 10), // Multiple dots
        ];

        $normalEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($suspiciousEmails as $email) {
            $this->assertTrue($this->securityService->detectSuspiciousPattern($email));
        }

        foreach ($normalEmails as $email) {
            $this->assertFalse($this->securityService->detectSuspiciousPattern($email));
        }
    }

    /** @test */
    public function security_service_can_hash_and_verify_otp()
    {
        $otp = '123456';
        $hashedOtp = $this->securityService->hashOTP($otp);

        $this->assertNotEquals($otp, $hashedOtp);
        $this->assertTrue($this->securityService->verifyOTP($otp, $hashedOtp));
        $this->assertFalse($this->securityService->verifyOTP('654321', $hashedOtp));
    }

    /** @test */
    public function security_service_can_generate_secure_tokens()
    {
        $token = $this->securityService->generateSecureToken();

        $this->assertIsString($token);
        $this->assertGreaterThan(32, strlen($token));

        // Generate multiple tokens to ensure uniqueness
        $tokens = [];
        for ($i = 0; $i < 10; $i++) {
            $tokens[] = $this->securityService->generateSecureToken();
        }

        $uniqueTokens = array_unique($tokens);
        $this->assertEquals(10, count($uniqueTokens));
    }

    /** @test */
    public function security_service_can_log_security_events()
    {
        $email = '<EMAIL>';
        $event = 'suspicious_activity';
        $details = ['ip' => '***********', 'user_agent' => 'Test Agent'];

        // This should not throw any exceptions
        $this->securityService->logSecurityEvent($email, $event, $details);

        // Verify event was logged (assuming logging is implemented)
        $this->assertTrue(true); // Placeholder assertion
    }

    /** @test */
    public function security_service_can_check_ip_reputation()
    {
        $goodIp = '*******'; // Google DNS
        $suspiciousIp = '***********'; // Private IP

        $goodResult = $this->securityService->checkIPReputation($goodIp);
        $suspiciousResult = $this->securityService->checkIPReputation($suspiciousIp);

        $this->assertIsBool($goodResult['is_safe']);
        $this->assertIsBool($suspiciousResult['is_safe']);
        $this->assertIsArray($goodResult);
        $this->assertIsArray($suspiciousResult);
    }

    /** @test */
    public function security_service_can_implement_progressive_delays()
    {
        $email = '<EMAIL>';

        // First attempt - no delay
        $delay1 = $this->securityService->calculateProgressiveDelay($email, 1);
        $this->assertEquals(0, $delay1);

        // Second attempt - small delay
        $delay2 = $this->securityService->calculateProgressiveDelay($email, 2);
        $this->assertGreaterThan(0, $delay2);
        $this->assertLessThan(5, $delay2);

        // Fifth attempt - longer delay
        $delay5 = $this->securityService->calculateProgressiveDelay($email, 5);
        $this->assertGreaterThan($delay2, $delay5);
        $this->assertLessThan(60, $delay5);
    }
}