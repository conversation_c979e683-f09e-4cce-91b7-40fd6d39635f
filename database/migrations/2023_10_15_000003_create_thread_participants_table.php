<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Then create tables with foreign keys

            /*Schema::create('thread_participants', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('thread_id');
                $table->unsignedInteger('user_id');
                $table->string('role')->default('member');
                $table->timestamps();
                $table->softDeletes();
                $table->unique(['thread_id', 'user_id']);
                $table->foreign('thread_id')->references('id')->on('message_threads')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });*/
        Schema::create('thread_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('thread_id');
            $table->foreignId('user_id');
            $table->string('role')->default('member');
            $table->timestamps();
            $table->softDeletes();
            $table->unique(['thread_id', 'user_id']);
        });


    }

    public function down(): void
    {
        Schema::dropIfExists('thread_participants');
    }
};
