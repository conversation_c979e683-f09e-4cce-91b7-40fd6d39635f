<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('wallet_transactions')) {
            Schema::create('wallet_transactions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('wallet_id');//->constrained('wallets')->onDelete('cascade');
                $table->foreignId('user_id');//->constrained('users')->onDelete('cascade');
                $table->enum('type', ['credit', 'debit']);
                $table->decimal('amount', 10, 2);
                $table->string('balance_type')->default('balance')->comment('balance, reward_points, promotional_credits');
                $table->string('source')->nullable()->comment('Source of the transaction');
                $table->text('description')->nullable();
                $table->string('reference_type')->nullable()->comment('order, refund, admin, reward, promotion');
                $table->string('reference_id')->nullable();
                $table->foreignId('added_by')->nullable();//->constrained('users')->onDelete('set null');  // Use foreignId() for consistency
                $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved');
                $table->json('metadata')->nullable();
                $table->timestamps();

                // Indexes
                $table->index(['wallet_id', 'type']);
                $table->index(['user_id', 'type']);
                $table->index('balance_type');
                $table->index('approval_status');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
