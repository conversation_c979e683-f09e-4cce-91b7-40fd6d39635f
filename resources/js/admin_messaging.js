/**
 * Admin Messaging System
 * This script provides Vue.js components for the admin messaging system
 */

require('./bootstrap');

window.Vue = require('vue');

// Register Vue components for admin messaging
Vue.component('admin-messaging', require('./components/AdminMessaging.vue').default);

// Initialize Vue for admin messaging
if (document.getElementById('admin-messaging-widget')) {
    const adminMessaging = new Vue({
        el: '#admin-messaging-widget',
    });
}

// Initialize Echo listeners for real-time updates
if (window.Echo) {
    const userId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
    
    if (userId) {
        // Listen for new messages
        window.Echo.private(`user.${userId}.conversations`)
            .listen('.message.new', (e) => {
                // This will be handled by the Vue component
                // Handle new message from Echo
                
                // If messaging panel is not visible, update notification count
                const messagingPanel = document.getElementById('adminMessagingPanel');
                if (messagingPanel && messagingPanel.style.display !== 'flex') {
                    // Update unread count
                    fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/unread-counts')
                        .then(response => response.json())
                        .then(data => {
                            const unreadCount = data.data?.total || 0;
                            const unreadCountElement = document.getElementById('adminUnreadCount');
                            
                            if (unreadCountElement) {
                                unreadCountElement.textContent = unreadCount;
                                unreadCountElement.style.display = unreadCount > 0 ? 'flex' : 'none';
                            }
                            
                            // Also update the badge in the sidebar
                            const badge = document.querySelector('.messaging-badge');
                            if (badge) {
                                if (unreadCount > 0) {
                                    badge.textContent = unreadCount;
                                    badge.classList.remove('d-none');
                                } else {
                                    badge.classList.add('d-none');
                                }
                            }
                        })
                        .catch(error => console.error('Error updating unread count:', error));
                    
                    // Show notification if browser supports it
                    if ('Notification' in window && Notification.permission === 'granted') {
                        const notification = new Notification('New Message', {
                            body: e.message.content,
                            icon: document.querySelector('link[rel="icon"]')?.href || '/favicon.ico'
                        });
                        
                        notification.onclick = function() {
                            window.focus();
                            if (messagingPanel) {
                                messagingPanel.style.display = 'flex';
                            }
                        };
                    }
                    
                    // Play notification sound
                    const audio = new Audio('/assets/sounds/notification.mp3');
                    audio.play().catch(err => {
                        // Keep error logging for debugging auto-play issues
                        console.error('Auto-play prevented:', err);
                    });
                }
            });
            
        // Listen for conversation updates
        window.Echo.private(`user.${userId}.conversations`)
            .listen('.conversation.updated', (e) => {
                // Handle conversation update from Echo
                // This will be handled by the Vue component or the DOM event listeners
            });
            
        // Listen for unread count updates
        window.Echo.private(`user.${userId}`)
            .listen('.unread.updated', (e) => {
                // Handle unread count update from Echo
                // Update unread count in the UI
                const unreadCountElement = document.getElementById('adminUnreadCount');
                if (unreadCountElement) {
                    unreadCountElement.textContent = e.total || 0;
                    unreadCountElement.style.display = e.total > 0 ? 'flex' : 'none';
                }
                
                // Also update the badge in the sidebar
                const badge = document.querySelector('.messaging-badge');
                if (badge) {
                    if (e.total > 0) {
                        badge.textContent = e.total;
                        badge.classList.remove('d-none');
                    } else {
                        badge.classList.add('d-none');
                    }
                }
            });
    }
} 