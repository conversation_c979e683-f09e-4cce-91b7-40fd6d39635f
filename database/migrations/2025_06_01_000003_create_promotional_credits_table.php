<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //if (!Schema::hasTable('promotional_credits')) {
            Schema::create('promotional_credits', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id');
                $table->decimal('amount', 10, 2);
                $table->string('description');
            $table->boolean('is_active')->default(true);
            $table->timestamp('expiry_date')->nullable();
            $table->integer('usage_limit')->nullable();
            $table->integer('usage_count')->default(0);
            $table->decimal('min_purchase_amount', 10, 2)->nullable();
            $table->json('applicable_categories')->nullable();
            $table->timestamps();
            });
       // }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotional_credits');
    }
};
