<?php

namespace App\Http\Controllers;

use App\Models\ClubPoint;
use App\Models\ClubPointDetail;
use App\Models\Product;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Order;
use App\Models\OrderDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClubPointController extends Controller
{
    /**
     * Admin Methods
     */
    
    public function configure_index()
    {
        return view('backend.club_points.config');
    }

    public function index()
    {
        $club_points = ClubPoint::with('user', 'club_point_details')->latest()->paginate(20);
        return view('backend.club_points.index', compact('club_points'));
    }

    public function set_point()
    {
        $products = Product::where('digital', 0)->get();
        return view('backend.club_points.set_point', compact('products'));
    }

    public function set_products_point(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'point' => 'required|numeric|min:0'
        ]);

        $product = Product::find($request->product_id);
        $product->earn_point = $request->point;
        $product->save();

        flash('Club point updated successfully')->success();
        return redirect()->route('set_product_points');
    }

    public function set_all_products_point(Request $request)
    {
        $request->validate([
            'point' => 'required|numeric|min:0'
        ]);

        Product::where('digital', 0)->update(['earn_point' => $request->point]);

        flash('Club points updated for all products successfully')->success();
        return redirect()->route('set_product_points');
    }

    public function set_point_edit($id)
    {
        $product = Product::findOrFail($id);
        return view('backend.club_points.edit_point', compact('product'));
    }

    public function club_point_detail($id)
    {
        $club_point = ClubPoint::with('club_point_details.product', 'user')->findOrFail($id);
        return view('backend.club_points.detail', compact('club_point'));
    }

    public function update_product_point(Request $request, $id)
    {
        $request->validate([
            'point' => 'required|numeric|min:0'
        ]);

        $product = Product::findOrFail($id);
        $product->earn_point = $request->point;
        $product->save();

        flash('Club point updated successfully')->success();
        return redirect()->route('set_product_points');
    }

    public function convert_rate_store(Request $request)
    {
        $request->validate([
            'convert_rate' => 'required|numeric|min:0.01'
        ]);

        $business_setting = \App\Models\BusinessSetting::where('type', 'club_point_convert_rate')->first();
        if ($business_setting) {
            $business_setting->value = $request->convert_rate;
            $business_setting->save();
        } else {
            $business_setting = new \App\Models\BusinessSetting;
            $business_setting->type = 'club_point_convert_rate';
            $business_setting->value = $request->convert_rate;
            $business_setting->save();
        }

        flash('Club point convert rate updated successfully')->success();
        return redirect()->route('club_points.configs');
    }

    /**
     * Frontend Methods
     */
    
    public function userpoint_index()
    {
        $club_points = ClubPoint::where('user_id', Auth::id())->with('club_point_details.product')->latest()->paginate(20);
        return view('frontend.user.club_points.index', compact('club_points'));
    }

    public function convert_point_into_wallet(Request $request)
    {
        $request->validate([
            'club_point_id' => 'required|exists:club_points,id'
        ]);

        $club_point = ClubPoint::where('id', $request->club_point_id)
                              ->where('user_id', Auth::id())
                              ->where('convert_status', 0)
                              ->first();

        if (!$club_point) {
            flash('Invalid club point or already converted')->error();
            return redirect()->back();
        }

        $amount = 0;
        $convert_rate = get_setting('club_point_convert_rate') ?: 1;

        foreach ($club_point->club_point_details as $detail) {
            if ($detail->refunded == 0) {
                $converted_amount = floatval($detail->point / $convert_rate);
                $detail->converted_amount = $converted_amount;
                $detail->save();
                $amount += $converted_amount;
            }
        }

        if ($amount > 0) {
            // Add to wallet
            $wallet = new Wallet;
            $wallet->user_id = Auth::id();
            $wallet->amount = $amount;
            $wallet->payment_method = 'Club Point Convert';
            $wallet->payment_details = 'Converted ' . $club_point->club_point_details->sum('point') . ' points to wallet';
            $wallet->save();

            // Update user balance
            $user = User::find(Auth::id());
            $user->balance = $user->balance + $amount;
            $user->save();

            // Mark as converted
            $club_point->convert_status = 1;
            $club_point->save();

            flash('Club points converted to wallet successfully')->success();
        } else {
            flash('No points available for conversion')->error();
        }

        return redirect()->back();
    }

    /**
     * Process club points for an order (called from helpers)
     */
    public function processClubPoints($order)
    {
        if (!$order || get_setting('club_point_system') != 1) {
            return;
        }

        $club_point = new ClubPoint;
        $club_point->user_id = $order->user_id;
        $club_point->order_id = $order->id;
        $club_point->convert_status = 0;
        $club_point->save();

        foreach ($order->orderDetails as $order_detail) {
            if ($order_detail->product && $order_detail->product->earn_point > 0) {
                $club_point_detail = new ClubPointDetail;
                $club_point_detail->club_point_id = $club_point->id;
                $club_point_detail->product_id = $order_detail->product_id;
                $club_point_detail->point = $order_detail->product->earn_point * $order_detail->quantity;
                $club_point_detail->refunded = 0;
                $club_point_detail->save();
            }
        }
    }
} 