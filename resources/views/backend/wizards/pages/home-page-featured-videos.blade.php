<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 h6">{{translate($wizard->wizard_info_name)}}</h5>
            </div>
            <div class="card-body">
                <form class="form-horizontal" action="{{route('wizards.update-banner', $wizard->id)}}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="admin_view_file" value="{{$wizard->admin_view_file}}">


                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0 h6">{{ translate('Product Video Files') }}</h5>
                            </div>
                            <div class="card-body">
                                <div id="product-videos">
                                    @php
                                        $videos = optional($wizard)->videos ? json_decode($wizard->videos) : [];
                                    @endphp
                                    @if(!empty($videos))
                                        @foreach(json_decode($wizard->videos) as $key => $video)
                                            <div class="video-entry mb-3">
                                                <div class="form-group row">
                                                    <label class="col-md-3 col-from-label">{{ translate('Sequence') }}</label>
                                                    <div class="col-md-8">
                                                        <input type="number" class="form-control" name="video_sequence[]" value="{{ $video->sequence }}" min="1" placeholder="{{ translate('Enter sequence number') }}" required>
                                                        <small class="text-muted">{{ translate('Lower number will be shown first') }}</small>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-md-3 col-from-label">{{ translate('Video File') }}</label>
                                                    <div class="col-md-8">
                                                        <div class="input-group" data-toggle="aizuploader" data-type="video" data-multiple="false">
                                                            <div class="input-group-prepend">
                                                                <div class="input-group-text bg-soft-secondary font-weight-medium">{{ translate('Browse') }}</div>
                                                            </div>
                                                            <div class="form-control file-amount">{{ translate('Choose File') }}</div>
                                                            <input type="hidden" name="video_files[]" class="selected-files" value="{{ $video->upload_id }}" required>
                                                        </div>
                                                        <div class="file-preview box sm"></div>
                                                        <small class="text-muted">{{ translate('Max file size: 50MB') }}</small>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-md-3 col-from-label">{{ translate('Video Thumbnail') }}</label>
                                                    <div class="col-md-8">
                                                        <div class="input-group" data-toggle="aizuploader" data-type="image" data-multiple="false">
                                                            <div class="input-group-prepend">
                                                                <div class="input-group-text bg-soft-secondary font-weight-medium">{{ translate('Browse') }}</div>
                                                            </div>
                                                            <div class="form-control file-amount">{{ translate('Choose File') }}</div>
                                                            <input type="hidden" name="video_thumbnail[]" class="selected-files" value="{{ $video->thumbnail }}" required>
                                                        </div>
                                                        <div class="file-preview box sm"></div>
                                                        <small class="text-muted">{{ translate('Max file size: 2MB. Recommended size: 600x400px') }}</small>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-md-3 col-from-label">{{ translate('Video Duration') }}</label>
                                                    <div class="col-md-8">
                                                        <input type="text" class="form-control" name="video_duration[]" value="{{ $video->duration }}" placeholder="00:00" required>
                                                        <small class="text-muted">{{ translate('Format: MM:SS (e.g. 02:30)') }}</small>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <button type="button" class="btn btn-soft-danger btn-sm" onclick="removeVideoEntry(this);"><i class="las la-times"></i></button>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif

                                </div>
                                <div class="form-group row">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-8">
                                        <button type="button" class="btn btn-soft-secondary btn-sm" onclick="addVideoEntry()">
                                            <i class="las la-plus"></i> {{ translate('Add More Video') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>



                    <div class="form-group mb-0 text-right">
                        <button type="submit" class="btn btn-primary">{{translate('Update')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    let videoCount = 0;

    function addVideoEntry() {
        const videoEntry = `
                                <div class="video-entry mb-3">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-from-label">{{ translate('Sequence') }}</label>
                                        <div class="col-md-8">
                                            <input type="number" class="form-control" name="video_sequence[]" value="${videoCount}" min="1" placeholder="{{ translate('Enter sequence number') }}" required>
                                            <small class="text-muted">{{ translate('Lower number will be shown first') }}</small>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 col-from-label">{{ translate('Video File') }}</label>
                                        <div class="col-md-8">
                                            <div class="input-group" data-toggle="aizuploader" data-type="video" data-multiple="false">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text bg-soft-secondary font-weight-medium">{{ translate('Browse') }}</div>
                                                </div>
                                                <div class="form-control file-amount">{{ translate('Choose File') }}</div>
                                                <input type="hidden" name="video_files[]" class="selected-files" required>
                                            </div>
                                            <div class="file-preview box sm"></div>
                                            <small class="text-muted">{{ translate('Max file size: 50MB') }}</small>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 col-from-label">{{ translate('Video Thumbnail') }}</label>
                                        <div class="col-md-8">
                                            <div class="input-group" data-toggle="aizuploader" data-type="image" data-multiple="false">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text bg-soft-secondary font-weight-medium">{{ translate('Browse') }}</div>
                                                </div>
                                                <div class="form-control file-amount">{{ translate('Choose File') }}</div>
                                                <input type="hidden" name="video_thumbnail[]" class="selected-files" required>
                                            </div>
                                            <div class="file-preview box sm"></div>
                                            <small class="text-muted">{{ translate('Max file size: 2MB. Recommended size: 600x400px') }}</small>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 col-from-label">{{ translate('Video Duration') }}</label>
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" name="video_duration[]" placeholder="00:00" required>
                                            <small class="text-muted">{{ translate('Format: MM:SS (e.g. 02:30)') }}</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <button type="button" class="btn btn-soft-danger btn-sm" onclick="removeVideoEntry(this);"><i class="las la-times"></i></button>
                                    </div>
                                </div>
                            `;
        $("#product-videos").append(videoEntry);
        AIZ.plugins.inputTags();
        videoCount++;
    }

    function removeVideoEntry(el) {
        $(el).closest('.video-entry').remove();
    }
</script>




