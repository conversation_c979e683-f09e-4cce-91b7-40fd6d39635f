<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('return_methods', function (Blueprint $table) {
            if (!Schema::hasColumn('return_methods', 'requirements')) {
                $table->json('requirements')->nullable();
            }
            if (!Schema::hasColumn('return_methods', 'additionalInformation')) {
                $table->json('additionalInformation')->nullable();
            }
            if (!Schema::hasColumn('return_methods', 'type')) {
                $table->enum('type', ['return', 'refund'])->after('id')->default('return');
            }
        });
    }

    public function down()
    {
        Schema::table('return_methods', function (Blueprint $table) {
            $table->dropColumn(['requirements', 'additionalInformation', 'type']);
        });
    }
};
