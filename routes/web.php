<?php

use App\Http\Controllers\B2BController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\ReviewController;

// dropshipper added by suman
use App\Http\Controllers\SearchController;

// dropshipper added by suman
use App\Http\Controllers\WalletController;

// dropshipper added by suman
use App\Http\Controllers\AddressController;

// Test POST route
Route::post('/test-web-post', function() {
    return response()->json(['message' => 'Web POST route works']);
});

// dropshipper added by suman
//use App\Http\Controllers\dropshipper\ShopController; // dropshipper added by suman
use App\Http\Controllers\CompareController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\HomePageController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\LoadMoreController;
use App\Http\Controllers\WishlistController;
use App\Http\Controllers\AizUploadController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\B2BProfileController;
use App\Http\Controllers\SubscriberController;
use App\Http\Controllers\UpgradeShopController;
use App\Http\Controllers\ConversationController;
use App\Http\Controllers\FollowSellerController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ProductQueryController;
use App\Http\Controllers\dropshipper\DashboardController;
use App\Http\Controllers\Payment\BkashController;

use App\Http\Controllers\Payment\NagadController;
use App\Http\Controllers\Payment\PaykuController;
use App\Http\Controllers\StripePaymentController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\DigitalProductController;
use App\Http\Controllers\Payment\IyzicoController;
use App\Http\Controllers\Payment\PaypalController;
use App\Http\Controllers\Payment\StripeController;
use App\Http\Controllers\dropshipper\UserProfileController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\CustomerProductController;
use App\Http\Controllers\Payment\NgeniusController;
use App\Http\Controllers\Payment\PayhereController;
use App\Http\Controllers\PurchaseHistoryController;
use App\Http\Controllers\Payment\AamarpayController;
use App\Http\Controllers\Payment\PaystackController;
use App\Http\Controllers\Payment\RazorpayController;
use App\Http\Controllers\Payment\VoguepayController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\Payment\InstamojoController;
use App\Http\Controllers\Auth\GuestRegisterController;

// added by sumon
use App\Http\Controllers\Payment\SslcommerzController;

// sumon
use App\Http\Controllers\Payment\MercadopagoController;
use App\Http\Controllers\Payment\AuthorizenetController;
use App\Http\Controllers\CustomerCollectionsController; // arif


/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */

// <!-- /*******
// *****load more area
//  */ -->

//Route::get('product/load-more-data', [LoadMoreController::class, 'loadMoreData'])->name('load_more_data');

// Route::get('/related-products/{product_id}/{offset}', [LoadMoreController::class, 'loadMoreRelatedProducts']);
Route::get('/test-nahid',function(){
    $wizard_info_one=\App\Models\WizardInfo::with([
        'wizard_details' => function($query) {
            $query->orderBy('wizard_detail_position', 'asc'); // Order by wizard_detail_position
        },
    ])
        ->where('slug','home-page-secondary-promo-banners')
        ->where('wizard_info_status',1)
        ->orderBy('wizard_info_position','asc')
        ->first();
    dd($wizard_info_one);
});




Route::get('/clear-cache', function (\Illuminate\Http\Request $request) {
    // Get the cache key from the request
    $cache_key = $request->get('cache_key');
    if ($cache_key) {
        // Clear the cache for the given cache key
        Illuminate\Support\Facades\Cache::forget($cache_key);

        // Return a success message (you can redirect back or return a message)
        return redirect()->back()->with('success', 'Cache cleared for: ' . $cache_key);
    }

    // If no cache key is provided, return an error message
    return redirect()->back()->with('error', 'No cache key provided.');
})->name('clear-cache');
Route::get('my-logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index']);


Route::get('/cash-data', function () {
    $settings = \Illuminate\Support\Facades\Cache::get('business_settings_array');
    dd($settings);
});
Route::get('/clear-cache-all', function () {
    Artisan::call('cache:clear');
    Artisan::call('config:cache');
    Artisan::call('route:clear');
    Artisan::call('config:clear');
    Artisan::call('cache:clear');
    Artisan::call('view:clear');
    return "Cache is cleared";
});
Route::get('/config-cache', function () {
    Artisan::call('config:cache');
    return "Config Cached";
});
Route::get('/route-clear', function () {
    Artisan::call('route:clear');
    return "Route cleared";
});
Route::get('/config-clear', function () {
    Artisan::call('config:clear');
    return "Config cleared";
});
Route::get('/cache-clear', function () {
    Artisan::call('cache:clear');
    return "Cache is cleared";
});
Route::get('/view-clear', function () {
    Artisan::call('view:clear');
    return "View is cleared";
});

Route::controller(DemoController::class)->group(function () {
    Route::get('/demo/cron_1', 'cron_1');
    Route::get('/demo/cron_2', 'cron_2');
    Route::get('/convert_assets', 'convert_assets');
    Route::get('/convert_category', 'convert_category');
    Route::get('/convert_tax', 'convertTaxes');
    Route::get('/insert_product_variant_forcefully', 'insert_product_variant_forcefully');
    Route::get('/update_seller_id_in_orders/{id_min}/{id_max}', 'update_seller_id_in_orders');
    Route::get('/migrate_attribute_values', 'migrate_attribute_values');
});

Route::get('/refresh-csrf', function () {
    return csrf_token();
});

// AIZ Uploader
Route::controller(AizUploadController::class)->group(function () {
    Route::post('/aiz-uploader', 'show_uploader');
    Route::post('/aiz-uploader/upload', 'upload');
    Route::get('/aiz-uploader/get-uploaded-files', 'get_uploaded_files');
    Route::post('/aiz-uploader/get_file_by_ids', 'get_preview_files');
    Route::get('/aiz-uploader/download/{id}', 'attachment_download')->name('download_attachment');
});

Route::group(['middleware' => 'prevent-back-history'], function () {
    Auth::routes(['verify' => true]);
});

// Login
Route::controller(LoginController::class)->group(function () {
    Route::get('/logout', 'logout');
    Route::get('/social-login/redirect/{provider}', 'redirectToProvider')->name('social.login');
    Route::get('/social-login/{provider}/callback', 'handleProviderCallback')->name('social.callback');
    //Apple Callback
    Route::post('/apple-callback', 'handleAppleCallback');
    Route::get('/account-deletion', 'account_deletion')->name('account_delete');
});

Route::controller(VerificationController::class)->group(function () {
    Route::get('/email/resend', 'resend')->name('verification.resend');
    Route::get('/verification-confirmation/{code}', 'verification_confirmation')->name('email.verification.confirmation');
    Route::get('/otp-verification-confirmation/{code}', 'otp_verification_confirmation')->name('otp.email.verification.confirmation');
    Route::post('/verify-otp-email-reset-password', 'verify_otp_email_reset_password')->name('verify.otp.email.reset.password');

    Route::post('/reset-password', 'verify_otp_reset_password')->name('verify.otp.email.reset.password.s');
});

Route::controller(HomePageController::class)->group(function () {
    Route::get('/', 'index')->middleware('restricted_old_view')->name('home');
    Route::get('/get_product_by_category', 'get_product_by_category')->name('home.get_product_by_category');
    Route::get('/get_offer_product_by_offer_id', 'get_offer_product_by_offer_id')->name('home.get_offer_product_by_offer_id');
});


Route::controller(HomeController::class)->group(function () {
    Route::get('/email-change/callback', 'email_change_callback')->name('email_change.callback');
    Route::post('/password/reset/email/submit', 'reset_password_with_code')->name('password.update');

    Route::get('/otp-password-request', 'otp_password_request')->name('otp.password.request');
    Route::get('/otp-password-request-form/{token}/{email}', 'otp_password_request_form')->name('password.reset');
    Route::post('/verify-otp-reset-password', 'verify_otp_reset_password')->name('password.verify-otp-reset-password');
    Route::post('/otp-password-email', 'otp_password_email')->name('otp.password.email');

    Route::get('/users/login', 'login')->name('user.login');
    Route::get('/seller/login', 'login')->name('seller.login');
    Route::get('/dropshipper/login', 'login')->name('dropshipper.login');
    Route::get('/dropshipper/landing', 'dropshipperLanding')->name('dropshipperLanding');
    Route::get('/deliveryboy/login', 'login')->name('deliveryboy.login');
    Route::get('/users/registration', 'registration')->middleware('restricted_old_view')->name('user.registration');
    Route::get('/users/guest', 'guestRegistration')->middleware('restricted_old_view')->name('user.guest');
    Route::post('/users/login/cart', 'cart_login')->middleware('restricted_old_view')->name('cart.login.submit');
    // Route::get('/new-page', 'new_page')->name('new_page');


    //Home Page

    Route::post('/home/<USER>/featured', 'load_featured_section')->name('home.section.featured');
    Route::post('/home/<USER>/todays-deal', 'load_todays_deal_section')->name('home.section.todays_deal');
    Route::post('/home/<USER>/best-selling', 'load_best_selling_section')->name('home.section.best_selling');
    Route::post('/home/<USER>/newest-products', 'load_newest_product_section')->name('home.section.newest_products');
    Route::post('/home/<USER>/home-categories', 'load_home_categories_section')->name('home.section.home_categories');
    Route::post('/home/<USER>/best-sellers', 'load_best_sellers_section')->name('home.section.best_sellers');

    //category dropdown menu ajax call
    Route::post('/category/nav-element-list', 'get_category_items')->name('category.elements');

    //Flash Deal Details Page
    Route::get('/flash-deals', 'all_flash_deals')->name('flash-deals');
    Route::get('/flash-deal/{slug}', 'flash_deal_details')->name('flash-deal-details');

    //Todays Deal Details Page
    Route::get('/todays-deal', 'todays_deal')->name('todays-deal');

    Route::get('/product/{slug}', 'product')->name('product');
    Route::post('/product/variant-price', 'variant_price')->name('products.variant_price');
    Route::get('/shop/{slug}', 'shop')->name('shop.visit');
    Route::get('/shop/{slug}/{type}', 'filter_shop')->name('shop.visit.type');

    Route::get('/customer-packages', 'premium_package_index')->name('customer_packages_list_show');

    Route::get('/brands', 'all_brands')->name('brands.all');
    Route::get('/categories', 'all_categories')->name('categories.all');
    Route::get('/sellers', 'all_seller')->name('sellers');
    Route::get('/coupons', 'all_coupons')->name('coupons.all');
    Route::get('/inhouse', 'inhouse_products')->name('inhouse.all');


    // Policies
    Route::get('/seller-policy', 'sellerpolicy')->name('sellerpolicy');
    Route::get('/return-policy', 'returnpolicy')->name('returnpolicy');
    Route::get('/support-policy', 'supportpolicy')->name('supportpolicy');
    Route::get('/terms', 'terms')->name('terms');
    Route::get('/privacy-policy', 'privacypolicy')->name('privacypolicy');

    Route::get('/track-your-order', 'trackOrder')->name('orders.track');
    Route::get('/related-products', 'loadRelatedProducts')->name('related.products');
    Route::get('/product/{id}/reviews', 'loadMoreReviews')->name('product.loadMoreReviews');


    Route::get('/featured-products',  'loadFeaturedProducts')->name('featured.products');
    Route::get('/best-sellers', 'loadBestSellers')->name('best.sellers');
});

// Language Switch
Route::post('/language', [LanguageController::class, 'changeLanguage'])->name('language.change');

// Currency Switch
Route::post('/currency', [CurrencyController::class, 'changeCurrency'])->name('currency.change');


Route::get('/sitemap.xml', function () {
    return base_path('sitemap.xml');
});

// Classified Product
Route::controller(CustomerProductController::class)->group(function () {
    Route::get('/customer-products', 'customer_products_listing')->name('customer.products');
    Route::get('/customer-products?category={category_slug}', 'search')->name('customer_products.category');
    Route::get('/customer-products?city={city_id}', 'search')->name('customer_products.city');
    Route::get('/customer-products?q={search}', 'search')->name('customer_products.search');
    Route::get('/customer-product/{slug}', 'customer_product')->name('customer.product');
});

// Search
Route::controller(SearchController::class)->group(function () {
    Route::get('/search', 'index')->name('search');
    Route::get('/search?keyword={search}', 'index')->name('suggestion.search');
    Route::post('/ajax-search', 'ajax_search')->name('search.ajax');
    Route::get('/category/{category_slug}', 'listingByCategory')->name('products.category');
    Route::get('/brand/{brand_slug}', 'listingByBrand')->name('products.brand');
});

// Cart
Route::controller(CartController::class)->group(function () {
    Route::get('/cart', 'index')->name('cart');
    Route::post('/cart/show-cart-modal', 'showCartModal')->name('cart.showCartModal');
    Route::post('/cart/addtocart', 'addToCart')->name('cart.addToCart');
    Route::post('/cart/removeFromCart', 'removeFromCart')->name('cart.removeFromCart');
    Route::post('/cart/updateQuantity', 'updateQuantity')->name('cart.updateQuantity');
    //showflashdeal
    Route::post('/show-flash-deal-cart-modal', 'showFlashDealCartModal')->name('show_flash_deal_modal');
});

//Paypal START
Route::controller(PaypalController::class)->group(function () {
    Route::get('/paypal/payment/done', 'getDone')->name('payment.done');
    Route::get('/paypal/payment/cancel', 'getCancel')->name('payment.cancel');
});
//Mercadopago START
Route::controller(MercadopagoController::class)->group(function () {
    Route::any('/mercadopago/payment/done', 'paymentstatus')->name('mercadopago.done');
    Route::any('/mercadopago/payment/cancel', 'callback')->name('mercadopago.cancel');
});
//Mercadopago

// SSLCOMMERZ Start
Route::controller(SslcommerzController::class)->group(function () {
    Route::get('/sslcommerz/pay', 'index');
    Route::POST('/sslcommerz/success', 'success');
    Route::POST('/sslcommerz/fail', 'fail');
    Route::POST('/sslcommerz/cancel', 'cancel');
    Route::POST('/sslcommerz/ipn', 'ipn');
});
//SSLCOMMERZ END

//Stipe Start
Route::controller(StripeController::class)->group(function () {
    Route::get('stripe', 'stripe');
    Route::post('/stripe/create-checkout-session', 'create_checkout_session')->name('stripe.get_token');
    Route::any('/stripe/payment/callback', 'callback')->name('stripe.callback');
    Route::get('/stripe/success', 'success')->name('stripe.success');
    Route::get('/stripe/cancel', 'cancel')->name('stripe.cancel');
});
//Stripe END


Route::controller(StripePaymentController::class)->group(function () {
    Route::post('/payment/store-card', 'storeCard')->name('store-card');
    Route::get('/payment/cards', 'getCards')->name('cards');
    Route::delete('/payment/card/{cardId}', 'removeCard')->name('payment.card');
    Route::post('/pay-with-card', 'payWithCard')->name('pay-with-card');
    Route::get('/stripe-payment/success', 'success')->name('stripe2.success');
    Route::get('/stripe-payment/cancel', 'cancel')->name('stripe2.cancel');
});

// Compare
Route::controller(CompareController::class)->group(function () {
    Route::get('/compare', 'index')->name('compare');
    Route::get('/compare/reset', 'reset')->name('compare.reset');
    Route::post('/compare/addToCompare', 'addToCompare')->name('compare.addToCompare');
    Route::get('/compare/details/{id}', 'details')->name('compare.details');
});

// Subscribe
Route::resource('subscribers', SubscriberController::class);

//Route::group(['middleware' => ['user', 'verified', 'unbanned']], function () { //commented by suman for disable verify

Route::group(['middleware' => ['user', 'unbanned']], function () {

    Route::controller(HomeController::class)->group(function () {
        Route::get('/dashboard', 'dashboard')->name('dashboard')->middleware(['prevent-back-history']);
        Route::get('/profile', 'profile')->name('profile');
        Route::post('/new-user-verification', 'new_verify')->name('user.new.verify');
        Route::post('/new-user-email', 'update_email')->name('user.change.email');
        Route::post('/user/update-profile', 'userProfileUpdate')->name('user.profile.update');
    });

    Route::get('/all-notifications', [NotificationController::class, 'index'])->name('all-notifications');
});

// Route::group(['middleware' => ['customer', 'verified', 'unbanned']], function () { //commented by suman for disable verify

Route::group(['middleware' => ['user', 'unbanned', 'verified']], function () {
    //Route::group(['middleware' => ['customer', 'guest', 'unbanned']], function () {
    // Checkout Routs
    Route::group(['prefix' => 'checkout'], function () {
        Route::controller(CheckoutController::class)->group(function () {
            Route::get('/', 'get_shipping_info')->name('checkout.shipping_info');
            Route::any('/delivery-info', 'store_shipping_info')->name('checkout.store_shipping_infostore');
            Route::any('/payment-select', 'store_shipping_info')->name('checkout.store_shipping_infostore');
            //Route::any('/payment_select', 'CheckoutController@store_shipping_info')->name('checkout.store_shipping_infostore');
            //Route::post('/payment-select', 'store_delivery_info')->name('checkout.store_delivery_info');
            Route::get('/order-confirmed', 'order_confirmed')->name('order_confirmed');
            Route::any('/payment', 'checkout')->name('payment.checkout');
            Route::post('/get-pick-up-points', 'get_pick_up_points')->name('shipping_info.get_pick_up_points');
            Route::get('/payment-select', 'get_payment_info')->name('checkout.payment_info');
            Route::post('/apply-coupon-code', 'apply_coupon_code')->name('checkout.apply_coupon_code');
            Route::post('/remove-coupon-code', 'remove_coupon_code')->name('checkout.remove_coupon_code');
            //Club point
            Route::post('/apply-club-point', 'apply_club_point')->name('checkout.apply_club_point');
            Route::post('/remove-club-point', 'remove_club_point')->name('checkout.remove_club_point');
            Route::get('/payment/process', [CheckoutController::class, 'processPayment'])->name('payment.process');
        });
    });

    // Purchase History
    Route::resource('purchase_history', PurchaseHistoryController::class);
    Route::controller(PurchaseHistoryController::class)->group(function () {
        Route::get('/purchase_history/details/{id}', 'purchase_history_details')->name('purchase_history.details');
        Route::get('/purchase_history/destroy/{id}', 'order_cancel')->name('purchase_history.destroy');
        Route::get('digital-purchase-history', 'digital_index')->name('digital_purchase_history.index');
        Route::get('/digital-products/download/{id}', 'download')->name('digital-products.download');
        Route::get('/payment/process', [PurchaseHistoryController::class, 'processPayment'])->name('purchase_history.payment.process');
        Route::get('/re-order/{id}', 're_order')->name('re_order');
    });

    // Wishlist
    Route::resource('wishlists', WishlistController::class);
    Route::post('/wishlists/remove', [WishlistController::class, 'remove'])->name('wishlists.remove');

    //Follow
    Route::controller(FollowSellerController::class)->group(function () {
        Route::get('/followed-seller', 'index')->name('followed_seller');
        Route::get('/followed-seller/store', 'store')->name('followed_seller.store');
        Route::get('/followed-seller/remove', 'remove')->name('followed_seller.remove');
    });

    // Wallet
    Route::controller(WalletController::class)->group(function () {
        Route::get('/wallet', 'index')->name('wallet.index');
        Route::post('/recharge', 'recharge')->name('wallet.recharge');
    });

    // Support Ticket
    Route::resource('support_ticket', SupportTicketController::class);
    Route::post('support_ticket/reply', [SupportTicketController::class, 'seller_store'])->name('support_ticket.seller_store');

    // Customer Package
    Route::post('/customer-packages/purchase', [CustomerPackageController::class, 'purchase_package'])->name('customer_packages.purchase');

    // Customer Product
    Route::resource('customer_products', CustomerProductController::class);
    Route::controller(CustomerProductController::class)->group(function () {
        Route::get('/customer_products/{id}/edit', 'edit')->name('customer_products.edit');
        Route::post('/customer_products/published', 'updatePublished')->name('customer_products.published');
        Route::post('/customer_products/status', 'updateStatus')->name('customer_products.update.status');
        Route::get('/customer_products/destroy/{id}', 'destroy')->name('customer_products.destroy');
    });

    // Product Review
    Route::post('/product-review-modal', [ReviewController::class, 'product_review_modal'])->name('product_review_modal');
});


Route::get('translation-check/{check}', [LanguageController::class, 'get_translation']);


Route::group(['middleware' => ['auth']], function () {

    Route::get('invoice/{order_id}', [InvoiceController::class, 'invoice_download'])->name('invoice.download');

    // Reviews
    Route::resource('/reviews', ReviewController::class);

    // Product Conversation
    Route::resource('conversations', ConversationController::class);
    Route::controller(ConversationController::class)->group(function () {
        Route::get('/conversations/destroy/{id}', 'destroy')->name('conversations.destroy');
        Route::post('conversations/refresh', 'refresh')->name('conversations.refresh');
    });

    // Product Query
    Route::resource('product-queries', ProductQueryController::class);

    Route::resource('messages', MessageController::class);

    //Address
    Route::resource('addresses', AddressController::class);
    Route::controller(AddressController::class)->group(function () {
        Route::post('/get-states', 'getStates')->name('get-state');
        Route::post('/get-cities', 'getCities')->name('get-city');
        Route::post('/addresses/update/{id}', 'update')->name('addresses.update');
        Route::get('/addresses/destroy/{id}', 'destroy')->name('addresses.destroy');
        Route::get('/addresses/set-default/{id}', 'set_default')->name('addresses.set_default');
        Route::post('/add-city', 'addCity')->name('add.city');
    });
});

Route::resource('shops', ShopController::class);
Route::resource('dropshippers', B2BProfileController::class);

Route::get('/instamojo/payment/pay-success', [InstamojoController::class, 'success'])->name('instamojo.success');

Route::post('rozer/payment/pay-success', [RazorpayController::class, 'payment'])->name('payment.rozer');

Route::get('/paystack/payment/callback', [PaystackController::class, 'handleGatewayCallback']);
Route::get('/paystack/new-callback', [PaystackController::class, 'paystackNewCallback']);

Route::controller(VoguepayController::class)->group(function () {
    Route::get('/vogue-pay', 'showForm');
    Route::get('/vogue-pay/success/{id}', 'paymentSuccess');
    Route::get('/vogue-pay/failure/{id}', 'paymentFailure');
});


//Iyzico
Route::any('/iyzico/payment/callback/{payment_type}/{amount?}/{payment_method?}/{combined_order_id?}/{customer_package_id?}/{seller_package_id?}', [IyzicoController::class, 'callback'])->name('iyzico.callback');

Route::get('/customer-products/admin', [IyzicoController::class, 'initPayment'])->name('profile.edit');

//payhere below
Route::controller(PayhereController::class)->group(function () {
    Route::get('/payhere/checkout/testing', 'checkout_testing')->name('payhere.checkout.testing');
    Route::get('/payhere/wallet/testing', 'wallet_testing')->name('payhere.checkout.testing');
    Route::get('/payhere/customer_package/testing', 'customer_package_testing')->name('payhere.customer_package.testing');

    Route::any('/payhere/checkout/notify', 'checkout_notify')->name('payhere.checkout.notify');
    Route::any('/payhere/checkout/return', 'checkout_return')->name('payhere.checkout.return');
    Route::any('/payhere/checkout/cancel', 'chekout_cancel')->name('payhere.checkout.cancel');

    Route::any('/payhere/wallet/notify', 'wallet_notify')->name('payhere.wallet.notify');
    Route::any('/payhere/wallet/return', 'wallet_return')->name('payhere.wallet.return');
    Route::any('/payhere/wallet/cancel', 'wallet_cancel')->name('payhere.wallet.cancel');

    Route::any('/payhere/seller_package_payment/notify', 'seller_package_notify')->name('payhere.seller_package_payment.notify');
    Route::any('/payhere/seller_package_payment/return', 'seller_package_payment_return')->name('payhere.seller_package_payment.return');
    Route::any('/payhere/seller_package_payment/cancel', 'seller_package_payment_cancel')->name('payhere.seller_package_payment.cancel');

    Route::any('/payhere/customer_package_payment/notify', 'customer_package_notify')->name('payhere.customer_package_payment.notify');
    Route::any('/payhere/customer_package_payment/return', 'customer_package_return')->name('payhere.customer_package_payment.return');
    Route::any('/payhere/customer_package_payment/cancel', 'customer_package_cancel')->name('payhere.customer_package_payment.cancel');
});


//N-genius
Route::controller(NgeniusController::class)->group(function () {
    Route::any('ngenius/cart_payment_callback', 'cart_payment_callback')->name('ngenius.cart_payment_callback');
    Route::any('ngenius/wallet_payment_callback', 'wallet_payment_callback')->name('ngenius.wallet_payment_callback');
    Route::any('ngenius/customer_package_payment_callback', 'customer_package_payment_callback')->name('ngenius.customer_package_payment_callback');
    Route::any('ngenius/seller_package_payment_callback', 'seller_package_payment_callback')->name('ngenius.seller_package_payment_callback');
});

Route::controller(BkashController::class)->group(function () {
    Route::get('/bkash/create-payment', 'create_payment')->name('bkash.create_payment');
    Route::get('/bkash/callback', 'callback')->name('bkash.callback');
    Route::get('/bkash/success', 'success')->name('bkash.success');
});

Route::get('/checkout-payment-detail', [StripeController::class, 'checkout_payment_detail']);

//Nagad
Route::get('/nagad/callback', [NagadController::class, 'verify'])->name('nagad.callback');

//aamarpay
Route::controller(AamarpayController::class)->group(function () {
    Route::post('/aamarpay/success', 'success')->name('aamarpay.success');
    Route::post('/aamarpay/fail', 'fail')->name('aamarpay.fail');
});

//Authorize-Net-Payment
Route::post('/dopay/online', [AuthorizenetController::class, 'handleonlinepay'])->name('dopay.online');
Route::get('/authorizenet/cardtype', [AuthorizenetController::class, 'cardType'])->name('authorizenet.cardtype');

//payku
Route::get('/payku/callback/{id}', [PaykuController::class, 'callback'])->name('payku.result');

//Blog Section
Route::controller(BlogController::class)->group(function () {
    Route::get('/blog', 'all_blog')->name('blog');
    Route::get('/blog/{slug}', 'blog_details')->name('blog.details');
});
// Added By Arif
Route::group(['prefix' => 'collections'], function () {
    Route::controller(CustomerCollectionsController::class)->group(function () {
        Route::get('/{collection_id}/category/{category_slug}', 'listingByCategoryAndCollection')->name('collections.category');
        Route::get('/search?keyword={search}', 'index')->name('collections.suggestion.search');
        Route::post('/ajax-search', 'ajax_search')->name('collections.search.ajax');
    });
});

Route::controller(PageController::class)->group(function () {
    //mobile app balnk page for webview
    Route::get('/mobile-page/{slug}', 'mobile_custom_page')->name('mobile.custom-pages');

    //Custom page - exclude API routes (temporarily commented out)
    // Route::get('/{slug}', 'show_custom_page')->where('slug', '^(?!api).*')->name('custom-pages.show_custom_page');
    Route::get('/{slug}', 'show_custom_page')->name('custom-pages.show_custom_page');
});

// by sumon started

Route::controller(GuestRegisterController::class)->group(function () {
    Route::post('/users/guest-reg', 'register')->name('storeGuest');
});
Route::resource('shopupgrade', UpgradeShopController::class);



// by sumon ended

// Admin routes
Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'admin']], function () {
    // ... existing admin routes ...

    // Promotions Dashboard
    Route::get('/promotions/dashboard', 'App\Http\Controllers\Admin\PromotionsController@dashboard')->name('admin.promotions.dashboard');
    Route::get('/promotions/analytics', 'App\Http\Controllers\Admin\PromotionsController@analytics')->name('admin.promotions.analytics');

    // Flash Sales Admin Routes
    Route::get('/flash-sales', 'App\Http\Controllers\Admin\FlashSaleController@index')->name('admin.flash_sales.index');
    Route::get('/flash-sales/create', 'App\Http\Controllers\Admin\FlashSaleController@create')->name('admin.flash_sales.create');
    Route::post('/flash-sales/store', 'App\Http\Controllers\Admin\FlashSaleController@store')->name('admin.flash_sales.store');
    Route::get('/flash-sales/edit/{id}', 'App\Http\Controllers\Admin\FlashSaleController@edit')->name('admin.flash_sales.edit');
    Route::post('/flash-sales/update/{id}', 'App\Http\Controllers\Admin\FlashSaleController@update')->name('admin.flash_sales.update');
    Route::get('/flash-sales/destroy/{id}', 'App\Http\Controllers\Admin\FlashSaleController@destroy')->name('admin.flash_sales.destroy');

    // Banners Admin Routes
    Route::get('/banners', 'App\Http\Controllers\Admin\BannerController@index')->name('admin.banners.index');
    Route::get('/banners/create', 'App\Http\Controllers\Admin\BannerController@create')->name('admin.banners.create');
    Route::post('/banners/store', 'App\Http\Controllers\Admin\BannerController@store')->name('admin.banners.store');
    Route::get('/banners/edit/{id}', 'App\Http\Controllers\Admin\BannerController@edit')->name('admin.banners.edit');
    Route::post('/banners/update/{id}', 'App\Http\Controllers\Admin\BannerController@update')->name('admin.banners.update');
    Route::get('/banners/destroy/{id}', 'App\Http\Controllers\Admin\BannerController@destroy')->name('admin.banners.destroy');

    // Seasonal Banners Admin Routes
    // Route::get('/seasonal-banners', 'App\Http\Controllers\Admin\SeasonalBannerController@index')->name('admin.seasonal_banners.index');
    // Route::get('/seasonal-banners/create', 'App\Http\Controllers\Admin\SeasonalBannerController@create')->name('admin.seasonal_banners.create');
    // Route::post('/seasonal-banners/store', 'App\Http\Controllers\Admin\SeasonalBannerController@store')->name('admin.seasonal_banners.store');
    // Route::get('/seasonal-banners/edit/{id}', 'App\Http\Controllers\Admin\SeasonalBannerController@edit')->name('admin.seasonal_banners.edit');
    // Route::post('/seasonal-banners/update/{id}', 'App\Http\Controllers\Admin\SeasonalBannerController@update')->name('admin.seasonal_banners.update');
    // Route::get('/seasonal-banners/destroy/{id}', 'App\Http\Controllers\Admin\SeasonalBannerController@destroy')->name('admin.seasonal_banners.destroy');
});

// Add debug route for wallet data (temporary for debugging)
Route::get('/debug-wallet', function () {
    try {
        // Check database connection
        $dbConnection = DB::connection()->getPdo();
        if (!$dbConnection) {
            return response()->json(['error' => 'Database connection failed']);
        }

        // Check if tables exist
        $tables = ['wallets', 'wallet_transactions', 'wallet_exchange_rates'];
        $existingTables = [];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $existingTables[] = $table;
            }
        }

        // Get sample data
        $walletsCount = DB::table('wallets')->count();
        $transactionsCount = DB::table('wallet_transactions')->count();
        $exchangeRatesCount = DB::table('wallet_exchange_rates')->count();

        // Get sample wallet records
        $sampleWallets = DB::table('wallets')->limit(5)->get();
        $sampleTransactions = DB::table('wallet_transactions')->limit(5)->get();

        return response()->json([
            'database_connection' => 'OK',
            'existing_tables' => $existingTables,
            'missing_tables' => array_diff($tables, $existingTables),
            'data_counts' => [
                'wallets' => $walletsCount,
                'transactions' => $transactionsCount,
                'exchange_rates' => $exchangeRatesCount
            ],
            'sample_data' => [
                'wallets' => $sampleWallets,
                'transactions' => $sampleTransactions
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'error' => 'Database error: ' . $e->getMessage()
        ]);
    }
});

// Admin Notification Management Route
Route::get('/admin/notifications/manage', function () {
    return redirect()->to('/admin/notifications');
})->name('admin.notifications.manage');

// Admin Notification Mark as Read Route
Route::post('/admin/notifications/mark-read/{id}', function ($id) {
    try {
        $notification = \App\Models\UserNotification::findOrFail($id);
        $notification->read = true;
        $notification->read_at = now();
        $notification->save();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to mark notification as read'
        ], 500);
    }
})->name('admin.notifications.mark-read');

// Test dropshipper API routes
Route::prefix('api/v3/test')->group(function () {
    Route::get('top-tier-products', [\App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperProductsController::class, 'getTopTierProducts']);
    Route::get('top-tier-stats', [\App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperProductsController::class, 'getTopTierStats']);
    Route::get('early-access', [\App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperProductsController::class, 'getEarlyAccessProducts']);
    Route::get('limited-offers', [\App\Http\Controllers\Api\V3\Dropshipper\ApiDropshipperProductsController::class, 'getLimitedOffers']);
});

