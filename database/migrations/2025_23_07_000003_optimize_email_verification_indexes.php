<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_verify', function (Blueprint $table) {
            // Add index for last_resend_at to optimize cleanup queries
            if (!$this->indexExists('email_verify', 'idx_email_verify_last_resend_at')) {
                $table->index('last_resend_at', 'idx_email_verify_last_resend_at');
            }

            // Add composite index for is_verify and expire_time for cleanup queries
            if (!$this->indexExists('email_verify', 'idx_email_verify_is_verify_expire_time')) {
                $table->index(['is_verify', 'expire_time'], 'idx_email_verify_is_verify_expire_time');
            }

            // Add index for resend_count to optimize resend limit checks
            if (!$this->indexExists('email_verify', 'idx_email_verify_resend_count')) {
                $table->index('resend_count', 'idx_email_verify_resend_count');
            }

            // Add composite index for email and is_verify for verification status checks
            if (!$this->indexExists('email_verify', 'idx_email_verify_email_is_verify')) {
                $table->index(['email', 'is_verify'], 'idx_email_verify_email_is_verify');
            }

            // Add index for created_at to optimize old record cleanup
            if (!$this->indexExists('email_verify', 'idx_email_verify_created_at')) {
                $table->index('created_at', 'idx_email_verify_created_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_verify', function (Blueprint $table) {
            // Drop the indexes in reverse order
            $table->dropIndex('idx_email_verify_created_at');
            $table->dropIndex('idx_email_verify_email_is_verify');
            $table->dropIndex('idx_email_verify_resend_count');
            $table->dropIndex('idx_email_verify_is_verify_expire_time');
            $table->dropIndex('idx_email_verify_last_resend_at');
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $indexes = collect(\DB::select("SHOW INDEX FROM {$table}"))
            ->pluck('Key_name')
            ->toArray();

        return in_array($indexName, $indexes);
    }
};