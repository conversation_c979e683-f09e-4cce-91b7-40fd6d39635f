<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            if (!Schema::hasColumn('wallet_transactions', 'balance_type')) {
                $table->string('balance_type')->default('balance')->after('amount');
            }
            if (!Schema::hasColumn('wallet_transactions', 'added_by')) {
                $table->unsignedInteger('added_by')->nullable()->after('reference_id');
                $table->foreign('added_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('wallet_transactions', 'approval_status')) {
                $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved')->after('added_by');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            if (Schema::hasColumn('wallet_transactions', 'balance_type')) {
                $table->dropColumn('balance_type');
            }
            if (Schema::hasColumn('wallet_transactions', 'added_by')) {
                $table->dropForeign(['added_by']);
                $table->dropColumn('added_by');
            }
            if (Schema::hasColumn('wallet_transactions', 'approval_status')) {
                $table->dropColumn('approval_status');
            }
        });
    }
}; 