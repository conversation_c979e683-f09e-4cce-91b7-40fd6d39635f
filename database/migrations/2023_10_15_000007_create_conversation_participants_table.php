<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::create('conversation_participants', function (Blueprint $table) {
                $table->id();
                $table->foreignId('conversation_id');
                //$table->foreignId('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                //$table->unsignedInteger('user_id');
                $table->foreignId('user_id');
                $table->string('role')->default('member');
                $table->timestamps();
                $table->softDeletes();
                $table->unique(['conversation_id', 'user_id']);
                //$table->foreign('conversation_id')->on('conversations')->onDelete('cascade');
                //$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('conversation_participants');
    }
};
