<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCombinedOrderCodeToCombinedOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('combined_orders')) {
            Schema::table('combined_orders', function (Blueprint $table) {
                $table->string('combined_order_code')->nullable()->after('id');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('combined_orders', function (Blueprint $table) {
            $table->dropColumn('combined_order_code');
        });
    }
}