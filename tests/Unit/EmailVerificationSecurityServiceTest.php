<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\EmailVerificationSecurityService;
use Illuminate\Support\Facades\Cache;

class EmailVerificationSecurityServiceTest extends TestCase
{
    protected $securityService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->securityService = new EmailVerificationSecurityService();
    }

    /** @test */
    public function it_validates_otp_format_correctly()
    {
        // Valid OTP formats (non-sequential, non-repeating)
        $this->assertTrue($this->securityService->validateOtpFormat('102938'));
        $this->assertTrue($this->securityService->validateOtpFormat('847291'));
        $this->assertTrue($this->securityService->validateOtpFormat('395847'));
        
        // Invalid OTP formats
        $this->assertFalse($this->securityService->validateOtpFormat('12345')); // Too short
        $this->assertFalse($this->securityService->validateOtpFormat('1234567')); // Too long
        $this->assertFalse($this->securityService->validateOtpFormat('12345a')); // Contains letter
        $this->assertFalse($this->securityService->validateOtpFormat('111111')); // All same digit
        $this->assertFalse($this->securityService->validateOtpFormat('123456')); // Sequential
        $this->assertFalse($this->securityService->validateOtpFormat('654321')); // Sequential reverse
        $this->assertFalse($this->securityService->validateOtpFormat('012345')); // Sequential with zero
    }

    /** @test */
    public function it_detects_bot_activity_correctly()
    {
        // Bot-like user agents should be detected
        $botUserAgents = [
            'curl/7.68.0',
            'python-requests/2.25.1',
            'Mozilla/5.0 (compatible; Googlebot/2.1)',
            'HeadlessChrome/91.0.4472.114',
            'PhantomJS/2.1.1',
            'Scrapy/2.5.0'
        ];
        
        foreach ($botUserAgents as $userAgent) {
            $isBot = $this->securityService->detectBotActivity($userAgent, [
                'accept' => 'text/html'
            ]);
            $this->assertTrue($isBot, "Failed to detect bot: {$userAgent}");
        }
        
        // Normal browser user agent should not be detected as bot
        $normalUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        $isBot = $this->securityService->detectBotActivity($normalUserAgent, [
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'accept-language' => 'en-US,en;q=0.5',
            'accept-encoding' => 'gzip, deflate'
        ]);
        $this->assertFalse($isBot);
        
        // Missing headers should trigger bot detection
        $isBot = $this->securityService->detectBotActivity($normalUserAgent, []);
        $this->assertTrue($isBot);
    }

    /** @test */
    public function it_generates_secure_tokens()
    {
        $tokens = [];
        
        // Generate multiple tokens
        for ($i = 0; $i < 10; $i++) {
            $token = $this->securityService->generateSecureToken();
            $tokens[] = $token;
            
            // Validate token security
            $this->assertTrue($this->securityService->validateTokenSecurity($token));
        }
        
        // All tokens should be unique
        $this->assertEquals(count($tokens), count(array_unique($tokens)));
        
        // Tokens should be proper length and format
        foreach ($tokens as $token) {
            $this->assertEquals(32, strlen($token));
            $this->assertTrue(ctype_xdigit($token));
        }
    }

    /** @test */
    public function it_validates_token_security_correctly()
    {
        // Valid secure tokens
        $validTokens = [
            'a1b2c3d4e5f6789012345678901234ab',
            '1234567890abcdef1234567890abcdef',
            'fedcba0987654321fedcba0987654321'
        ];
        
        foreach ($validTokens as $token) {
            $this->assertTrue($this->securityService->validateTokenSecurity($token));
        }
        
        // Invalid tokens
        $invalidTokens = [
            'short', // Too short
            '1111111111111111111111111111111', // Low entropy
            'not-hex-characters-in-this-token!', // Non-hex characters
            '' // Empty
        ];
        
        foreach ($invalidTokens as $token) {
            $this->assertFalse($this->securityService->validateTokenSecurity($token));
        }
    }

    /** @test */
    public function it_manages_ip_blocking()
    {
        $ip = '*************';
        
        // Initially IP should not be blocked
        $this->assertFalse($this->securityService->isIpBlocked($ip));
        
        // Block the IP
        $this->securityService->blockIp($ip, 60); // 1 minute
        
        // Now IP should be blocked
        $this->assertTrue($this->securityService->isIpBlocked($ip));
    }

    /** @test */
    public function it_manages_email_blocking()
    {
        $email = '<EMAIL>';
        
        // Initially email should not be blocked
        $this->assertFalse($this->securityService->isEmailBlocked($email));
        
        // Block the email
        $this->securityService->blockEmail($email, 60); // 1 minute
        
        // Now email should be blocked
        $this->assertTrue($this->securityService->isEmailBlocked($email));
    }

    /** @test */
    public function it_tracks_and_clears_failed_attempts()
    {
        $email = '<EMAIL>';
        $ip = '*************';
        
        // Track failed attempts
        $this->securityService->trackFailedAttempt($email, $ip);
        
        // Verify attempts are tracked
        $this->assertTrue(Cache::has("failed_otp:{$email}"));
        $this->assertTrue(Cache::has("failed_otp_ip:{$ip}"));
        
        // Clear failed attempts
        $this->securityService->clearFailedAttempts($email, $ip);
        
        // Verify attempts are cleared
        $this->assertFalse(Cache::has("failed_otp:{$email}"));
        $this->assertFalse(Cache::has("failed_otp_ip:{$ip}"));
    }

    /** @test */
    public function it_provides_security_statistics()
    {
        $stats = $this->securityService->getSecurityStatistics();
        
        $this->assertIsArray($stats);
        // The method should return an array even if empty
        $this->assertArrayHasKey('rate_limits', $stats);
    }

    /** @test */
    public function it_generates_different_length_tokens()
    {
        // Test different token lengths
        $lengths = [16, 32, 64];
        
        foreach ($lengths as $length) {
            $token = $this->securityService->generateSecureToken($length);
            $this->assertEquals($length, strlen($token));
            $this->assertTrue(ctype_xdigit($token));
        }
    }

    /** @test */
    public function it_logs_security_events()
    {
        // This test verifies the method doesn't throw exceptions
        $this->securityService->logSecurityEvent('test_event', [
            'test_data' => 'test_value'
        ]);
        
        // If we reach here, the method executed without errors
        $this->assertTrue(true);
    }
}