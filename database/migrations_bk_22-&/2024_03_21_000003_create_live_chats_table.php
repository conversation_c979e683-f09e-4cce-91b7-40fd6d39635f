<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('live_chats')) {
            Schema::create('live_chats', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->onDelete('cascade');
                $table->string('status')->default('pending');
                $table->string('session_id')->unique();
                $table->timestamp('started_at')->nullable();
                $table->timestamp('ended_at')->nullable();
                $table->timestamp('last_message_at')->nullable();
                $table->timestamps();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('live_chats');
    }
}; 