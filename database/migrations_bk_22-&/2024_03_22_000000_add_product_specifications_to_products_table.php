<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'material')) {
                $table->string('material', 255)->nullable()->after('product_details');
            }
            if (!Schema::hasColumn('products', 'warranty')) {
                $table->string('warranty', 255)->nullable()->after('material');
            }
            if (!Schema::hasColumn('products', 'often_buy_together')) {
                $table->string('often_buy_together', 255)->nullable()->after('warranty');
            }
            if (!Schema::hasColumn('products', 'key_features')) {
                $table->text('key_features')->nullable()->after('often_buy_together');
            }
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'material',
                'capacity',
                'dimensions',
                'water_resistance',
                'warranty',
                'key_features'
            ]);
        });
    }
};
