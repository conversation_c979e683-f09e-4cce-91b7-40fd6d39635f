<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDropshipperOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //if (!Schema::hasTable('dropshipper_offers')) {
            Schema::create('dropshipper_offers', function (Blueprint $table) {
                $table->id();
                $table->string('offer_id');
                $table->string('title');
                $table->text('description');
                $table->decimal('discount_percentage', 8, 2)->nullable();
                $table->decimal('discount_amount', 10, 2)->nullable();
                $table->decimal('min_order_value', 10, 2)->nullable();
                $table->string('promo_code');
                $table->timestamp('start_date');
                $table->timestamp('expiration_date');
                $table->boolean('is_exclusive')->default(false);
                $table->boolean('is_seasonal')->default(false);
                $table->boolean('is_personalized')->default(false);
                $table->json('categories')->nullable();
                $table->json('products')->nullable();
                $table->integer('usage_limit')->nullable();
                $table->integer('used_count')->default(0);
                $table->boolean('is_redeemed')->default(false);
                $table->boolean('is_expired')->default(false);
                $table->integer('priority')->default(0);
                $table->json('tags')->nullable();
                $table->json('terms_and_conditions')->nullable();
                $table->boolean('is_dropshipper_only')->default(true);
                $table->json('bulk_discount_thresholds')->nullable();
                $table->decimal('savings_amount', 10, 2)->nullable();
                $table->timestamps();
            });
        //}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dropshipper_offers');
    }
}
