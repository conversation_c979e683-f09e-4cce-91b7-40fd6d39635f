<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('tickets')) {
            Schema::create('tickets', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('subject');
                $table->text('description');
                $table->enum('status', ['pending', 'open', 'solved', 'closed', 'closed_by_user', 'reopen'])->default('pending');
                $table->enum('priority', ['low', 'medium', 'high'])->nullable();
                $table->foreignId('ticket_category_id')->nullable()->constrained('ticket_categories')->onDelete('set null');
                $table->timestamps();
                $table->softDeletes();

                // Indexes
                $table->index(['user_id', 'status']);
                $table->index('priority');
                $table->index('ticket_category_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
}; 