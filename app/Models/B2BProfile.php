<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class B2BProfile extends Model
{
    protected $table = 'b2b_seller_profile';
    protected $with = ['user'];
    protected $fillable = [
            'user_id',
            'slug',
            'name',
            'verification_status',
            'business_address',
            'shipping_address',
            'tax_id',
            'business_type',
            'website',
            'business_category'
        ];
    public function dropshipper()
    {
      return $this->belongsTo(B2BProfile::class);
    }

    public function user()
    {
      return $this->belongsTo(User::class);
    }

    public function seller_package(){
      return $this->belongsTo(SellerPackage::class);
    }
    public function followers(){
      return $this->hasMany(FollowSeller::class);
    }
}
