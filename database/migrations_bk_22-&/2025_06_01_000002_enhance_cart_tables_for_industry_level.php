<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance cart_info table for industry-level features
        Schema::table('cart_info', function (Blueprint $table) {
            // Add columns only if they don't exist
            if (!Schema::hasColumn('cart_info', 'temp_user_id')) {
                $table->string('temp_user_id')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('cart_info', 'status')) {
                $table->enum('status', ['active', 'abandoned', 'merged', 'expired'])->default('active')->after('temp_user_id');
            }
            if (!Schema::hasColumn('cart_info', 'expires_at')) {
                $table->timestamp('expires_at')->nullable()->after('status');
            }
            if (!Schema::hasColumn('cart_info', 'last_activity_at')) {
                $table->timestamp('last_activity_at')->nullable()->after('expires_at');
            }
            if (!Schema::hasColumn('cart_info', 'session_id')) {
                $table->string('session_id')->nullable()->after('last_activity_at');
            }
            if (!Schema::hasColumn('cart_info', 'promo_code_applied')) {
                $table->json('promo_code_applied')->nullable()->after('session_id');
            }
            if (!Schema::hasColumn('cart_info', 'gift_options')) {
                $table->json('gift_options')->nullable()->after('promo_code_applied');
            }
            if (!Schema::hasColumn('cart_info', 'notes')) {
                $table->text('notes')->nullable()->after('gift_options');
            }
        });

        // Add indexes only if they don't exist
        $this->addIndexIfNotExists('cart_info', 'cart_info_temp_user_id_index', ['temp_user_id']);
        $this->addIndexIfNotExists('cart_info', 'cart_info_session_id_index', ['session_id']);
        $this->addIndexIfNotExists('cart_info', 'cart_info_temp_user_id_status_index', ['temp_user_id', 'status']);
        $this->addIndexIfNotExists('cart_info', 'cart_info_user_id_status_index', ['user_id', 'status']);
        $this->addIndexIfNotExists('cart_info', 'cart_info_expires_at_index', ['expires_at']);
        $this->addIndexIfNotExists('cart_info', 'cart_info_last_activity_at_index', ['last_activity_at']);

        // Enhance carts table (cart items)
        Schema::table('carts', function (Blueprint $table) {
            // Add columns only if they don't exist
            if (!Schema::hasColumn('carts', 'temp_user_id')) {
                $table->string('temp_user_id')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('carts', 'status')) {
                $table->enum('status', ['active', 'saved_for_later', 'removed'])->default('active')->after('temp_user_id');
            }
            if (!Schema::hasColumn('carts', 'original_price')) {
                $table->decimal('original_price', 10, 2)->nullable()->after('price');
            }
            if (!Schema::hasColumn('carts', 'options')) {
                $table->json('options')->nullable()->after('original_price');
            }
            if (!Schema::hasColumn('carts', 'gift_wrap')) {
                $table->boolean('gift_wrap')->default(false)->after('options');
            }
            if (!Schema::hasColumn('carts', 'gift_message')) {
                $table->text('gift_message')->nullable()->after('gift_wrap');
            }
            if (!Schema::hasColumn('carts', 'last_modified')) {
                $table->timestamp('last_modified')->nullable()->after('gift_message');
            }
            if (!Schema::hasColumn('carts', 'added_by')) {
                $table->enum('added_by', ['manual', 'quick_add', 'recommendation', 'reorder'])->default('manual')->after('last_modified');
            }
        });

        // Add indexes for carts table
        $this->addIndexIfNotExists('carts', 'carts_temp_user_id_index', ['temp_user_id']);
        $this->addIndexIfNotExists('carts', 'carts_status_index', ['status']);
        $this->addIndexIfNotExists('carts', 'carts_user_id_status_index', ['user_id', 'status']);
        $this->addIndexIfNotExists('carts', 'carts_temp_user_id_status_index', ['temp_user_id', 'status']);
        $this->addIndexIfNotExists('carts', 'carts_last_modified_index', ['last_modified']);

        // Create cart_events table for tracking cart activities
        if (!Schema::hasTable('cart_events')) {
            Schema::create('cart_events', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('cart_info_id')->nullable();
                $table->unsignedInteger('user_id')->nullable();
                $table->string('temp_user_id')->nullable();
                $table->string('event_type'); // 'item_added', 'item_removed', 'quantity_updated', etc.
                $table->json('event_data')->nullable();
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->timestamps();

                $table->index(['cart_info_id', 'event_type']);
                $table->index(['user_id', 'event_type']);
                $table->index(['temp_user_id', 'event_type']);
                $table->index('created_at');
            });
        }

        // Create cart_recovery table for abandoned cart recovery
        if (!Schema::hasTable('cart_recovery')) {
            Schema::create('cart_recovery', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('cart_info_id');
                $table->unsignedInteger('user_id')->nullable();
                $table->string('temp_user_id')->nullable();
                $table->string('recovery_token')->unique();
                $table->string('email')->nullable();
                $table->timestamp('abandoned_at');
                $table->timestamp('recovered_at')->nullable();
                $table->integer('recovery_attempts')->default(0);
                $table->timestamp('last_reminder_sent_at')->nullable();
                $table->json('recovery_data')->nullable();
                $table->timestamps();

                $table->index(['cart_info_id', 'abandoned_at']);
                $table->index(['email', 'abandoned_at']);
                $table->index('recovery_token');
                $table->index('last_reminder_sent_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop new tables
        Schema::dropIfExists('cart_recovery');
        Schema::dropIfExists('cart_events');

        // Remove added columns from cart_info if they exist
        Schema::table('cart_info', function (Blueprint $table) {
            $columnsToRemove = ['notes', 'gift_options', 'promo_code_applied', 'session_id', 'last_activity_at', 'expires_at', 'status', 'temp_user_id'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('cart_info', $column)) {
                    $table->dropColumn($column);
                }
            }
        });

        // Remove added columns from carts if they exist
        Schema::table('carts', function (Blueprint $table) {
            $columnsToRemove = ['added_by', 'last_modified', 'gift_message', 'gift_wrap', 'options', 'original_price', 'status', 'temp_user_id'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('carts', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }

    /**
     * Add index if it doesn't exist
     */
    private function addIndexIfNotExists($table, $indexName, $columns): void
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
        if (empty($indexes)) {
            DB::statement("ALTER TABLE {$table} ADD INDEX {$indexName} (" . implode(',', $columns) . ")");
        }
    }
}; 