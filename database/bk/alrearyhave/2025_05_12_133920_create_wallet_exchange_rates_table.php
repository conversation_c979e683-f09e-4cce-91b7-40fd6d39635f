<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('wallet_exchange_rates')) {
            Schema::create('wallet_exchange_rates', function (Blueprint $table) {
                $table->id();
                $table->string('from_type')->comment('balance, reward_points, promotional_credits');
                $table->string('to_type')->comment('balance, reward_points, promotional_credits');
                $table->decimal('rate', 20, 6)->comment('Exchange rate value');
            $table->decimal('min_amount', 20, 2)->default(0)->comment('Minimum amount required');
            $table->decimal('max_amount', 20, 2)->default(0)->comment('Maximum amount (0 = no limit)');
            $table->decimal('fee_fixed', 20, 2)->default(0)->comment('Fixed fee for exchange');
            $table->decimal('fee_percent', 5, 2)->default(0)->comment('Percentage fee for exchange');
                $table->boolean('status')->default(1)->comment('1=active, 0=inactive');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_exchange_rates');
    }
};
