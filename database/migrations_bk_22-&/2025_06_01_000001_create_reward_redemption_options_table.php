<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('reward_redemption_options')) {
            Schema::create('reward_redemption_options', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description')->nullable();
                $table->integer('points_cost');
            $table->enum('category', ['voucher', 'product', 'gift-card', 'donation'])->default('voucher');
            $table->decimal('value', 10, 2)->nullable();
            $table->string('image_url')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_limited')->default(false);
            $table->integer('remaining_quantity')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_redemption_options');
    }
}; 