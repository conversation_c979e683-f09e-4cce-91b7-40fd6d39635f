<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('return_process_steps')) {
            Schema::create('return_process_steps', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description');
                $table->integer('order')->default(0);
                $table->foreignId('knowledge_base_id')->constrained('return_knowledge_bases')->onDelete('cascade');
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('return_process_steps');
    }
}; 