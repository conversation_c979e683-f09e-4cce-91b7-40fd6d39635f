<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add reactions to conversation_messages
        Schema::table('conversation_messages', function (Blueprint $table) {
            $table->json('reactions')->nullable()->after('content');
        });

        // Add reactions to messages
        Schema::table('messages', function (Blueprint $table) {
            $table->json('reactions')->nullable()->after('message');
        });

        // Add reactions to ticket_messages
        Schema::table('ticket_messages', function (Blueprint $table) {
            $table->json('reactions')->nullable()->after('message');
        });

        // Add reactions to order_messages
        Schema::table('order_messages', function (Blueprint $table) {
            $table->json('reactions')->nullable()->after('content');
        });

        // Add reactions to live_chat_messages
        Schema::table('live_chat_messages', function (Blueprint $table) {
            $table->json('reactions')->nullable()->after('message');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('conversation_messages', function (Blueprint $table) {
            $table->dropColumn('reactions');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn('reactions');
        });

        Schema::table('ticket_messages', function (Blueprint $table) {
            $table->dropColumn('reactions');
        });

        Schema::table('order_messages', function (Blueprint $table) {
            $table->dropColumn('reactions');
        });

        Schema::table('live_chat_messages', function (Blueprint $table) {
            $table->dropColumn('reactions');
        });
    }
}; 