<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddFulltextIndexesToProductsTable extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop existing indexes if they exist
         //   DB::statement('ALTER TABLE products DROP INDEX IF EXISTS products_name_fulltext');
         //   DB::statement('ALTER TABLE products DROP INDEX IF EXISTS products_tags_fulltext');
            
            // Add FULLTEXT indexes
          //  DB::statement('ALTER TABLE products ADD FULLTEXT products_name_fulltext (name)');
         //   DB::statement('ALTER TABLE products ADD FULLTEXT products_tags_fulltext (tags)');
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            DB::statement('ALTER TABLE products DROP INDEX IF EXISTS products_name_fulltext');
            DB::statement('ALTER TABLE products DROP INDEX IF EXISTS products_tags_fulltext');
        });
    }
}