<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix cart_info table if it exists
        if (Schema::hasTable('cart_info')) {
            // Add missing columns to cart_info table
            Schema::table('cart_info', function (Blueprint $table) {
                // Check if each column exists before adding it
                if (!Schema::hasColumn('cart_info', 'cart_info_id')) {
                    $table->string('cart_info_id')->nullable()->index();
                }
                
                if (!Schema::hasColumn('cart_info', 'item_count')) {
                    $table->integer('item_count')->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'total_quantity')) {
                    $table->integer('total_quantity')->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'subtotal')) {
                    $table->decimal('subtotal', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'shipping_total')) {
                    $table->decimal('shipping_total', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'tax_total')) {
                    $table->decimal('tax_total', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'discount')) {
                    $table->decimal('discount', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'currency')) {
                    $table->string('currency')->default('USD');
                }
                
                if (!Schema::hasColumn('cart_info', 'coupon_code')) {
                    $table->string('coupon_code')->nullable();
                }
                
                if (!Schema::hasColumn('cart_info', 'wallet_amount_used')) {
                    $table->decimal('wallet_amount_used', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'reward_points_used')) {
                    $table->integer('reward_points_used')->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'reward_points_discount')) {
                    $table->decimal('reward_points_discount', 10, 2)->default(0);
                }
            });
        } else {
            // Create cart_info table if it doesn't exist
            Schema::create('cart_info', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->foreignId('user_id')->nullable()->constrained();
                $table->foreignId('address_id')->nullable();
                $table->integer('item_count')->default(0);
                $table->integer('total_quantity')->default(0);
                $table->decimal('subtotal', 10, 2)->default(0);
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('coupon_code')->nullable();
                $table->decimal('shipping_total', 10, 2)->default(0);
                $table->decimal('tax_total', 10, 2)->default(0);
                $table->decimal('total', 10, 2)->default(0);
                $table->string('currency')->default('USD');
                $table->decimal('wallet_amount_used', 10, 2)->default(0);
                $table->integer('reward_points_used')->default(0);
                $table->decimal('reward_points_discount', 10, 2)->default(0);
                $table->timestamps();
            });
        }

        // Fix cart_items table if it exists
        if (Schema::hasTable('cart_items')) {
            // Add missing columns to cart_items table
            Schema::table('cart_items', function (Blueprint $table) {
                // Check if each column exists before adding it
                if (!Schema::hasColumn('cart_items', 'cart_info_id')) {
                    $table->uuid('cart_info_id')->nullable()->index();
                }
                
                if (!Schema::hasColumn('cart_items', 'user_id')) {
                    $table->foreignId('user_id')->nullable();
                }
                
                if (!Schema::hasColumn('cart_items', 'product_id')) {
                    $table->foreignId('product_id')->nullable();
                }
                
                if (!Schema::hasColumn('cart_items', 'variation')) {
                    $table->text('variation')->nullable();
                }
                
                if (!Schema::hasColumn('cart_items', 'price')) {
                    $table->decimal('price', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_items', 'quantity')) {
                    $table->integer('quantity')->default(1);
                }
            });
        } else {
            // Create cart_items table if it doesn't exist
            Schema::create('cart_items', function (Blueprint $table) {
                $table->id();
                $table->uuid('cart_info_id')->nullable()->index();
                $table->foreignId('user_id')->nullable();
                $table->string('temp_user_id')->nullable();
                $table->foreignId('product_id')->nullable();
                $table->foreignId('owner_id')->nullable();
                $table->text('variation')->nullable();
                $table->decimal('price', 10, 2)->default(0);
                $table->decimal('tax', 10, 2)->default(0);
                $table->decimal('shipping_cost', 10, 2)->default(0);
                $table->string('shipping_type')->nullable();
                $table->integer('pickup_point')->nullable();
                $table->integer('carrier_id')->nullable();
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('product_referral_code')->nullable();
                $table->string('coupon_code')->nullable();
                $table->boolean('coupon_applied')->default(false);
                $table->integer('quantity')->default(1);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't roll back to prevent data loss
    }
}; 