<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\V3\ApiResponse;

class EmailVerificationRateLimit extends ApiResponse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $email = $request->input('email');
        $ip = $request->ip();
        $userAgent = $request->userAgent();

        if (!$email) {
            return $next($request);
        }

        // Enhanced rate limiting with multiple layers
        $rateLimitResult = $this->checkRateLimits($email, $ip, $userAgent, $request);
        
        if ($rateLimitResult !== true) {
            return $rateLimitResult;
        }

        // Log security event for monitoring
        $this->logSecurityEvent($request, $email, $ip);

        return $next($request);
    }

    /**
     * Check multiple rate limiting layers
     */
    private function checkRateLimits($email, $ip, $userAgent, Request $request)
    {
        // Layer 1: Rate limiting by email (5 requests per hour per email)
        $emailKey = 'email_verification_email:' . hash('sha256', $email);
        $emailAttempts = RateLimiter::attempts($emailKey);
        
        if ($emailAttempts >= 5) {
            $availableAt = RateLimiter::availableAt($emailKey);
            $retryAfter = $availableAt - time();
            
            $this->logRateLimitExceeded('email', $email, $ip, $emailAttempts);
            
            return $this->error(
                'Too many requests',
                'Too many verification requests for this email. Please try again in ' . ceil($retryAfter / 60) . ' minutes.',
                429,
                ['retry_after' => $retryAfter, 'type' => 'email_limit']
            );
        }

        // Layer 2: Rate limiting by IP (20 requests per hour per IP)
        $ipKey = 'email_verification_ip:' . $ip;
        $ipAttempts = RateLimiter::attempts($ipKey);
        
        if ($ipAttempts >= 20) {
            $availableAt = RateLimiter::availableAt($ipKey);
            $retryAfter = $availableAt - time();
            
            $this->logRateLimitExceeded('ip', $email, $ip, $ipAttempts);
            
            return $this->error(
                'Too many requests',
                'Too many verification requests from this IP address. Please try again later.',
                429,
                ['retry_after' => $retryAfter, 'type' => 'ip_limit']
            );
        }

        // Layer 3: Rate limiting by User Agent + IP combination (stricter for suspicious patterns)
        $userAgentKey = 'email_verification_ua:' . hash('sha256', $ip . $userAgent);
        $userAgentAttempts = RateLimiter::attempts($userAgentKey);
        
        if ($userAgentAttempts >= 10) {
            $availableAt = RateLimiter::availableAt($userAgentKey);
            $retryAfter = $availableAt - time();
            
            $this->logRateLimitExceeded('user_agent', $email, $ip, $userAgentAttempts, $userAgent);
            
            return $this->error(
                'Too many requests',
                'Suspicious activity detected. Please try again later.',
                429,
                ['retry_after' => $retryAfter, 'type' => 'pattern_limit']
            );
        }

        // Layer 4: Global rate limiting (100 requests per hour globally)
        $globalKey = 'email_verification_global';
        $globalAttempts = RateLimiter::attempts($globalKey);
        
        if ($globalAttempts >= 100) {
            $availableAt = RateLimiter::availableAt($globalKey);
            $retryAfter = $availableAt - time();
            
            $this->logRateLimitExceeded('global', $email, $ip, $globalAttempts);
            
            return $this->error(
                'Service temporarily unavailable',
                'Email verification service is temporarily unavailable. Please try again later.',
                503,
                ['retry_after' => $retryAfter, 'type' => 'global_limit']
            );
        }

        // Layer 5: Suspicious pattern detection (rapid requests from same source)
        if ($this->detectSuspiciousPattern($ip, $email)) {
            $this->logSuspiciousActivity($email, $ip, $request);
            
            return $this->error(
                'Suspicious activity detected',
                'Unusual activity detected. Please try again later or contact support.',
                429,
                ['type' => 'suspicious_pattern']
            );
        }

        // Increment all rate limit counters
        RateLimiter::hit($emailKey, 3600); // 1 hour
        RateLimiter::hit($ipKey, 3600); // 1 hour
        RateLimiter::hit($userAgentKey, 3600); // 1 hour
        RateLimiter::hit($globalKey, 3600); // 1 hour

        return true;
    }

    /**
     * Detect suspicious patterns in requests
     */
    private function detectSuspiciousPattern($ip, $email)
    {
        // Check for rapid-fire requests (more than 3 requests in 1 minute)
        $rapidKey = 'rapid_requests:' . $ip;
        $rapidAttempts = Cache::get($rapidKey, 0);
        
        if ($rapidAttempts >= 3) {
            return true;
        }
        
        Cache::put($rapidKey, $rapidAttempts + 1, 60); // 1 minute

        // Check for email enumeration patterns (many different emails from same IP)
        $enumerationKey = 'email_enumeration:' . $ip;
        $emails = Cache::get($enumerationKey, []);
        
        if (!in_array($email, $emails)) {
            $emails[] = $email;
            Cache::put($enumerationKey, $emails, 3600); // 1 hour
        }
        
        // Flag if more than 10 different emails from same IP in 1 hour
        if (count($emails) > 10) {
            return true;
        }

        return false;
    }

    /**
     * Log rate limit exceeded events
     */
    private function logRateLimitExceeded($type, $email, $ip, $attempts, $userAgent = null)
    {
        Log::warning('Email verification rate limit exceeded', [
            'type' => $type,
            'email' => hash('sha256', $email), // Hash email for privacy
            'ip' => $ip,
            'attempts' => $attempts,
            'user_agent' => $userAgent,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Log suspicious activity
     */
    private function logSuspiciousActivity($email, $ip, Request $request)
    {
        Log::warning('Suspicious email verification activity detected', [
            'email' => hash('sha256', $email), // Hash email for privacy
            'ip' => $ip,
            'user_agent' => $request->userAgent(),
            'headers' => $request->headers->all(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Log security events for monitoring
     */
    private function logSecurityEvent(Request $request, $email, $ip)
    {
        Log::info('Email verification request', [
            'email' => hash('sha256', $email), // Hash email for privacy
            'ip' => $ip,
            'user_agent' => $request->userAgent(),
            'endpoint' => $request->path(),
            'timestamp' => now()->toISOString()
        ]);
    }
}