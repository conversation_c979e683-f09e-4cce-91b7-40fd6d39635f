<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('conversations')) {
            Schema::create('conversations', function (Blueprint $table) {
                $table->id();
                $table->string('title')->nullable();
                $table->string('type')->default('private');
                $table->boolean('is_archived')->default(false);
                $table->timestamps();
                $table->softDeletes();
            });
        }

    }

    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
