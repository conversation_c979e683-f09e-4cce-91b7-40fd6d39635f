<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAddressIdToCartInfoTable extends Migration
{
    public function up()
    {
        Schema::table('cart_info', function (Blueprint $table) {
            if (!Schema::hasColumn('cart_info', 'address_id')) {
                $table->unsignedInteger('address_id')->nullable()->after('id');
            }

            // Optional: add foreign key constraint if address_id relates to addresses table
            // $table->foreign('address_id')->references('id')->on('addresses')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('cart_info', function (Blueprint $table) {
            // $table->dropForeign(['address_id']); // if foreign key was added
            $table->dropColumn('address_id');
        });
    }
}

