<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupons', function (Blueprint $table) {
            if (!Schema::hasColumn('coupons', 'is_personalized')) {
                $table->boolean('is_personalized')->default(false)->after('featured');
            }
            if (!Schema::hasColumn('coupons', 'applicable_to')) {
                $table->json('applicable_to')->nullable()->after('is_personalized');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropColumn('is_personalized');
            $table->dropColumn('applicable_to');
        });
    }
};
