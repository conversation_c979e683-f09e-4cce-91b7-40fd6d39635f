<div class="aiz-sidebar-wrap">
    <div class="aiz-sidebar left c-scrollbar">
        <div class="aiz-side-nav-logo-wrap">
            <a href="{{ route('admin.dashboard') }}" class="d-block text-left">
                @if (get_setting('system_logo_white') != null)
                    <img class="mw-100" src="{{ uploaded_asset(get_setting('system_logo_white')) }}" class="brand-icon"
                        alt="{{ get_setting('site_name') }}">
                @else
                    <img class="mw-100" src="{{ static_asset('assets/img/logo.png') }}" class="brand-icon"
                        alt="{{ get_setting('site_name') }}">
                @endif
            </a>
        </div>
        <div class="aiz-side-nav-wrap">
            <div class="px-20px mb-3">
                <input class="form-control bg-soft-secondary border-0 form-control-sm text-white" type="text"
                    name="" placeholder="{{ translate('Search in menu') }}" id="menu-search"
                    onkeyup="menuSearch()">
            </div>
            <ul class="aiz-side-nav-list" id="search-menu">
            </ul>
            <ul class="aiz-side-nav-list" id="main-menu" data-toggle="aiz-side-menu">

                {{-- Dashboard --}}
                @can('admin_dashboard')
                    <li class="aiz-side-nav-item">
                        <a href="{{ route('admin.dashboard') }}" class="aiz-side-nav-link">
                            <i class="las la-home aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Dashboard') }}</span>
                        </a>
                    </li>
                @endcan

                <!-- POS Addon-->
                @if (addon_is_activated('pos_system') &&
                        (auth()->user()->can('pos_manager') || auth()->user()->can('pos_configuration')))
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-tasks aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('POS System') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('pos_manager')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('poin-of-sales.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['poin-of-sales.index', 'poin-of-sales.create']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('POS Manager') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('pos_configuration')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('poin-of-sales.activation') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('POS Configuration') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endif

                <!-- Product -->
                @canany(['add_new_product', 'show_all_products', 'show_in_house_products', 'show_seller_products',
                    'show_digital_products', 'product_bulk_import', 'product_bulk_export', 'view_product_categories',
                    'view_all_brands', 'view_product_attributes', 'view_colors', 'view_product_reviews'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-shopping-cart aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Products') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <!--Submenu-->
                        <ul class="aiz-side-nav-list level-2">
                            @can('add_new_product')
                                <li class="aiz-side-nav-item">
                                    <a class="aiz-side-nav-link" href="{{ route('products.create') }}">
                                        <span class="aiz-side-nav-text">{{ translate('Add New product') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('show_all_products')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('products.all') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('All Products') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('show_in_house_products')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('products.admin') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['products.admin', 'products.create', 'products.admin.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('In House Products') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('show_digital_products')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('digitalproducts.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['digitalproducts.index', 'digitalproducts.create', 'digitalproducts.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Digital Products') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @if (get_setting('vendor_system_activation') == 1)
                                @can('show_seller_products')
                                    {{-- <li class="aiz-side-nav-item">
                                        <a href="{{route('products.seller')}}" class="aiz-side-nav-link {{ areActiveRoutes(['products.seller', 'products.seller.edit']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Seller Products') }}</span>
                                        </a>
                                    </li> --}}
                                    <li class="aiz-side-nav-item">
                                        <a href="javascript:void(0);" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Seller Product') }}</span>
                                            <span class="aiz-side-nav-arrow"></span>
                                        </a>
                                        <ul class="aiz-side-nav-list level-3">
                                            <li class="aiz-side-nav-item">
                                                <a href="{{ route('products.seller', 'physical') }}" class="aiz-side-nav-link">
                                                    <span class="aiz-side-nav-text">{{ translate('Physical Products') }}</span>
                                                </a>
                                            </li>
                                            <li class="aiz-side-nav-item">
                                                <a href="{{ route('products.seller', 'digital') }}" class="aiz-side-nav-link">
                                                    <span class="aiz-side-nav-text">{{ translate('Digital Products') }}</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                @endcan
                            @endif
                            {{--
                            @can('product_bulk_import')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('product_bulk_upload.index') }}" class="aiz-side-nav-link" >
                                        <span class="aiz-side-nav-text">{{ translate('Bulk Import') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('product_bulk_export')
                                <li class="aiz-side-nav-item">
                                    <a href="{{route('product_bulk_export.index')}}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{translate('Bulk Export')}}</span>
                                    </a>
                                </li>
                            @endcan


                            <li class="aiz-side-nav-item">
                                <a href="{{route('product_bulk_edit')}}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{translate('Bulk Edit')}}</span>
                                </a>
                            </li>


                            --}}


                            <!-- added by arif start -->
                            {-- Product bulk route --}}

                            <li class="aiz-side-nav-item">
                                <a href="javascript:void(0);" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Product Bulk') }}</span>
                                    <span class="aiz-side-nav-arrow"></span>
                                </a>
                                <ul class="aiz-side-nav-list level-3">
                                    @can('product_bulk_export')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_product_upload.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Product Import') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('product_bulk_export')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_product_export') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Product Export') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('product_bulk_export')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_product_edit') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Product Edit') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    {{--
                                    @can('product_bulk_export')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('bulk_product_deleted') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{translate('Bulk Product Delete')}}</span>
                                        </a>
                                    </li>
                                    @endcan
                                    --}}
                                </ul>
                            </li>




                            <!-- added by arif end-->




                            @can('view_product_categories')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('categories.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['categories.index', 'categories.create', 'categories.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Category') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_all_brands')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('brands.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['brands.index', 'brands.create', 'brands.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Brand') }}</span>
                                    </a>
                                </li>
                            @endcan


                            {{-- Brand Bulk Upload route --}}

                            <li class="aiz-side-nav-item">
                                <a href="javascript:void(0);" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Brand Bulk Upload') }}</span>
                                    <span class="aiz-side-nav-arrow"></span>
                                </a>
                                <ul class="aiz-side-nav-list level-3">
                                    @can('view_all_brands')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_brand_upload.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Brand Import') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('view_all_brands')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_brand_export.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Brand Export') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('view_all_brands')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('bulk_brand_edit') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Bulk Brand Edit') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    {{--
                                    @can('view_all_brands')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('bulk_brand_deleted') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{translate('Bulk Brand Delete')}}</span>
                                        </a>
                                    </li>
                                    @endcan
                                    --}}
                                </ul>
                            </li>
                            {{-- Brand Bulk Upload route --}}


                            <!-- Suppliers -->
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('suppliers.index') }}" class="aiz-side-nav-link">
                                    <i class="las la-backward aiz-side-nav-icon"></i>
                                    <span class="aiz-side-nav-text">{{ translate('Supplier') }}</span>
                                </a>
                            </li>


                            @can('view_product_attributes')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('attributes.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['attributes.index', 'attributes.create', 'attributes.edit', 'attributes.show', 'edit-attribute-value' . '']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Attribute') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_colors')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('colors') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['colors', 'colors.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Colors') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_product_reviews')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('reviews.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Product Reviews') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
                <!-- Collections -->
                <li class="aiz-side-nav-item">
                    <a href="{{ route('collections.index') }}" class="aiz-side-nav-link">
                        <i class="las la-shopping-basket aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Collections') }}</span>
                    </a>
                </li>
                <!-- Auction Product -->
                @if (addon_is_activated('auction'))
                    @canany(['add_auction_product', 'view_all_auction_products', 'view_inhouse_auction_products',
                        'view_seller_auction_products', 'view_auction_product_orders'])
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <i class="las la-gavel aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Auction Products') }}</span>
                                @if (env('DEMO_MODE') == 'On')
                                    <span class="badge badge-inline badge-danger">Addon</span>
                                @endif
                                <span class="aiz-side-nav-arrow"></span>
                            </a>
                            <!--Submenu-->
                            <ul class="aiz-side-nav-list level-2">
                                @can('add_auction_product')
                                    <li class="aiz-side-nav-item">
                                        <a class="aiz-side-nav-link" href="{{ route('auction_product_create.admin') }}">
                                            <span class="aiz-side-nav-text">{{ translate('Add New auction product') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_all_auction_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('auction.all_products') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['auction_product_edit.admin', 'product_bids.admin']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('All Auction Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_inhouse_auction_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('auction.inhouse_products') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Inhouse Auction Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_seller_auction_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('auction.seller_products') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Seller Auction Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_auction_product_orders')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('auction_products_orders') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['auction_products_orders.index']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Auction Products Orders') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endcanany
                @endif

                <!-- Wholesale Product -->
                @if (addon_is_activated('wholesale'))
                    @canany(['add_wholesale_product', 'view_all_wholesale_products', 'view_inhouse_wholesale_products',
                        'view_sellers_wholesale_products'])
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <i class="las la-luggage-cart aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Wholesale Products') }}</span>
                                @if (env('DEMO_MODE') == 'On')
                                    <span class="badge badge-inline badge-danger">Addon</span>
                                @endif
                                <span class="aiz-side-nav-arrow"></span>
                            </a>
                            <ul class="aiz-side-nav-list level-2">
                                @can('add_wholesale_product')
                                    <li class="aiz-side-nav-item">
                                        <a class="aiz-side-nav-link" href="{{ route('wholesale_product_create.admin') }}">
                                            <span
                                                class="aiz-side-nav-text">{{ translate('Add New Wholesale Product') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_all_wholesale_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('wholesale_products.all') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['wholesale_product_edit.admin']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('All Wholesale Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_inhouse_wholesale_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('wholesale_products.in_house') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['wholesale_product_edit.admin']) }}">
                                            <span
                                                class="aiz-side-nav-text">{{ translate('In House Wholesale Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_sellers_wholesale_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('wholesale_products.seller') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['wholesale_product_edit.admin']) }}">
                                            <span
                                                class="aiz-side-nav-text">{{ translate('Seller Wholesale Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endcanany
                @endif

                <!-- Sale -->
                @canany(['view_all_orders', 'view_inhouse_orders', 'view_seller_orders', 'view_pickup_point_orders'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-money-bill aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Sales') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <!--Submenu-->
                        <ul class="aiz-side-nav-list level-2">
                            @can('view_all_orders')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('all_orders.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['all_orders.index', 'all_orders.show']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('All Orders') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_inhouse_orders')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('inhouse_orders.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['inhouse_orders.index', 'inhouse_orders.show']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Inhouse orders') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_seller_orders')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('seller_orders.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['seller_orders.index', 'seller_orders.show']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Seller Orders') }}</span>
                                    </a>
                                </li>
                            @endcan

                            @can('view_pickup_point_orders')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('pick_up_point.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['pick_up_point.index', 'pick_up_point.order_show']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Pick-up Point Order') }}</span>
                                    </a>
                                </li>
                            @endcan
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('export.paid_payment_order_details') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Download Paid Order Details') }}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                @endcanany

                <!-- Deliver Boy Addon-->
                @if (addon_is_activated('delivery_boy'))
                    @canany(['view_all_delivery_boy', 'add_delivery_boy', 'delivery_boy_payment_history',
                        'collected_histories_from_delivery_boy', 'order_cancle_request_by_delivery_boy',
                        'delivery_boy_configuration'])
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <i class="las la-truck aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Delivery Boy') }}</span>
                                @if (env('DEMO_MODE') == 'On')
                                    <span class="badge badge-inline badge-danger">Addon</span>
                                @endif
                                <span class="aiz-side-nav-arrow"></span>
                            </a>
                            <ul class="aiz-side-nav-list level-2">
                                @can('view_all_delivery_boy')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boys.index') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('All Delivery Boy') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('add_delivery_boy')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boys.create') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Add Delivery Boy') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('delivery_boy_payment_history')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boys-payment-histories') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Payment Histories') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('collected_histories_from_delivery_boy')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boys-collection-histories') }}"
                                            class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Collected Histories') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('order_cancle_request_by_delivery_boy')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boy.cancel-request') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Cancel Request') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('delivery_boy_configuration')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('delivery-boy-configuration') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Configuration') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endcanany
                @endif

                <!-- Return addon -->
                {{--                @if (addon_is_activated('refund_request')) --}}
                {{--                    @canany(['view_refund_requests', 'view_approved_refund_requests', 'view_rejected_refund_requests', 'refund_request_configuration']) --}}
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-backward aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Return') }}</span>
                        @if (env('DEMO_MODE') == 'On')
                            <span class="badge badge-inline badge-danger">Addon</span>
                        @endif
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('return_configuration') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['return_configuration']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Return FAQ Configuration') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('return_request_configuration') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['return_request_configuration']) }}">
                                <span
                                    class="aiz-side-nav-text">{{ translate('Return Request Configuration') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('return_methods.index') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['return_methods.index', 'return_methods.create', 'return_methods.edit']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Return Methods') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('return_request_list') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['return_request_list', 'return_request_details']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Return Request List') }}</span>
                            </a>
                        </li>

                        @can('view_return_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('return_requests_all') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['return_requests_all', 'reason_show']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Return Requests') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('view_approved_return_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('approve_return') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Approved Returns') }}</span>
                                </a>
                            </li>
                        @endcan

                        @can('view_rejected_return_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('rejected_return') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Rejected Returns') }}</span>
                                </a>
                            </li>
                        @endcan
                    </ul>
                </li>
                {{--                    @endcanany --}}
                {{--                @endif --}}

                <!-- change addon -->
                {{--                @if (addon_is_activated('change_request')) --}}
                {{--                    @canany(['view_change_requests', 'view_approved_change_requests', 'view_rejected_change_requests', 'change_request_configuration']) --}}
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-backward aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Change') }}</span>
                        @if (env('DEMO_MODE') == 'On')
                            <span class="badge badge-inline badge-danger">Addon</span>
                        @endif
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        @can('view_change_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('change_requests_all') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['change_requests_all', 'reason_show']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Change Requests') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('view_approved_change_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('approve_change') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Approved Changes') }}</span>
                                </a>
                            </li>
                        @endcan

                        @can('view_rejected_change_requests')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('rejected_change') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Rejected Changes') }}</span>
                                </a>
                            </li>
                        @endcan
                    </ul>
                </li>
                {{--                    @endcanany --}}
                {{--                @endif --}}

                <!-- Refund addon -->
                @if (addon_is_activated('refund_request'))
                    @canany(['view_refund_requests', 'view_approved_refund_requests', 'view_rejected_refund_requests',
                        'refund_request_configuration'])
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <i class="las la-backward aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Refunds') }}</span>
                                @if (env('DEMO_MODE') == 'On')
                                    <span class="badge badge-inline badge-danger">Addon</span>
                                @endif
                                <span class="aiz-side-nav-arrow"></span>
                            </a>

                            <ul class="aiz-side-nav-list level-2">
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('refund_methods.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['refund_methods.index', 'refund_methods.create', 'refund_methods.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Refund Methods') }}</span>
                                    </a>
                                </li>
                                @can('view_refund_requests')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('refund_requests_all') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['refund_requests_all', 'reason_show']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Refund Requests') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_approved_refund_requests')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('paid_refund') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Approved Refunds') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_rejected_refund_requests')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('rejected_refund') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Rejected Refunds') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('refund_request_configuration')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('refund_time_config') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Refund Configuration') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endcanany
                @endif
                @if (addon_is_activated('refund_request'))
                    @canany(['view_refund_requests', 'view_approved_refund_requests', 'view_rejected_refund_requests',
                        'refund_request_configuration'])
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <i class="las la-backward aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Order Cancel Request') }}</span>
                                <span class="aiz-side-nav-arrow"></span>
                            </a>
                            <ul class="aiz-side-nav-list level-2">
                                @can('view_refund_requests')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('admin_order_cancel_request_all') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['admin_order_cancel_request_all']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Order Cancel Requests') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endcanany
                @endif

                <!-- Wallet Management -->
                @can('view_wallet_management')
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-wallet aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Wallet Management') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            <li class="aiz-side-nav-item">
                                <a href="{{ url('admin/wallet') }}" class="aiz-side-nav-link {{ areActiveRoutes(['wallet.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('All Wallets') }}</span>
                                </a>
                            </li>
                            <li class="aiz-side-nav-item">
                                <a href="{{ url('admin/wallet/exchange-rates') }}" class="aiz-side-nav-link {{ areActiveRoutes(['wallet.exchange_rates']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Exchange Rates') }}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                @endcan

                <!-- Customers -->
                @canany(['view_all_customers', 'view_classified_products', 'view_classified_packages'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-user-friends aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Customers') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('view_all_customers')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('customers.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Customer list') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @if (get_setting('classified_product') == 1)
                                @can('view_classified_products')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('classified_products') }}" class="aiz-side-nav-link">
                                            <span class="aiz-side-nav-text">{{ translate('Classified Products') }}</span>
                                        </a>
                                    </li>
                                @endcan
                                @can('view_classified_packages')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('customer_packages.index') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['customer_packages.index', 'customer_packages.create', 'customer_packages.edit']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Classified Packages') }}</span>
                                        </a>
                                    </li>
                                @endcan
                            @endif
                        </ul>
                    </li>
                @endcanany

                <!-- Sellers -->
                @canany(['view_all_seller', 'seller_payment_history', 'view_seller_payout_requests',
                    'seller_commission_configuration', 'view_all_seller_packages',
                    'seller_verification_form_configuration'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-user aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Sellers') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('view_all_seller')
                                <li class="aiz-side-nav-item">
                                    @php
                                        $sellers = \App\Models\Shop::where('verification_status', 0)
                                            ->where('verification_info', '!=', null)
                                            ->count();
                                    @endphp
                                    <a href="{{ route('sellers.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['sellers.index', 'sellers.create', 'sellers.edit', 'sellers.payment_history', 'sellers.approved', 'sellers.profile_modal', 'sellers.show_verification_request']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('All Seller') }}</span>
                                        @if ($sellers > 0)<span
                                                class="badge badge-info">{{ $sellers }}</span> @endif
                                    </a>
                                </li>
                            @endcan
                            @can('seller_payment_history')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('sellers.payment_histories') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Payouts') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_seller_payout_requests')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('withdraw_requests_all') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Payout Requests') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('seller_commission_configuration')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('business_settings.vendor_commission') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Seller Commission') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @if (addon_is_activated('seller_subscription'))
                                @can('view_all_seller_packages')
                                    <li class="aiz-side-nav-item">
                                        <a href="{{ route('seller_packages.index') }}"
                                            class="aiz-side-nav-link {{ areActiveRoutes(['seller_packages.index', 'seller_packages.create', 'seller_packages.edit']) }}">
                                            <span class="aiz-side-nav-text">{{ translate('Seller Packages') }}</span>
                                            @if (env('DEMO_MODE') == 'On')
                                                <span class="badge badge-inline badge-danger">Addon</span>
                                            @endif
                                        </a>
                                    </li>
                                @endcan
                            @endif
                            @can('seller_verification_form_configuration')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('seller_verification_form.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Seller Verification Form') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-user aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('dropshipper') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">

                        <li class="aiz-side-nav-item">
                            @can('view_all_seller')
                            <li class="aiz-side-nav-item">
                                @php
                                    $dropshipper = \App\Models\Shop::where('verification_status', 0)
                                        ->where('verification_info', '!=', null)
                                        ->count();
                                @endphp
                                <a href="{{ route('dropshipper.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['dropshipper.index', 'dropshipper.create', 'dropshipper.edit', 'dropshipper.payment_history', 'dropshipper.approved', 'dropshipper.profile_modal', 'dropshipper.show_verification_request']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('All Dropshipper') }}</span>
                                    @if ($dropshipper > 0)<span
                                            class="badge badge-info">{{ $dropshipper }}</span> @endif
                                </a>
                            </li>
                        @endcan
                </li>
                {{--
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('dropshipper.orders.index') }}" class="aiz-side-nav-link ">
                                <span class="aiz-side-nav-text">{{ translate('dropshipper Orders') }}</span>
                            </a>
                        </li>
                        --}}
            </ul>
            </li>
            {{-- Uploads Files --}}
            <li class="aiz-side-nav-item">
                <a href="{{ route('uploaded-files.index') }}"
                    class="aiz-side-nav-link {{ areActiveRoutes(['uploaded-files.create']) }}">
                    <i class="las la-folder-open aiz-side-nav-icon"></i>
                    <span class="aiz-side-nav-text">{{ translate('Uploaded Files') }}</span>
                </a>
            </li>

            <!-- Reports -->
            @canany(['in_house_product_sale_report', 'seller_products_sale_report', 'products_stock_report',
                'product_wishlist_report', 'user_search_report', 'commission_history_report', 'wallet_transaction_report'])
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-file-alt aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Reports') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        @can('in_house_product_sale_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('in_house_sale_report.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['in_house_sale_report.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('In House Product Sale') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('seller_products_sale_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('seller_sale_report.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['seller_sale_report.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Seller Products Sale') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('products_stock_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('stock_report.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['stock_report.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Products Stock') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('product_wishlist_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('wish_report.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['wish_report.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Products wishlist') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('user_search_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('user_search_report.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['user_search_report.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('User Searches') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('commission_history_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('commission-log.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Commission History') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('wallet_transaction_report')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('wallet-history.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Wallet Recharge History') }}</span>
                                </a>
                            </li>
                        @endcan
                    </ul>
                </li>
            @endcanany

            <!--Blog System-->
            @canany(['view_blogs', 'view_blog_categories'])
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-bullhorn aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Blog System') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        @can('view_blogs')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('blog.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['blog.create', 'blog.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('All Posts') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('view_blog_categories')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('blog-category.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['blog-category.create', 'blog-category.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Categories') }}</span>
                                </a>
                            </li>
                        @endcan
                    </ul>
                </li>
            @endcanany

            <!-- marketing -->
            @canany(['view_all_flash_deals', 'send_newsletter', 'send_bulk_sms', 'view_all_subscribers',
                'view_all_coupons'])
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-bullhorn aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Marketing') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('website.popular_products') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['product_by_categories.index', 'product_by_categories.create', 'product_by_categories.edit']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Popular Products') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('product_by_categories.index') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['product_by_categories.index', 'product_by_categories.create', 'product_by_categories.edit']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Top Picks of  Category') }}</span>
                            </a>
                        </li>
                        @can('view_all_flash_deals')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('flash_deals.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['flash_deals.index', 'flash_deals.create', 'flash_deals.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Flash deals') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('send_newsletter')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('newsletters.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Newsletters') }}</span>
                                </a>
                            </li>
                        @endcan
                        @if (addon_is_activated('otp_system') && auth()->user()->can('send_bulk_sms'))
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('sms.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Bulk SMS') }}</span>
                                    @if (env('DEMO_MODE') == 'On')
                                        <span class="badge badge-inline badge-danger">Addon</span>
                                    @endif
                                </a>
                            </li>
                        @endif
                        @can('view_all_subscribers')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('subscribers.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Subscribers') }}</span>
                                </a>
                            </li>
                        @endcan
                        @if (get_setting('coupon_system') == 1 && auth()->user()->can('view_all_coupons'))
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('user_feedback.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['user_feedback.index']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('User Feedback') }}</span>
                                </a>
                            </li>
                        @endif
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('coupon.index') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['coupon.index', 'coupon.create', 'coupon.edit']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Coupon') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('offers.index') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['offers.index', 'offers.create', 'offers.edit']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Offers') }}</span>
                            </a>
                        </li>

                        <!-- Price Alerts -->
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('admin.price-alerts.index') }}"
                                class="aiz-side-nav-link {{ areActiveRoutes(['admin.price-alerts.index', 'admin.price-alerts.create']) }}">
                                <span class="aiz-side-nav-text">{{ translate('Price Alerts') }}</span>
                            </a>
                        </li>
                        <!-- Add this inside the Marketing or Promotions section -->
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('admin.category-banners.index') }}" class="aiz-side-nav-link {{ areActiveRoutes(['admin.category-banners.index', 'admin.category-banners.create', 'admin.category-banners.edit']) }}">
                                <i class="las la-image aiz-side-nav-icon"></i>
                                <span class="aiz-side-nav-text">{{ translate('Category Banners') }}</span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endcanany

            <!-- Support -->
            @canany(['view_all_support_tickets','view_product_queries','view_all_product_conversations'])
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-link aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{translate('Support')}}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        @can('view_all_support_tickets')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('support_ticket.admin_index') }}" class="aiz-side-nav-link {{ areActiveRoutes(['support_ticket.admin_index', 'support_ticket.admin_show']) }}">
                                    <span class="aiz-side-nav-text">{{translate('Ticket')}}</span>
                                </a>
                            </li>
                        @endcan

                        @can('view_product_queries')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('product_query.index') }}" class="aiz-side-nav-link {{ areActiveRoutes(['product_query.index','product_query.show']) }}">
                                    <span class="aiz-side-nav-text">{{translate('Product Queries')}}</span>
                                </a>
                            </li>
                        @endcan

                        @can('view_all_product_conversations')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('conversations.admin_index') }}" class="aiz-side-nav-link {{ areActiveRoutes(['conversations.admin_index', 'conversations.admin_show']) }}">
                                    <span class="aiz-side-nav-text">{{translate('Product Conversations')}}</span>
                                </a>
                            </li>
                        @endcan

                        <!-- Messaging System (New) -->
                        <li class="aiz-side-nav-item">
                            <a href="#" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{translate('Messaging')}}</span>
                                <span class="aiz-side-nav-arrow"></span>
                            </a>
                            <ul class="aiz-side-nav-list level-3">
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('admin.messaging.threads.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{translate('Threads')}}</span>
                                    </a>
                                </li>
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('admin.messaging.conversations.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{translate('Conversations')}}</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            @endcanany

            <!-- Affiliate Addon -->
            @if (addon_is_activated('affiliate_system'))
                @canany(['affiliate_registration_form_config', 'affiliate_configurations', 'view_affiliate_users',
                    'view_all_referral_users', 'view_affiliate_withdraw_requests', 'view_affiliate_logs'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-link aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Affiliate System') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('affiliate_registration_form_config')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('affiliate.configs') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Affiliate Registration Form') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('affiliate_configurations')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('affiliate.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Affiliate Configurations') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_affiliate_users')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('affiliate.users') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['affiliate.users', 'affiliate_users.show_verification_request', 'affiliate_user.payment_history']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Affiliate Users') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_all_referral_users')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('refferals.users') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Referral Users') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_affiliate_withdraw_requests')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('affiliate.withdraw_requests') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Affiliate Withdraw Requests') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_affiliate_logs')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('affiliate.logs.admin') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Affiliate Logs') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
            @endif

            <!-- Offline Payment Addon-->
            @if (addon_is_activated('offline_payment'))
                @canany(['view_all_manual_payment_methods', 'view_all_offline_wallet_recharges',
                    'view_all_offline_customer_package_payments', 'view_all_offline_seller_package_payments'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-money-check-alt aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Offline Payment System') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('view_all_manual_payment_methods')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('manual_payment_methods.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['manual_payment_methods.index', 'manual_payment_methods.create', 'manual_payment_methods.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Manual Payment Methods') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_all_offline_wallet_recharges')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('offline_wallet_recharge_request.index') }}"
                                        class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Offline Wallet Recharge') }}</span>
                                    </a>
                                </li>
                            @endcan

                            @if (get_setting('classified_product') == 1 && auth()->user()->can('view_all_offline_customer_package_payments'))
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('offline_customer_package_payment_request.index') }}"
                                        class="aiz-side-nav-link">
                                        <span
                                            class="aiz-side-nav-text">{{ translate('Offline Customer Package Payments') }}</span>
                                    </a>
                                </li>
                            @endif
                            @if (addon_is_activated('seller_subscription') && auth()->user()->can('view_all_offline_seller_package_payments'))
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('offline_seller_package_payment_request.index') }}"
                                        class="aiz-side-nav-link">
                                        <span
                                            class="aiz-side-nav-text">{{ translate('Offline Seller Package Payments') }}</span>
                                        @if (env('DEMO_MODE') == 'On')
                                            <span class="badge badge-inline badge-danger">Addon</span>
                                        @endif
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </li>
                @endcanany
            @endif

            <!-- Paytm Addon -->
            @if (addon_is_activated('paytm') && auth()->user()->can('asian_payment_gateway_configuration'))
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-mobile-alt aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Asian Payment Gateway') }}</span>
                        @if (env('DEMO_MODE') == 'On')
                            <span class="badge badge-inline badge-danger">Addon</span>
                        @endif
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('paytm.index') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Set Asian PG Credentials') }}</span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif

            <!-- Club Point Addon-->
            @if (addon_is_activated('club_point'))
                @canany(['club_point_configurations', 'set_club_points', 'view_users_club_points'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="lab la-btc aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Club Point System') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('club_point_configurations')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('club_points.configs') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Club Point Configurations') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('set_club_points')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('set_product_points') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['set_product_points', 'product_club_point.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Set Product Point') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_users_club_points')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('club_points.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['club_points.index', 'club_point.details']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('User Points') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
            @endif

            <!--OTP addon -->
            @if (addon_is_activated('otp_system'))
                @canany(['otp_configurations', 'sms_templates', 'sms_providers_configurations', 'send_bulk_sms'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-phone aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('OTP System') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('otp_configurations')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('otp.configconfiguration') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('OTP Configurations') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('sms_templates')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('sms-templates.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('SMS Templates') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('sms_providers_configurations')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('otp_credentials.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Set OTP Credentials') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
            @endif

            @if (addon_is_activated('african_pg'))
                @canany(['african_pg_configuration', 'african_pg_credentials_configuration'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-phone aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('African Payment Gateway Addon') }}</span>
                            @if (env('DEMO_MODE') == 'On')
                                <span class="badge badge-inline badge-danger">Addon</span>
                            @endif
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('african_pg_configuration')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('african.configuration') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('African PG Configurations') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('african_pg_credentials_configuration')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('african_credentials.index') }}" class="aiz-side-nav-link">
                                        <span class="aiz-side-nav-text">{{ translate('Set African PG Credentials') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
            @endif

            <!-- Website Setup -->
            @canany(['header_setup', 'footer_setup', 'view_all_website_pages', 'website_appearance'])
                <li class="aiz-side-nav-item">
                    <a href="#"
                        class="aiz-side-nav-link {{ areActiveRoutes(['website.footer', 'website.header']) }}">
                        <i class="las la-desktop aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Website Setup') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('website.banners') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Banner') }}</span>
                            </a>
                        </li>
                        @can('header_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('website.header') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Header') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('footer_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('website.footer', ['lang' => App::getLocale()]) }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['website.footer']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Footer') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('view_all_website_pages')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('website.pages') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['website.pages', 'custom-pages.create', 'custom-pages.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Pages') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('website_appearance')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('website.appearance') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Appearance') }}</span>
                                </a>
                            </li>
                        @endcan
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('wizards.index') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Wizards Settings') }}</span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endcanany

            <!-- Setup & Configurations -->
            @canany(['general_settings', 'features_activation', 'language_setup', 'currency_setup', 'vat_&_tax_setup',
                'pickup_point_setup', 'smtp_settings', 'payment_methods_configurations', 'order_configuration',
                'file_system_&_cache_configuration', 'social_media_logins', 'facebook_chat', 'facebook_comment',
                'analytics_tools_configuration', 'google_recaptcha_configuration', 'google_map_setting',
                'google_firebase_setting', 'shipping_configuration', 'shipping_country_setting', 'manage_shipping_states',
                'manage_shipping_cities', 'manage_zones', 'manage_carriers'])
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-dharmachakra aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Setup & Configurations') }}</span>
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        @can('general_settings')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('general_setting.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('General Settings') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('features_activation')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('activation.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Features activation') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('language_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('languages.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['languages.index', 'languages.create', 'languages.store', 'languages.show', 'languages.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Languages') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('currency_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('currency.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Currency') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('vat_&_tax_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('tax.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['tax.index', 'tax.create', 'tax.store', 'tax.show', 'tax.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Vat & TAX') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('pickup_point_setup')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('pick_up_points.index') }}"
                                    class="aiz-side-nav-link {{ areActiveRoutes(['pick_up_points.index', 'pick_up_points.create', 'pick_up_points.edit']) }}">
                                    <span class="aiz-side-nav-text">{{ translate('Pickup point') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('smtp_settings')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('smtp_settings.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('SMTP Settings') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('payment_methods_configurations')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('payment_method.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Payment Methods') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('order_configuration')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('order_configuration.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Order Configuration') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('file_system_&_cache_configuration')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('file_system.index') }}" class="aiz-side-nav-link">
                                    <span
                                        class="aiz-side-nav-text">{{ translate('File System & Cache Configuration') }}</span>
                                </a>
                            </li>
                        @endcan
                        @can('social_media_logins')
                            <li class="aiz-side-nav-item">
                                <a href="{{ route('social_login.index') }}" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Social media Logins') }}</span>
                                </a>
                            </li>
                        @endcan
                        @canany(['facebook_chat', 'facebook_comment'])
                            <li class="aiz-side-nav-item">
                                <a href="javascript:void(0);" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Facebook') }}</span>
                                    <span class="aiz-side-nav-arrow"></span>
                                </a>
                                <ul class="aiz-side-nav-list level-3">
                                    @can('facebook_chat')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('facebook_chat.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Facebook Chat') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('facebook_comment')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('facebook-comment') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Facebook Comment') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                </ul>
                            </li>
                        @endcanany
                        @canany(['analytics_tools_configuration', 'google_recaptcha_configuration', 'google_map_setting',
                            'google_firebase_setting'])
                            <li class="aiz-side-nav-item">
                                <a href="javascript:void(0);" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Google') }}</span>
                                    <span class="aiz-side-nav-arrow"></span>
                                </a>
                                <ul class="aiz-side-nav-list level-3">
                                    @can('analytics_tools_configuration')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('google_analytics.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Analytics Tools') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('google_recaptcha_configuration')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('google_recaptcha.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Google reCAPTCHA') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('google_map_setting')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('google-map.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Google Map') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('google_firebase_setting')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('google-firebase.index') }}" class="aiz-side-nav-link">
                                                <span class="aiz-side-nav-text">{{ translate('Google Firebase') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                </ul>
                            </li>
                        @endcanany
                        @canany(['shipping_configuration', 'shipping_country_setting', 'manage_shipping_states',
                            'manage_shipping_cities', 'manage_zones', 'manage_carriers'])
                            <li class="aiz-side-nav-item">
                                <a href="javascript:void(0);" class="aiz-side-nav-link">
                                    <span class="aiz-side-nav-text">{{ translate('Shipping') }}</span>
                                    <span class="aiz-side-nav-arrow"></span>
                                </a>
                                <ul class="aiz-side-nav-list level-3">
                                    @can('shipping_configuration')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('shipping_configuration.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['shipping_configuration.index', 'shipping_configuration.edit', 'shipping_configuration.update']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping Configuration') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('shipping_country_setting')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('countries.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['countries.index', 'countries.edit', 'countries.update']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping Countries') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('manage_shipping_states')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('states.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['states.index', 'states.edit', 'states.update']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping States') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('manage_shipping_cities')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('cities.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['cities.index', 'cities.edit', 'cities.update']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping Cities') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('manage_zones')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('zones.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['zones.index', 'zones.create', 'zones.edit']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping Zones') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                    @can('manage_carriers')
                                        <li class="aiz-side-nav-item">
                                            <a href="{{ route('carriers.index') }}"
                                                class="aiz-side-nav-link {{ areActiveRoutes(['carriers.index', 'carriers.create', 'carriers.edit']) }}">
                                                <span class="aiz-side-nav-text">{{ translate('Shipping Carrier') }}</span>
                                            </a>
                                        </li>
                                    @endcan
                                </ul>
                            </li>
                            @endif
                        </ul>
                    </li>
                @endcanany

                <!-- Staffs -->
                @canany(['view_all_staffs', 'view_staff_roles'])
                    <li class="aiz-side-nav-item">
                        <a href="#" class="aiz-side-nav-link">
                            <i class="las la-user-tie aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Staffs') }}</span>
                            <span class="aiz-side-nav-arrow"></span>
                        </a>
                        <ul class="aiz-side-nav-list level-2">
                            @can('view_all_staffs')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('staffs.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['staffs.index', 'staffs.create', 'staffs.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('All staffs') }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('view_staff_roles')
                                <li class="aiz-side-nav-item">
                                    <a href="{{ route('roles.index') }}"
                                        class="aiz-side-nav-link {{ areActiveRoutes(['roles.index', 'roles.create', 'roles.edit']) }}">
                                        <span class="aiz-side-nav-text">{{ translate('Staff permissions') }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany

                {{-- System Update & Server Status --}}
                <!--@canany(['system_update', 'server_status'])-->
                    <!--    <li class="aiz-side-nav-item">-->
                    <!--        <a href="#" class="aiz-side-nav-link">-->
                    <!--            <i class="las la-user-tie aiz-side-nav-icon"></i>-->
                    <!--            <span class="aiz-side-nav-text">{{ translate('System') }}</span>-->
                    <!--            <span class="aiz-side-nav-arrow"></span>-->
                    <!--        </a>-->
                    <!--        <ul class="aiz-side-nav-list level-2">-->
                    <!--            @can('system_update')-->
                        <!--                <li class="aiz-side-nav-item">-->
                        <!--                    <a href="{{ route('system_update') }}" class="aiz-side-nav-link">-->
                        <!--                        <span class="aiz-side-nav-text">{{ translate('Update') }}</span>-->
                        <!--                    </a>-->
                        <!--                </li>-->
                    <!--            @endcan-->
                    <!--            @can('server_status')-->
                        <!--            <li class="aiz-side-nav-item">-->
                        <!--                <a href="{{ route('system_server') }}" class="aiz-side-nav-link">-->
                        <!--                    <span class="aiz-side-nav-text">{{ translate('Server status') }}</span>-->
                        <!--                </a>-->
                        <!--            </li>-->
                    <!--            @endcan-->
                    <!--        </ul>-->
                    <!--    </li>-->
                <!--@endcanany-->

                <!-- Addon Manager -->
                @can('manage_addons')
                    <li class="aiz-side-nav-item">
                        <a href="{{ route('addons.index') }}"
                            class="aiz-side-nav-link {{ areActiveRoutes(['addons.index', 'addons.create']) }}">
                            <i class="las la-wrench aiz-side-nav-icon"></i>
                            <span class="aiz-side-nav-text">{{ translate('Addon Manager') }}</span>
                        </a>
                    </li>
                @endcan

                <!-- Notification Management -->
                <li class="aiz-side-nav-item">
                    <a href="#" class="aiz-side-nav-link">
                        <i class="las la-bell aiz-side-nav-icon"></i>
                        <span class="aiz-side-nav-text">{{ translate('Notifications') }}</span>
                        @if(auth()->user()->unreadNotifications()->count() > 0)
                            <span class="badge badge-inline badge-primary ml-2">{{ auth()->user()->unreadNotifications()->count() }}</span>
                        @endif
                        <span class="aiz-side-nav-arrow"></span>
                    </a>
                    <ul class="aiz-side-nav-list level-2">
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('admin.notifications.dashboard') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Dashboard') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('admin.notifications.manage-notifications') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Manage Notifications') }}</span>
                            </a>
                        </li>
                        <li class="aiz-side-nav-item">
                            <a href="{{ route('admin.notifications.settings') }}" class="aiz-side-nav-link">
                                <span class="aiz-side-nav-text">{{ translate('Settings') }}</span>
                            </a>
                        </li>
                    </ul>
                </li>

                </ul><!-- .aiz-side-nav -->
            </div><!-- .aiz-side-nav-wrap -->
        </div><!-- .aiz-sidebar -->
        <div class="aiz-sidebar-overlay"></div>
    </div><!-- .aiz-sidebar -->

    <!-- Messaging -->
    @canany(['view_all_messages'])
    <li class="aiz-side-nav-item">
       <a href="javascript:void(0)" class="aiz-side-nav-link" id="adminMessagingTrigger">
           <i class="las la-comment-dots aiz-side-nav-icon"></i>
           <span class="aiz-side-nav-text">{{translate('Messaging')}}</span>
           <span class="badge badge-inline badge-danger messaging-badge d-none">0</span>
       </a>
    </li>
    @endcanany

    <script>
    // Use a safer approach to ensure jQuery is available
    function initializeMessaging() {
        // Add event listener to the messaging link in sidebar
        const messagingTrigger = document.getElementById('adminMessagingTrigger');
        if (messagingTrigger) {
            messagingTrigger.addEventListener('click', function(e) {
                e.preventDefault();

                // Toggle messaging panel
                const messagingPanel = document.getElementById('adminMessagingPanel');
                const messagingButton = document.getElementById('adminMessagingButton');

                if (messagingPanel) {
                    if (messagingPanel.style.display === 'flex') {
                        messagingPanel.style.display = 'none';
                    } else {
                        messagingPanel.style.display = 'flex';

                        // Trigger load conversations
                        if (typeof loadConversations === 'function') {
                            loadConversations();
                        }
                    }
                } else {
                    // Trigger the floating button if panel not directly accessible
                    if (messagingButton) {
                        messagingButton.click();
                    }
                }
            });
        }

        // Update messaging badge with unread count
        function updateMessagingBadge() {
            fetch('/buzfi-n-main/buzfi-backend-new/api/v3/messaging/unread-counts')
                .then(response => response.json())
                .then(data => {
                    const unreadCount = data.data?.total || 0;

                    // Use native JavaScript instead of jQuery for better compatibility
                    const badges = document.querySelectorAll('.messaging-badge');

                    badges.forEach(badge => {
                        if (unreadCount > 0) {
                            badge.textContent = unreadCount;
                            badge.classList.remove('d-none');
                        } else {
                            badge.classList.add('d-none');
                        }
                    });
                })
                .catch(error => console.error('Error loading unread count:', error));

            // Poll for updates every 30 seconds
            setTimeout(updateMessagingBadge, 30000);
        }

        // Initialize badge
        updateMessagingBadge();
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMessaging);
    } else {
        initializeMessaging();
    }

    // Also try with jQuery if available
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            // jQuery-specific initialization can go here if needed
        });
    }
    </script>
