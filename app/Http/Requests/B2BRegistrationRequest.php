<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;

class B2BRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [];
        $rules['user_name']          = 'required|string|max:255';
        $rules['name']          = 'required|string|max:255';
        $rules['last_name']          = 'required|string|max:255';
        // $rules['country_code']          = 'required|numeric|digits_between:1,3';
        // $rules['phone']          = 'required|unique:users|numeric|digits_between:11,13';
        $rules['email']         = 'required|email|unique:users|max:255';
        $rules['password' ]     = 'required|string|min:8|confirmed|';
        //'regex:/[a-z]/',      // must contain at least one lowercase letter
        //'regex:/[A-Z]/',      // must contain at least one uppercase letter
        //'regex:/[0-9]/',      // must contain at least one digit
        return $rules;
    }

    public function messages()
    {
        return [
            'user_name.required'         => translate('User Name is required'),
            'user_name.required'         => translate('User Name is required'),
            'user_name.string'           => translate('User Name should be string type'),
            'name.max'              => translate('Max 255 characters'),
            'name.required'         => translate('First Name is required'),
            'name.string'           => translate('First Name should be string type'),
            'last_name.max'              => translate('Max 255 characters'),
            'last_name.required'         => translate('Last Name is required'),
            'last_name.string'           => translate('Last Name should be string type'),
            // 'phone.required'        => translate('Phone number is required'),
            // 'phone.digits_between'           => translate('Phone number must be between 11 to 13 digit'),
            // 'phone.unique'          => translate('The provided Phone Number is already registered, Use another one!'),
            // 'phone.numeric'             => translate('Phone Number need to be numeric'),
            'email.required'        => translate('Email is required'),
            'email.email'           => translate('Please type a valid email'),
            'email.unique'          => translate('The provided Email is already registered, Use another one! <a href="https://test.buzfi.com/users/login">Login Now</a>'),
            'email.max'             => translate('Max 255 characters'),
            'password.required'     => translate('Password is required'),
            'password.string'       => translate('Password should be string type'),
            'password.min'          => translate('Min 6 characters'),
            'password.confirmed'    => translate('Confirm password do not matched'),

        ];
    }

    public function failedValidation(Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(response()->json([
                'message' => $validator->errors()->all(),
                'result' => false
            ], 422));
        } else {
            throw (new ValidationException($validator))
                    ->errorBag($this->errorBag)
                    ->redirectTo($this->getRedirectUrl());
        }
    }
}
