<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEarlyAccessFieldsToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'is_early_access')) {
                $table->boolean('is_early_access')->default(0)->after('is_premium');
            }
            
            if (!Schema::hasColumn('products', 'early_access_expiry')) {
                $table->timestamp('early_access_expiry')->nullable()->after('is_early_access');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'is_early_access')) {
                $table->dropColumn('is_early_access');
            }
            
            if (Schema::hasColumn('products', 'early_access_expiry')) {
                $table->dropColumn('early_access_expiry');
            }
        });
    }
}
