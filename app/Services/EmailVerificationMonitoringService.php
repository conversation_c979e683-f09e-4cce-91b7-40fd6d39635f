<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmailVerificationMonitoringService
{
    /**
     * Log email verification event with comprehensive details
     */
    public function logEmailVerificationEvent(string $event, array $data = [])
    {
        $logData = [
            'event' => $event,
            'timestamp' => Carbon::now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'data' => $data
        ];

        // Log to dedicated email verification channel
        Log::channel('email_logs')->info("Email Verification Event: {$event}", $logData);

        // Also log to main application log for critical events
        if (in_array($event, ['registration_completed', 'security_violation', 'system_error'])) {
            Log::info("Critical Email Verification Event: {$event}", $logData);
        }

        // Store metrics for monitoring dashboard
        $this->updateMetrics($event, $data);
    }

    /**
     * Log authentication event with security context
     */
    public function logAuthenticationEvent(string $event, array $data = [])
    {
        $logData = [
            'event' => $event,
            'timestamp' => Carbon::now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'security_context' => [
                'is_suspicious' => $this->detectSuspiciousActivity($data),
                'rate_limit_status' => $this->getRateLimitStatus($data['email'] ?? null),
                'geo_location' => $this->getGeoLocation(request()->ip()),
            ],
            'data' => $data
        ];

        // Log to dedicated auth channel
        Log::channel('api_auth')->info("Authentication Event: {$event}", $logData);

        // Store security metrics
        $this->updateSecurityMetrics($event, $logData);
    }

    /**
     * Log registration event with user type and success metrics
     */
    public function logRegistrationEvent(string $event, array $data = [])
    {
        $logData = [
            'event' => $event,
            'timestamp' => Carbon::now()->toISOString(),
            'user_type' => $data['user_type'] ?? 'unknown',
            'registration_flow' => $data['flow'] ?? 'standard',
            'auto_login' => $data['auto_login'] ?? false,
            'cart_merge' => $data['cart_merge'] ?? false,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $data
        ];

        // Log to registration channel
        Log::channel('api_registration')->info("Registration Event: {$event}", $logData);

        // Update registration metrics
        $this->updateRegistrationMetrics($event, $logData);
    }

    /**
     * Log performance metrics for email verification operations
     */
    public function logPerformanceMetrics(string $operation, float $duration, array $context = [])
    {
        $logData = [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'timestamp' => Carbon::now()->toISOString(),
            'context' => $context,
            'performance_flags' => [
                'slow_query' => $duration > 1.0, // Flag queries over 1 second
                'cache_hit' => $context['cache_hit'] ?? false,
                'database_queries' => $context['query_count'] ?? 0,
            ]
        ];

        // Log performance data
        Log::channel('api_auth')->debug("Performance Metric: {$operation}", $logData);

        // Store performance metrics for monitoring
        $this->updatePerformanceMetrics($operation, $duration, $context);
    }

    /**
     * Log security violation with detailed context
     */
    public function logSecurityViolation(string $violation, array $context = [])
    {
        $logData = [
            'violation' => $violation,
            'severity' => $context['severity'] ?? 'medium',
            'timestamp' => Carbon::now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'email_hash' => isset($context['email']) ? hash('sha256', $context['email']) : null,
            'context' => $context,
            'security_flags' => [
                'automated_request' => $this->detectAutomatedRequest(),
                'suspicious_ip' => $this->isSuspiciousIP(request()->ip()),
                'rate_limited' => $context['rate_limited'] ?? false,
            ]
        ];

        // Log security violation
        Log::channel('api_auth')->warning("Security Violation: {$violation}", $logData);

        // Also log to main log for high severity violations
        if (in_array($context['severity'] ?? 'medium', ['high', 'critical'])) {
            Log::warning("Critical Security Violation: {$violation}", $logData);
        }

        // Update security metrics
        $this->updateSecurityViolationMetrics($violation, $logData);
    }

    /**
     * Get comprehensive system health metrics
     */
    public function getSystemHealthMetrics(): array
    {
        $cacheKey = 'email_verification_health_metrics';
        
        return Cache::remember($cacheKey, 300, function () { // Cache for 5 minutes
            return [
                'email_verification' => [
                    'success_rate_24h' => $this->getSuccessRate('email_verification', 24),
                    'average_response_time' => $this->getAverageResponseTime('email_verification'),
                    'total_verifications_today' => $this->getTotalCount('email_verification', 1),
                    'failed_attempts_24h' => $this->getFailedAttempts(24),
                ],
                'registration' => [
                    'success_rate_24h' => $this->getSuccessRate('registration', 24),
                    'total_registrations_today' => $this->getTotalCount('registration', 1),
                    'auto_login_success_rate' => $this->getAutoLoginSuccessRate(),
                    'user_type_distribution' => $this->getUserTypeDistribution(),
                ],
                'security' => [
                    'blocked_ips_24h' => $this->getBlockedIPsCount(24),
                    'security_violations_24h' => $this->getSecurityViolationsCount(24),
                    'suspicious_activity_rate' => $this->getSuspiciousActivityRate(),
                ],
                'performance' => [
                    'average_email_send_time' => $this->getAverageEmailSendTime(),
                    'database_query_performance' => $this->getDatabaseQueryPerformance(),
                    'cache_hit_rate' => $this->getCacheHitRate(),
                ],
                'system' => [
                    'uptime' => $this->getSystemUptime(),
                    'error_rate_24h' => $this->getErrorRate(24),
                    'last_updated' => Carbon::now()->toISOString(),
                ]
            ];
        });
    }

    /**
     * Generate daily monitoring report
     */
    public function generateDailyReport(): array
    {
        $date = Carbon::today();
        
        return [
            'date' => $date->toDateString(),
            'summary' => [
                'total_email_verifications' => $this->getTotalCount('email_verification', 1),
                'total_registrations' => $this->getTotalCount('registration', 1),
                'success_rate' => $this->getSuccessRate('overall', 24),
                'security_incidents' => $this->getSecurityViolationsCount(24),
            ],
            'user_types' => $this->getUserTypeDistribution(),
            'performance' => [
                'average_response_time' => $this->getAverageResponseTime('overall'),
                'slowest_operations' => $this->getSlowestOperations(),
                'cache_performance' => $this->getCachePerformanceReport(),
            ],
            'security' => [
                'blocked_attempts' => $this->getBlockedAttemptsReport(),
                'suspicious_activities' => $this->getSuspiciousActivitiesReport(),
                'rate_limit_hits' => $this->getRateLimitHitsReport(),
            ],
            'errors' => [
                'total_errors' => $this->getErrorCount(24),
                'error_breakdown' => $this->getErrorBreakdown(24),
                'critical_errors' => $this->getCriticalErrors(24),
            ]
        ];
    }

    /**
     * Update metrics in cache/database
     */
    private function updateMetrics(string $event, array $data)
    {
        $key = "email_verification_metrics:{$event}:" . Carbon::now()->format('Y-m-d-H');
        $currentValue = Cache::get($key, 0);
        Cache::put($key, $currentValue + 1, 86400 * 7); // Keep for 7 days
    }

    /**
     * Update security metrics
     */
    private function updateSecurityMetrics(string $event, array $data)
    {
        $key = "security_metrics:{$event}:" . Carbon::now()->format('Y-m-d-H');
        $currentValue = Cache::get($key, 0);
        Cache::put($key, $currentValue + 1, 86400 * 30); // Keep for 30 days
    }

    /**
     * Update registration metrics
     */
    private function updateRegistrationMetrics(string $event, array $data)
    {
        $key = "registration_metrics:{$event}:" . Carbon::now()->format('Y-m-d-H');
        Cache::increment($key, 1);
        
        // Track user type distribution
        if (isset($data['user_type'])) {
            $userTypeKey = "registration_user_type:{$data['user_type']}:" . Carbon::now()->format('Y-m-d');
            $currentValue = Cache::get($userTypeKey, 0);
            Cache::put($userTypeKey, $currentValue + 1, 86400 * 30);
        }
    }

    /**
     * Update performance metrics
     */
    private function updatePerformanceMetrics(string $operation, float $duration, array $context)
    {
        $key = "performance_metrics:{$operation}:" . Carbon::now()->format('Y-m-d-H');
        
        // Store duration data for averaging
        $durations = Cache::get($key . ':durations', []);
        $durations[] = $duration;
        
        // Keep only last 100 measurements per hour
        if (count($durations) > 100) {
            $durations = array_slice($durations, -100);
        }
        
        Cache::put($key . ':durations', $durations, 86400 * 7);
    }

    /**
     * Update security violation metrics
     */
    private function updateSecurityViolationMetrics(string $violation, array $data)
    {
        $key = "security_violations:{$violation}:" . Carbon::now()->format('Y-m-d-H');
        $currentValue = Cache::get($key, 0);
        Cache::put($key, $currentValue + 1, 86400 * 30);
    }

    /**
     * Detect suspicious activity patterns
     */
    private function detectSuspiciousActivity(array $data): bool
    {
        $ip = request()->ip();
        $email = $data['email'] ?? null;
        
        // Check for rapid requests from same IP
        $ipRequestCount = Cache::get("ip_requests:{$ip}:" . Carbon::now()->format('Y-m-d-H'), 0);
        if ($ipRequestCount > 50) { // More than 50 requests per hour
            return true;
        }
        
        // Check for multiple email attempts from same IP
        if ($email) {
            $emailAttempts = Cache::get("email_attempts:{$ip}:" . Carbon::now()->format('Y-m-d'), 0);
            if ($emailAttempts > 10) { // More than 10 different emails per day
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get rate limit status for email/IP
     */
    private function getRateLimitStatus(?string $email): array
    {
        if (!$email) {
            return ['status' => 'unknown'];
        }
        
        $ip = request()->ip();
        
        return [
            'email_attempts' => Cache::get("rate_limit:email:{$email}", 0),
            'ip_attempts' => Cache::get("rate_limit:ip:{$ip}", 0),
            'is_rate_limited' => Cache::has("rate_limited:email:{$email}") || Cache::has("rate_limited:ip:{$ip}"),
        ];
    }

    /**
     * Get geo location for IP (mock implementation)
     */
    private function getGeoLocation(string $ip): array
    {
        // In production, this would use a real geo-location service
        return [
            'country' => 'Unknown',
            'city' => 'Unknown',
            'is_vpn' => false,
            'is_tor' => false,
        ];
    }

    /**
     * Detect automated requests
     */
    private function detectAutomatedRequest(): bool
    {
        $userAgent = request()->userAgent();
        
        // Simple bot detection patterns
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python', 'java'
        ];
        
        foreach ($botPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if IP is suspicious
     */
    private function isSuspiciousIP(string $ip): bool
    {
        // Check against known suspicious IP cache
        return Cache::has("suspicious_ip:{$ip}");
    }

    // Placeholder methods for metrics calculation
    // These would be implemented with actual database queries or cache operations
    
    private function getSuccessRate(string $operation, int $hours): float
    {
        // Implementation would calculate actual success rate
        return 95.5; // Placeholder
    }
    
    private function getAverageResponseTime(string $operation): float
    {
        // Implementation would calculate actual average response time
        return 0.25; // Placeholder (250ms)
    }
    
    private function getTotalCount(string $operation, int $days): int
    {
        // Implementation would count actual operations
        return 1250; // Placeholder
    }
    
    private function getFailedAttempts(int $hours): int
    {
        // Implementation would count failed attempts
        return 45; // Placeholder
    }
    
    private function getAutoLoginSuccessRate(): float
    {
        // Implementation would calculate auto-login success rate
        return 98.2; // Placeholder
    }
    
    private function getUserTypeDistribution(): array
    {
        // Implementation would get actual user type distribution
        return [
            'customer' => 75,
            'seller' => 20,
            'dropshipper' => 5
        ]; // Placeholder
    }
    
    private function getBlockedIPsCount(int $hours): int
    {
        return 12; // Placeholder
    }
    
    private function getSecurityViolationsCount(int $hours): int
    {
        return 8; // Placeholder
    }
    
    private function getSuspiciousActivityRate(): float
    {
        return 2.1; // Placeholder
    }
    
    private function getAverageEmailSendTime(): float
    {
        return 0.15; // Placeholder
    }
    
    private function getDatabaseQueryPerformance(): array
    {
        return [
            'average_time' => 0.05,
            'slow_queries' => 3,
            'total_queries' => 1500
        ]; // Placeholder
    }
    
    private function getCacheHitRate(): float
    {
        return 85.5; // Placeholder
    }
    
    private function getSystemUptime(): string
    {
        return '99.9%'; // Placeholder
    }
    
    private function getErrorRate(int $hours): float
    {
        return 0.5; // Placeholder
    }
    
    private function getSlowestOperations(): array
    {
        return [
            'email_send' => 0.45,
            'database_query' => 0.12,
            'otp_generation' => 0.08
        ]; // Placeholder
    }
    
    private function getCachePerformanceReport(): array
    {
        return [
            'hit_rate' => 85.5,
            'miss_rate' => 14.5,
            'evictions' => 25
        ]; // Placeholder
    }
    
    private function getBlockedAttemptsReport(): array
    {
        return [
            'total' => 45,
            'by_ip' => 30,
            'by_email' => 15
        ]; // Placeholder
    }
    
    private function getSuspiciousActivitiesReport(): array
    {
        return [
            'bot_attempts' => 20,
            'rapid_requests' => 15,
            'multiple_emails' => 10
        ]; // Placeholder
    }
    
    private function getRateLimitHitsReport(): array
    {
        return [
            'email_rate_limits' => 25,
            'ip_rate_limits' => 18,
            'global_rate_limits' => 5
        ]; // Placeholder
    }
    
    private function getErrorCount(int $hours): int
    {
        return 12; // Placeholder
    }
    
    private function getErrorBreakdown(int $hours): array
    {
        return [
            'validation_errors' => 8,
            'database_errors' => 2,
            'email_send_errors' => 2
        ]; // Placeholder
    }
    
    private function getCriticalErrors(int $hours): array
    {
        return [
            'database_connection_failed' => 1,
            'email_service_down' => 0,
            'security_breach_attempt' => 1
        ]; // Placeholder
    }
}