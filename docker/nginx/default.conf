server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html index.htm;

    # <PERSON><PERSON> requests to /buzfi-new-backend
    location /buzfi-new-backend {
        alias /var/www/html/public;
        try_files $uri $uri/ @buzfi;
        
        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass php:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME /var/www/html/public$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }

    # Handle root requests and redirect to buzfi-new-backend
    location = / {
        return 301 /buzfi-new-backend/;
    }

    # Handle direct access to the application
    location / {
        try_files $uri $uri/ @buzfi;
    }

    # Laravel URL rewriting
    location @buzfi {
        rewrite ^/buzfi-new-backend/(.*)$ /index.php?/$1 last;
        rewrite ^(.+)$ /index.php?/$1 last;
    }

    # Handle PHP files
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_connect_timeout 300;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;

    # Deny access to hidden files
    location ~ /\. {
        deny all;
    }

    # Handle static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Increase client max body size for file uploads
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # Error and access logs
    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
}
