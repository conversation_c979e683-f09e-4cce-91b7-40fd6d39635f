<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('wallet_exchange_rates', function (Blueprint $table) {
            if (!Schema::hasColumn('wallet_exchange_rates', 'points')) {
                $table->integer('points')->after('id')->nullable();
            }
            if (!Schema::hasColumn('wallet_exchange_rates', 'currency_amount')) {
                $table->decimal('currency_amount', 10, 2)->after('points')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('wallet_exchange_rates', function (Blueprint $table) {
            if (Schema::hasColumn('wallet_exchange_rates', 'points')) {
                $table->dropColumn('points');
            }
            if (Schema::hasColumn('wallet_exchange_rates', 'currency_amount')) {
                $table->dropColumn('currency_amount');
            }
        });
    }
};
