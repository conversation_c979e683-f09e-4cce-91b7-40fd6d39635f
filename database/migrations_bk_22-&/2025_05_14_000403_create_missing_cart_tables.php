<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the cart_items table if it doesn't exist
        if (!Schema::hasTable('cart_items')) {
            Schema::create('cart_items', function (Blueprint $table) {
                $table->id();
                $table->uuid('cart_info_id');
                $table->foreignId('user_id')->nullable()->constrained();
                $table->string('temp_user_id')->nullable();
                $table->foreignId('product_id')->constrained();
                $table->foreignId('owner_id')->nullable();
                $table->foreignId('address_id')->nullable();
                $table->text('variation')->nullable();
                $table->decimal('price', 10, 2)->default(0);
                $table->decimal('tax', 10, 2)->default(0);
                $table->decimal('shipping_cost', 10, 2)->default(0);
                $table->string('shipping_type')->nullable();
                $table->integer('pickup_point')->nullable();
                $table->integer('carrier_id')->nullable();
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('product_referral_code')->nullable();
                $table->string('coupon_code')->nullable();
                $table->boolean('coupon_applied')->default(false);
                $table->integer('quantity')->default(1);
                $table->timestamps();
            });
        }
        
        // Check if cart_info table has correct structure
        if (Schema::hasTable('cart_info')) {
            Schema::table('cart_info', function (Blueprint $table) {
                if (!Schema::hasColumn('cart_info', 'address_id')) {
                    $table->foreignId('address_id')->nullable();
                }
                
                if (!Schema::hasColumn('cart_info', 'wallet_amount_used')) {
                    $table->decimal('wallet_amount_used', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'reward_points_used')) {
                    $table->integer('reward_points_used')->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'reward_points_discount')) {
                    $table->decimal('reward_points_discount', 10, 2)->default(0);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop the tables on rollback to prevent data loss
    }
};
