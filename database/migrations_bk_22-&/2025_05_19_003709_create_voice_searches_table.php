<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('voice_searches')) {
            Schema::create('voice_searches', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->string('query', 255);
                $table->integer('result_count')->default(0);
                $table->timestamps();
            
            // Add indexes without foreign key constraints
            $table->index(['user_id', 'created_at']);
            $table->index('query');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('voice_searches');
    }
};
