<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateVideosTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check and update video_categories table
        if (Schema::hasTable('video_categories')) {
            // Add any missing columns to video_categories
            Schema::table('video_categories', function (Blueprint $table) {
                if (!Schema::hasColumn('video_categories', 'deleted_at')) {
                    $table->softDeletes();
                }
                
                if (!Schema::hasColumn('video_categories', 'slug')) {
                    $table->string('slug')->after('name');
                }
                
                // Add any other missing columns you need
            });
        } else {
            // Create the table if it doesn't exist (unlikely based on the error)
            Schema::create('video_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug');
                $table->text('description')->nullable();
                $table->string('icon')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->integer('sequence')->default(0);
                $table->unsignedInteger('created_by')->nullable();
                $table->unsignedInteger('updated_by')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Check and update videos table
        if (Schema::hasTable('videos')) {
            // Add any missing columns to videos
            Schema::table('videos', function (Blueprint $table) {
                if (!Schema::hasColumn('videos', 'deleted_at')) {
                    $table->softDeletes();
                }
                
                // Add category column - check first if video column exists
                if (Schema::hasColumn('videos', 'video')) {
                    if (!Schema::hasColumn('videos', 'category')) {
                        $table->string('category')->nullable()->after('video');
                    }
                } else {
                    // If video column doesn't exist, just add category without after()
                    if (!Schema::hasColumn('videos', 'category')) {
                        $table->string('category')->nullable();
                    }
                }
                
                // Add is_featured column - check first if status column exists
                if (Schema::hasColumn('videos', 'status')) {
                    if (!Schema::hasColumn('videos', 'is_featured')) {
                        $table->boolean('is_featured')->default(false)->after('status');
                    }
                } else {
                    // If status column doesn't exist, just add is_featured without after()
                    if (!Schema::hasColumn('videos', 'is_featured')) {
                        $table->boolean('is_featured')->default(false);
                    }
                }
            });
        } else {
            // Create videos table if it doesn't exist
            Schema::create('videos', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description')->nullable();
                $table->string('thumbnail')->nullable();
                $table->string('video');
                $table->string('category')->nullable();
                $table->integer('duration')->nullable();
                $table->integer('views')->default(0);
                $table->enum('status', ['pending', 'active', 'rejected'])->default('pending');
                $table->boolean('is_featured')->default(false);
                $table->integer('sequence')->default(0);
                $table->unsignedInteger('created_by')->nullable();
                $table->unsignedInteger('updated_by')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Check and update video_views table
        if (!Schema::hasTable('video_views')) {
            Schema::create('video_views', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('video_id');
                $table->unsignedBigInteger('user_id')->nullable();
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->timestamps();
                
                $table->foreign('video_id')->references('id')->on('videos')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // We don't want to drop any tables when rolling back
        // Just remove columns that were added
        Schema::table('videos', function (Blueprint $table) {
            // Only if we added these columns in this migration
            // $table->dropSoftDeletes();
            // $table->dropColumn('other_columns_added');
        });
    }
}