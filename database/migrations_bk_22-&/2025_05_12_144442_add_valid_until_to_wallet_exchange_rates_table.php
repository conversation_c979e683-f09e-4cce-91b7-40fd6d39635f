<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('wallet_exchange_rates', function (Blueprint $table) {
            if (!Schema::hasColumn('wallet_exchange_rates', 'valid_from')) {
                $table->timestamp('valid_from')->nullable()->after('status');
            }
            if (!Schema::hasColumn('wallet_exchange_rates', 'valid_until')) {
                $table->timestamp('valid_until')->nullable()->after('valid_from');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('wallet_exchange_rates', function (Blueprint $table) {
            if (Schema::hasColumn('wallet_exchange_rates', 'valid_from')) {
                $table->dropColumn('valid_from');
            }
            if (Schema::hasColumn('wallet_exchange_rates', 'valid_until')) {
                $table->dropColumn('valid_until');
            }
        });
    }
};
