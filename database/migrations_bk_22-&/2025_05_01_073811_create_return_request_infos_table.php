<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('return_request_infos')) {
        Schema::create('return_request_infos', function (Blueprint $table) {
            $table->id();
            $table->string('return_code')->unique();
            $table->foreignId('user_id')->nullable();
            //$table->foreignId('seller_id')->nullable();
            $table->foreignId('order_id');
            $table->integer('date');
            $table->decimal('amount', 20, 2);
            //$table->boolean('seller_approval')->default(0);
            $table->boolean('admin_seen')->default(0);
            $table->text('admin_note')->nullable();
            $table->string('reason_for_return')->comment('See return_request_configuration in business_settings table type column ->reason_for_return');
            $table->text('user_note')->nullable();
            $table->string('preferred_resolution')->comment('See return_request_configuration in business_settings table type column ->preferred_resolution');
            $table->enum('return_status', ['pending', 'approved', 'processing','completed', 'rejected', 'cancelled', 'cancelled_by_user'])->default('pending')->comment('only for admin');
            $table->enum('user_return_status', ['pending', 'approved', 'processing','completed', 'rejected', 'cancelled'])->default('pending')->comment('only for user');
            $table->string('attachments')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('return_request_infos');
    }
};
