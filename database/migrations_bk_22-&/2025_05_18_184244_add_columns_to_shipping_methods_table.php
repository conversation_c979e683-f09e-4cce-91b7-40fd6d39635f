<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shipping_methods', function (Blueprint $table) {
            // Check if the columns don't exist before adding them
            if (!Schema::hasColumn('shipping_methods', 'carrier_code')) {
                $table->string('carrier_code')->nullable()->comment('Code for the shipping carrier');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'carrier_name')) {
                $table->string('carrier_name')->nullable()->comment('Name of the shipping carrier');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'service_code')) {
                $table->string('service_code')->nullable()->comment('Code for the shipping service');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'tracking_url_template')) {
                $table->string('tracking_url_template')->nullable()->comment('URL template for tracking, use {tracking_code} placeholder');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'logo_url')) {
                $table->string('logo_url')->nullable()->comment('URL to carrier or method logo');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'is_expedited')) {
                $table->boolean('is_expedited')->default(false)->comment('Whether this is an expedited shipping option');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'restricted_countries')) {
                $table->json('restricted_countries')->nullable()->comment('List of country codes where this method is not available');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'supported_countries')) {
                $table->json('supported_countries')->nullable()->comment('List of country codes where this method is available');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'max_weight')) {
                $table->decimal('max_weight', 10, 2)->nullable()->comment('Maximum weight in kg this method supports');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'weight_surcharge_threshold')) {
                $table->decimal('weight_surcharge_threshold', 10, 2)->nullable()->comment('Weight threshold in kg where surcharges apply');
            }
            
            if (!Schema::hasColumn('shipping_methods', 'weight_surcharge_rate')) {
                $table->decimal('weight_surcharge_rate', 10, 2)->nullable()->comment('Surcharge rate per kg over threshold');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shipping_methods', function (Blueprint $table) {
            // Drop the columns in reverse order
            $table->dropColumn([
                'carrier_code',
                'carrier_name',
                'service_code',
                'tracking_url_template',
                'logo_url',
                'is_expedited',
                'restricted_countries',
                'supported_countries',
                'max_weight',
                'weight_surcharge_threshold',
                'weight_surcharge_rate',
            ]);
        });
    }
};
