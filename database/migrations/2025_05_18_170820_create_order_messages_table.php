<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
            Schema::create('order_messages', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id');//->onDelete('cascade'); // Changed to integer to match the orders table
                $table->unsignedInteger('sender_id');
                $table->string('sender_type'); // customer, seller, dropshipper, admin, system
                $table->text('content');
                $table->timestamp('read_at')->nullable();
                $table->timestamps();

                // Removed foreign key constraint since it's causing issues
                $table->index(['order_id', 'read_at']);
                $table->index(['sender_id', 'sender_type']);
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_message_attachments');
        Schema::dropIfExists('order_messages');
    }
};
