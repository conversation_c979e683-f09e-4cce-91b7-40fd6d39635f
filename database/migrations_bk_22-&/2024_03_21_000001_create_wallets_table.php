<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('wallets')) {
                Schema::create('wallets', function (Blueprint $table) {
                    $table->id();
                    $table->foreignId('user_id')->constrained()->onDelete('cascade');
                    $table->decimal('balance', 10, 2)->default(0);
                    $table->integer('reward_points')->default(0);
                    $table->decimal('promotional_credits', 10, 2)->default(0);
                    $table->string('status')->default('active');
                    $table->timestamp('last_updated_at')->nullable();
                    $table->timestamps();
                });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('wallets');
    }
}; 