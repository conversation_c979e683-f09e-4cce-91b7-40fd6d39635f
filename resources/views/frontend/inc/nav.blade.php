<!-- Customize for navbar with devkamal -->
<style>
    .bottom-menu {
        /* background:  !important; */
        display: block;
        /* height: 40px; */
        background: rgba(0, 95, 200, 1);
    }

    .show.dropdown-menu {
        margin-top: 7px !important;
        padding-top: 0;
    }

    .aiz-category-menu .sub-cat-menu {
        width: 750px;
    }




    /* .downMenu{
        left:-1rem;
    } */
</style>

<header>
    <!--    [ Strat Logo Area]-->

    <div class="row menu-logo">

    <!--changes here <div class="col-2 logo-area">
            <a href="{{ route('home') }}"><img src="{{ static_asset('frontend/assets/img/logo.png') }}" alt=""></a> -->


        <div class="col-2 logo-area d-flex flex-column justify-content-center align-items-center pr-0">
            <div class="logo-top-part">
            <a href="{{ route('home') }}" aria-label="Buzfi Logo"><img src="{{ static_asset('frontend/assets/img/Logo-2024/Buzfi-Icon.png') }}" alt=""></a>
            </div>
            <div class="logo-bottom-part">
            <a href="{{ route('home') }}" aria-label="Buzfi Logo"><img src="{{ static_asset('frontend/assets/img/Logo-2024/Buzfi-Logo-blue-in-white-1.png') }}" alt=""></a>
            </div>
        </div>

        <div class="col-10 menu-area">
            <div class="top-menu">
                <div class="d-flex justify-content-between flex-nowrap flex-row">
                   {{-- <div class="d-flex flex-column justify-content-center">
                        @if (!Auth::check())
                            <p>
                                <small>Become a</small>
                            <div class="d-flex">
                                <a href="{{ route('shops.create') }}"><i class="fa fa-hand-o-right"
                                        aria-hidden="true"></i>
                                    Seller</a>
                                <a href="{{ route('dropshipperLanding') }}"><i class="fa fa-handshake-o" aria-hidden="true"></i>
                                    {{ translate('Dropshipper') }}</a>
                            </div>
                            </p>
                        @else
                            <p>
                                <small>You are acting as a</small>
                            <div class="d-flex">
                                @if (auth()->user()->user_type == 'customer')
                                    <a href="{{ route('dashboard') }}"><i class="fa fa-hand-o-right"
                                            aria-hidden="true"></i>
                                        Customer</a>
                                @elseif(auth()->user()->user_type == 'b2b')
                                    <a href="{{ route('dropshipper.dashboard') }}"><i class="fa fa-hand-o-right"
                                            aria-hidden="true"></i>
                                        Dropshipper</a>
                                @elseif(auth()->user()->user_type == 'seller')
                                    <a href="{{ route('dashboard') }}"><i class="fa fa-hand-o-right"
                                            aria-hidden="true"></i>
                                        Seller</a>
                                @elseif(auth()->user()->user_type == 'guest')
                                    <a href="{{ route('home') }}"><i class="fa fa-hand-o-right" aria-hidden="true"></i>
                                        Guest</a>
                                @endif
                            </div>
                            </p>
                        @endif
                    </div>--}}
                    <div class="flex-grow-1 px-2">
                        <div class="main-search">
                            {{--<form action="{{ route('search') }}" method="GET" class="stop-propagation mb-0">
                                <div class="d-lg-none" data-toggle="class-toggle" data-target=".front-header-search">
                                    <button class="btn px-2" type="button"><i class="la la-2x la-long-arrow-left"></i>
                                    </button>
                                </div>
                                <div class="search-input-box d-flex justify-content-center">
                                    <input type="text"
                                        class="border border-soft-light form-control fs-14 hov-animate-outline"
                                        id="search" name="keyword"
                                        @isset($query)
                                               value="{{ $query }}"
                                           @endisset
                                        placeholder="{{ translate('Search in Buzfi ...') }}" autocomplete="off">
                                    <!-- <button><i class="fa fa-search" aria-hidden="true"></i></button> -->
                                    <!-- <button class="voice"><i class="fa fa-microphone " aria-hidden="true"></i></button> -->
                                </div>
                            </form>--}}
                            {{-- <button class="voice"><i class="fa fa-microphone " aria-hidden="true"></i></button> --}}
                            <div class="typed-search-box stop-propagation document-click-d-none d-none bg-white rounded shadow-lg position-absolute left-0 top-100 w-100"
                                style="min-height: 200px;z-index:999999">
                                <div class="search-preloader absolute-top-center">
                                    <div class="dot-loader">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                </div>
                                <div class="search-nothing d-none p-3 text-center fs-16">
                                </div>
                                <div id="search-content" class="text-left">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex mt-2">
                            {{--<div class="navbar-tool buz-bdr ">
                                <div class="navbar-ttol-icon p-0">
                                    <a href="{{ route('wishlists.index') }}"
                                        class="d-flex align-items-center text-dark" data-toggle="tooltip"
                                        data-title="{{ translate('Wishlist') }}" data-placement="top" aria-label="Wishlist">
                                        <span class="position-relative d-inline-block">

                                            <svg width="22" height="22" viewBox="0 0 22 22" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M13.5208 6.41657C12.4217 6.41657 11.5142 6.92166 10.9999 7.73108C10.4857 6.92166 9.57815 6.41657 8.47906 6.41657C6.83638 6.41657 5.49987 7.85575 5.49987 9.62493C5.49987 12.7077 10.5223 16.2671 10.7368 16.4165C10.8157 16.4725 10.9082 16.5 10.9999 16.5C11.0916 16.5 11.1842 16.4725 11.263 16.4165C11.4766 16.2671 16.5 12.7077 16.5 9.62493C16.5 7.85575 15.1634 6.41657 13.5208 6.41657ZM10.9999 15.476C9.51032 14.3733 6.41654 11.6599 6.41654 9.62493C6.41654 8.36084 7.34147 7.33325 8.47906 7.33325C9.69365 7.33325 10.5416 8.17292 10.5416 9.37559V9.62493C10.5416 9.87793 10.7469 10.0833 10.9999 10.0833C11.2529 10.0833 11.4583 9.87793 11.4583 9.62493V9.37559C11.4583 8.17292 12.3062 7.33325 13.5208 7.33325C14.6584 7.33325 15.5833 8.36084 15.5833 9.62493C15.5833 11.6599 12.4895 14.3733 10.9999 15.476ZM11.7672 0.0255362C8.5799 -0.190798 5.46412 0.975208 3.21552 3.22656C0.968753 5.47882 -0.193589 8.59734 0.0264122 11.7828C0.430665 17.6073 5.59429 22 12.0385 22H17.875C20.1492 22 22 20.1492 22 17.875V11.3254C22 5.37982 17.5055 0.416039 11.7672 0.0255362ZM21.0833 17.875C21.0833 19.6442 19.6441 21.0833 17.875 21.0833H12.0385C5.98479 21.0833 1.31801 17.1453 0.942169 11.7204C0.739584 8.79809 1.80476 5.93807 3.86453 3.87464C5.76296 1.97438 8.33606 0.916541 11.0146 0.916541C11.2437 0.916541 11.4748 0.923875 11.7048 0.939458C16.9638 1.29696 21.0824 5.85832 21.0824 11.3244V17.8741L21.0833 17.875Z"
                                                    fill="white" />
                                            </svg>
                                            @if (Auth::check() && count(Auth::user()->wishlists) > 0)
                                                <span
                                                    class="badge badge-primary badge-inline badge-pill absolute-top-right--10px">{{ count(Auth::user()->wishlists) }}</span>
                                            @endif
                                        </span>
                                    </a>
                                </div>
                                <div class="navbar-tool-text">
                                    <small>Reorder</small>
                                    <a href="{{ route('wishlists.index') }}" class="card-title loading"><span>My
                                            Items</span></a>
                                </div>
                            </div>--}}

                            {{--@auth
                            <div class="navbar-tool buz-bdr ">
                                <div class="navbar-ttol-icon p-0">
                                    <a href="#" id="notification-toggle"
                                        class="d-flex align-items-center text-dark" data-toggle="tooltip"
                                        data-title="{{ translate('Notifications') }}" data-placement="top" aria-label="Notifications">
                                        <span class="position-relative d-inline-block">
                                            <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10.9999 22C12.5499 22 13.8333 20.7167 13.8333 19.1667H8.16659C8.16659 20.7167 9.44992 22 10.9999 22ZM19.4999 15.5V9.76674C19.4999 6.22508 17.4766 3.23341 14.1458 2.38341V1.53341C14.1458 0.686742 13.4583 0.000075 12.6116 0.000075C11.7649 0.000075 11.0774 0.686742 11.0774 1.53341V2.38341C7.75825 3.23341 5.73492 6.21674 5.73492 9.76674V15.5L3.66659 17.5667V18.5833H18.3333V17.5667L16.2649 15.5H19.4999ZM16.2649 16.5H5.73492V9.76674C5.73492 6.90008 7.86659 4.58341 10.9999 4.58341C14.1333 4.58341 16.2649 6.90008 16.2649 9.76674V16.5Z" fill="white"/>
                                            </svg>
                                            <span class="notification-badge badge badge-primary badge-inline badge-pill absolute-top-right--10px" style="display: none;">0</span>
                                        </span>
                                    </a>
                                </div>
                                <div class="navbar-tool-text">
                                    <small>{{ translate('Alerts') }}</small>
                                    <a href="#" id="notification-toggle" class="card-title loading"><span>{{ translate('Notifications') }}</span></a>
                                </div>
                            </div>
                            @endauth--}}

                            <div class="navbar-tool buz-bdr px-2 px-xl-3">
                                <div class="navbar-ttol-icon">
                                    <a href="{{ route('user.login') }}" class="d-flex align-items-center text-dark"
                                        data-toggle="tooltip" data-title="{{ translate('login') }}"
                                        data-placement="top" aria-label="login">
                                        <svg width="23" height="22" viewBox="0 0 23 22" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M12.7445 0.0268973C11.173 -0.0832033 9.59622 0.145549 8.12079 0.697675C6.64537 1.2498 5.30576 2.11242 4.19262 3.22715C3.07947 4.34188 2.21877 5.68272 1.66875 7.15893C1.11873 8.63514 0.892226 10.2123 1.00457 11.7836C1.40802 17.6076 6.57207 22 13.0161 22H18.8521C19.9456 21.9985 20.9939 21.5635 21.7671 20.7903C22.5403 20.0171 22.9753 18.9688 22.9768 17.8753V11.326C22.9768 5.38086 18.4826 0.417456 12.7445 0.0268973ZM22.0599 17.8753C22.059 18.7258 21.7208 19.5412 21.1194 20.1426C20.518 20.7439 19.7026 21.0822 18.8521 21.0831H13.0161C6.96263 21.0831 2.29612 17.1457 1.9206 11.7209C1.82141 10.3384 2.00837 8.9503 2.46979 7.64332C2.93121 6.33633 3.65717 5.13853 4.60234 4.12474C5.54751 3.11095 6.69157 2.30294 7.96306 1.75119C9.23455 1.19945 10.6062 0.915812 11.9922 0.918006C12.2212 0.918006 12.4524 0.925311 12.6822 0.940778C17.9408 1.29825 22.0591 5.85949 22.0591 11.3252V17.8753H22.0599Z"
                                                fill="white" />
                                            <path
                                                d="M11.9778 11.0008C11.4792 11.0009 10.9918 10.8531 10.5772 10.5762C10.1626 10.2992 9.83941 9.90557 9.64855 9.44496C9.45769 8.98436 9.40771 8.47749 9.50493 7.98848C9.60214 7.49946 9.84219 7.05026 10.1947 6.69768C10.5472 6.34509 10.9964 6.10497 11.4854 6.00767C11.9744 5.91037 12.4813 5.96026 12.9419 6.15104C13.4026 6.34182 13.7963 6.66492 14.0733 7.07947C14.3503 7.49403 14.4981 7.98141 14.4981 8.48C14.4973 9.14824 14.2316 9.78889 13.7591 10.2614C13.2866 10.734 12.646 10.9999 11.9778 11.0008ZM11.9778 6.79961C11.6454 6.79961 11.3205 6.89816 11.0442 7.08281C10.7679 7.26745 10.5525 7.52989 10.4253 7.83694C10.2981 8.14399 10.2648 8.48186 10.3297 8.80783C10.3945 9.13379 10.5545 9.43321 10.7896 9.66821C11.0246 9.90322 11.324 10.0633 11.6499 10.1281C11.9759 10.1929 12.3138 10.1597 12.6208 10.0325C12.9279 9.90529 13.1903 9.68991 13.375 9.41357C13.5596 9.13723 13.6582 8.81235 13.6582 8.48C13.6576 8.03451 13.4804 7.60742 13.1654 7.29241C12.8503 6.9774 12.4233 6.80018 11.9778 6.79961ZM16.1785 15.6218C16.1785 13.608 14.215 11.8408 11.9778 11.8408C9.74055 11.8408 7.77658 13.608 7.77658 15.6218C7.77478 15.678 7.78431 15.7341 7.80461 15.7866C7.8249 15.8392 7.85555 15.8871 7.89473 15.9275C7.93391 15.968 7.98082 16.0002 8.03267 16.0221C8.08452 16.0441 8.14026 16.0554 8.19657 16.0554C8.25288 16.0554 8.30862 16.0441 8.36048 16.0221C8.41233 16.0002 8.45924 15.968 8.49841 15.9275C8.53759 15.8871 8.56824 15.8392 8.58854 15.7866C8.60883 15.7341 8.61836 15.678 8.61656 15.6218C8.61656 14.234 10.0533 12.6812 11.9778 12.6812C13.9022 12.6812 15.3386 14.234 15.3386 15.6218C15.3368 15.678 15.3463 15.7341 15.3666 15.7866C15.3869 15.8392 15.4175 15.8871 15.4567 15.9275C15.4959 15.968 15.5428 16.0002 15.5946 16.0221C15.6465 16.0441 15.7022 16.0554 15.7585 16.0554C15.8149 16.0554 15.8706 16.0441 15.9224 16.0221C15.9743 16.0002 16.0212 15.968 16.0604 15.9275C16.0996 15.8871 16.1302 15.8392 16.1505 15.7866C16.1708 15.7341 16.1803 15.678 16.1785 15.6218Z"
                                                fill="white" />
                                        </svg>
                                    </a>
                                </div>
                                @auth
                                    @if (Auth::user()->user_type == 'dropshipper')
                                        <div class="header-action-icon-2">
                                            <a href="{{ route('dropshipper.dashboard') }}"><span
                                                    class="lable ml-0">{{ Str::limit(Auth::user()->name, 5) }}</span></a>
                                            <div class="cart-dropdown-wrap cart-dropdown-hm2 account-dropdown">
                                                <ul>
                                                    <li>
                                                        <!-- Authentication -->
                                                        <form method="POST" action="{{ route('logout') }}">
                                                            @csrf
                                                            <a href="{{ route('logout') }}"
                                                                onclick="event.preventDefault(); this.closest('form').submit();">
                                                                <i class="fi fi-rs-sign-out mr-10"></i>Logout
                                                            </a>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    @elseif (Auth::user()->user_type == 'admin')
                                        <div class="header-action-icon-2">
                                            <a href="{{ route('admin.dashboard') }}"><span
                                                    class="lable ml-0">{{ Str::limit(Auth::user()->name, 5) }} </span></a>
                                            <div class="cart-dropdown-wrap cart-dropdown-hm2 account-dropdown">
                                                <ul>
                                                    <li>
                                                        <!-- Authentication -->
                                                        <form method="POST" action="{{ route('logout') }}">
                                                            @csrf
                                                            <a href="{{ route('logout') }}"
                                                                onclick="event.preventDefault(); this.closest('form').submit();">
                                                                <i class="fi fi-rs-sign-out mr-10"></i>Logout
                                                            </a>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    @else
                                        <div class="header-action-icon-2">
                                            <a href="{{ route('dashboard') }}"><span
                                                    class="lable ml-0">{{ Str::limit(Auth::user()->name, 5) }}</span></a>
                                            <div class="cart-dropdown-wrap cart-dropdown-hm2 account-dropdown">
                                                <ul>
                                                    <li>
                                                        <!-- Authentication -->
                                                        <form method="POST" action="{{ route('logout') }}">
                                                            @csrf
                                                            <a href="{{ route('logout') }}"
                                                                onclick="event.preventDefault(); this.closest('form').submit();">
                                                                <i class="fi fi-rs-sign-out mr-10"></i>Logout
                                                            </a>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    @endif
                                @else
                                    <div class="navbar-tool-text">
                                        <a href="{{ route('user.login') }}" class="d-flex align-items-center text-dark">
                                            <small>Sign in</small>
                                        </a>
                                        <a href="{{ route('user.registration') }}"
                                            class="card-title loading"><span>Account</span></a>
                                    </div>
                                @endauth

                            </div>


                           {{-- <div class="navbar-tool pl-2 pl-xl-3 mr-2">
                                @include('frontend.partials.cart')
                            </div>--}}


                            <!-- Cart -->
                            {{-- <div class="d-none d-xl-block align-self-stretch ml-5 mr-0 has-transition bg-black-10"
                              data-hover="dropdown">
                              <div class="nav-cart-box dropdown h-100" id="cart_items" style="width: max-content;">
                              @include('frontend.partials.cart')
                              </div>
                           </div> --}}


                        </div>
                    </div>
                </div>
            </div>

            <div class="bottom-menu d-flex justify-content-between align-items-center pt-0 pl-0">
                <div class="pl-0">
                   {{-- <nav class="navbar navbar-expand-lg bg-body-tertiary pt-0 pb-0 pl-0">
                        <div class="container-fluid pr-0 pl-0 p-l-0">
                            <div class="collapse navbar-collapse pl-0" id="navbarNavDropdown">
                                <ul class="navbar-nav pl-0">
                                    <li class="nav-item dropdown pl-0">
                                        <a class="nav-link dropdown-toggle p-l-0" href="#" role="button"
                                            data-toggle="dropdown" aria-expanded="false">

                                            <svg width="15" height="15" viewBox="0 0 23 23" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M0.000824499 5.17239C0.000824499 4.37705 0.000824499 3.57841 0.000824499 2.77978C-0.00490947 2.41433 0.0626302 2.05144 0.199451 1.71256C0.336273 1.37367 0.539595 1.06568 0.797407 0.806774C1.05522 0.547871 1.36229 0.343315 1.70045 0.205197C2.03862 0.0670801 2.40102 -0.001795 2.76625 0.00264301C4.40842 0.00264301 6.0506 0.00264301 7.69278 0.00264301C8.40725 0.00776964 9.09116 0.293387 9.5973 0.798024C10.1034 1.30266 10.3914 1.98598 10.3991 2.7009C10.3991 4.36828 10.3991 6.03238 10.3991 7.69318C10.3931 8.40818 10.1075 9.09241 9.60351 9.59923C9.09948 10.1061 8.41712 10.3952 7.70263 10.4046C6.04075 10.4046 4.37558 10.4046 2.71369 10.4046C1.9935 10.3968 1.30534 10.1055 0.798209 9.59373C0.291078 9.08195 0.00578731 8.3909 0.00410204 7.67018C-0.00246667 6.83868 0.000824499 6.00718 0.000824499 5.17239ZM1.09451 5.17239V7.56829C1.07979 7.79989 1.11427 8.03198 1.19565 8.24928C1.27704 8.46658 1.4035 8.66417 1.56671 8.82904C1.72991 8.99391 1.92616 9.12231 2.14254 9.20581C2.35892 9.2893 2.59052 9.32599 2.82208 9.31345H7.57455C7.80321 9.32558 8.03187 9.28973 8.24586 9.20819C8.45985 9.12665 8.65444 9.00124 8.81712 8.83999C8.97981 8.67874 9.10699 8.48523 9.19051 8.27189C9.27404 8.05854 9.31205 7.83008 9.30211 7.60116C9.30211 6.00389 9.30211 4.40772 9.30211 2.81265C9.31205 2.58373 9.27404 2.35526 9.19051 2.14192C9.10699 1.92857 8.97981 1.73506 8.81712 1.57382C8.65444 1.41257 8.45985 1.28715 8.24586 1.20561C8.03187 1.12408 7.80321 1.08822 7.57455 1.10035H2.82208C2.59052 1.08782 2.35892 1.1245 2.14254 1.208C1.92616 1.29149 1.72991 1.4199 1.56671 1.58477C1.4035 1.74964 1.27704 1.94722 1.19565 2.16453C1.11427 2.38183 1.07979 2.61392 1.09451 2.84551V5.17239Z"
                                                    fill="white" />
                                                <path
                                                    d="M22.9851 5.1687C22.9851 5.99034 22.9851 6.81198 22.9851 7.63033C22.9895 7.99582 22.9208 8.3585 22.7831 8.69703C22.6453 9.03556 22.4413 9.3431 22.183 9.60156C21.9248 9.86002 21.6174 10.0642 21.2791 10.202C20.9408 10.3399 20.5784 10.4086 20.2131 10.4042C18.5972 10.4042 16.9802 10.4042 15.3621 10.4042C14.9968 10.409 14.6341 10.3406 14.2956 10.2029C13.9571 10.0653 13.6496 9.86112 13.3912 9.60257C13.1328 9.34402 12.9288 9.0363 12.7912 8.69756C12.6537 8.35883 12.5853 7.99594 12.5901 7.63033C12.5901 6.01334 12.5901 4.39526 12.5901 2.77609C12.5848 2.40951 12.6532 2.0456 12.7912 1.70599C12.9292 1.36639 13.1341 1.05803 13.3936 0.799251C13.6532 0.540472 13.962 0.33656 14.3019 0.19964C14.6418 0.0627197 15.0057 -0.00441229 15.372 0.00223644H20.1573C20.5342 -0.0130255 20.9101 0.0499552 21.2615 0.187214C21.6129 0.324472 21.932 0.533035 22.1989 0.799773C22.4658 1.06651 22.6746 1.38565 22.8121 1.73709C22.9497 2.08853 23.0131 2.46467 22.9983 2.84182C22.9884 3.61745 22.9851 4.39307 22.9851 5.1687ZM17.7926 9.30976H20.1507C20.3822 9.32405 20.614 9.28911 20.8311 9.20726C21.0481 9.12541 21.2453 8.99849 21.4097 8.83486C21.5742 8.67124 21.7021 8.47461 21.7851 8.25794C21.8682 8.04126 21.9044 7.80944 21.8914 7.57774C21.8914 6.00458 21.8914 4.43032 21.8914 2.85497C21.9084 2.62026 21.8749 2.38463 21.793 2.16402C21.7112 1.94342 21.5829 1.74298 21.4169 1.57627C21.2509 1.40956 21.0511 1.28046 20.831 1.19772C20.6108 1.11497 20.3755 1.08051 20.1409 1.09666H15.4245C15.1931 1.08237 14.9612 1.11731 14.7442 1.19916C14.5272 1.28101 14.33 1.40793 14.1655 1.57155C14.0011 1.73518 13.8731 1.9318 13.7901 2.14848C13.7071 2.36516 13.6709 2.59698 13.6838 2.82867C13.6838 4.41279 13.6838 5.99801 13.6838 7.58432C13.6721 7.81315 13.7084 8.0419 13.7903 8.25587C13.8722 8.46985 13.9979 8.66432 14.1593 8.82681C14.3208 8.98929 14.5144 9.11619 14.7278 9.19936C14.9411 9.28254 15.1695 9.32014 15.3983 9.30976H17.7926Z"
                                                    fill="white" />
                                                <path
                                                    d="M10.3992 17.8091C10.3992 18.6176 10.3992 19.4294 10.3992 20.2379C10.4013 20.6005 10.3317 20.9599 10.1942 21.2954C10.0567 21.6309 9.85421 21.9358 9.59829 22.1925C9.34237 22.4492 9.03815 22.6526 8.7032 22.791C8.36826 22.9293 8.00924 22.9999 7.64688 22.9986C6.03974 22.9986 4.43369 22.9986 2.82873 22.9986C2.45575 23.0108 2.0842 22.9466 1.7369 22.81C1.3896 22.6734 1.07386 22.4671 0.809055 22.204C0.544254 21.9409 0.335978 21.6264 0.197007 21.2798C0.0580359 20.9332 -0.00869035 20.5619 0.000905947 20.1886C0.000905947 18.5804 0.000905947 16.9733 0.000905947 15.3672C-0.00258585 15.0045 0.0660595 14.6448 0.20285 14.3089C0.339641 13.973 0.541843 13.6677 0.797679 13.4108C1.05351 13.1539 1.35787 12.9505 1.69303 12.8124C2.02819 12.6743 2.38746 12.6043 2.74991 12.6065C4.39209 12.6065 6.00798 12.6065 7.64031 12.6065C8.0025 12.6039 8.36162 12.6732 8.69688 12.8104C9.03213 12.9476 9.33687 13.15 9.59344 13.4058C9.85002 13.6616 10.0533 13.9658 10.1916 14.3008C10.3299 14.6358 10.4005 14.9949 10.3992 15.3574C10.4024 16.1691 10.3992 16.9875 10.3992 17.8091ZM9.30548 17.8091C9.30548 17.0105 9.30548 16.2119 9.30548 15.4165C9.31507 15.1908 9.27804 14.9655 9.19669 14.7547C9.11534 14.5439 8.99142 14.3522 8.83268 14.1915C8.67394 14.0308 8.4838 13.9046 8.27412 13.8208C8.06444 13.7369 7.83973 13.6972 7.61404 13.7042H2.78932C2.56364 13.6968 2.33886 13.736 2.12902 13.8195C1.91918 13.9029 1.7288 14.0288 1.56975 14.1891C1.41071 14.3495 1.28642 14.541 1.20466 14.7516C1.12291 14.9622 1.08544 15.1875 1.09459 15.4132C1.09459 17.0105 1.09459 18.6078 1.09459 20.205C1.08512 20.4337 1.12349 20.6618 1.20722 20.8747C1.29095 21.0877 1.41822 21.2808 1.58085 21.4417C1.74348 21.6026 1.9379 21.7277 2.15165 21.809C2.36541 21.8904 2.59379 21.9261 2.82217 21.914C4.40522 21.914 5.98938 21.914 7.57463 21.914C7.8059 21.9265 8.0372 21.8899 8.25334 21.8066C8.46947 21.7233 8.66555 21.5952 8.8287 21.4307C8.99185 21.2662 9.11839 21.069 9.2 20.8521C9.2816 20.6352 9.31642 20.4035 9.30219 20.1722C9.30548 19.3702 9.30548 18.5848 9.30548 17.796V17.8091Z"
                                                    fill="white" />
                                                <path
                                                    d="M22.9848 17.8324C22.9848 18.631 22.9848 19.4264 22.9848 20.225C22.9901 20.5907 22.9221 20.9538 22.7846 21.2928C22.6472 21.6317 22.4432 21.9396 22.1848 22.1982C21.9263 22.4569 21.6186 22.661 21.2799 22.7985C20.9412 22.936 20.5783 23.0042 20.2128 22.9989C18.5707 22.9989 16.9547 22.9989 15.3257 22.9989C14.9662 22.9989 14.6101 22.9279 14.278 22.7901C13.9458 22.6524 13.6441 22.4504 13.39 22.1958C13.1359 21.9413 12.9345 21.6391 12.7972 21.3065C12.6599 20.974 12.5894 20.6177 12.5898 20.2579C12.5898 18.619 12.5898 16.9757 12.5898 15.328C12.5894 14.9684 12.6599 14.6122 12.7972 14.2798C12.9346 13.9475 13.1361 13.6455 13.3902 13.3912C13.6443 13.1369 13.9461 12.9353 14.2782 12.7978C14.6104 12.6604 14.9663 12.5899 15.3257 12.5903C16.9679 12.5903 18.6101 12.5903 20.2522 12.5903C20.6116 12.5899 20.9676 12.6604 21.2997 12.7978C21.6319 12.9353 21.9336 13.1369 22.1878 13.3912C22.4419 13.6455 22.6434 13.9475 22.7807 14.2798C22.9181 14.6122 22.9885 14.9684 22.9881 15.328C22.9914 16.1661 22.9848 17.0009 22.9848 17.8324ZM21.8911 17.8324C21.8911 17.0206 21.8911 16.2121 21.8911 15.4003C21.8994 15.1745 21.8611 14.9495 21.7785 14.7392C21.6959 14.5289 21.5709 14.3379 21.4113 14.1781C21.2516 14.0183 21.0607 13.8932 20.8506 13.8106C20.6404 13.728 20.4155 13.6896 20.1898 13.6979H15.3717C15.1491 13.692 14.9277 13.7315 14.7208 13.814C14.514 13.8964 14.326 14.0201 14.1684 14.1775C14.0109 14.3349 13.8869 14.5227 13.8041 14.7295C13.7213 14.9364 13.6814 15.1579 13.6868 15.3806C13.6868 16.9877 13.6868 18.5982 13.6868 20.2053C13.6796 20.4291 13.7181 20.6521 13.8001 20.8604C13.8821 21.0688 14.0058 21.2582 14.1636 21.417C14.3213 21.5758 14.5099 21.7007 14.7177 21.784C14.9254 21.8672 15.148 21.9071 15.3717 21.9011C16.9679 21.9011 18.5641 21.9011 20.1603 21.9011C20.3892 21.9137 20.6182 21.8782 20.8326 21.7969C21.047 21.7156 21.242 21.5903 21.405 21.429C21.5681 21.2677 21.6956 21.074 21.7793 20.8604C21.863 20.6468 21.9011 20.4181 21.8911 20.1888C21.8944 19.3968 21.8911 18.6113 21.8911 17.8258V17.8324Z"
                                                    fill="white" />
                                            </svg>

                                            &nbsp; Departments (See All)
                                        </a>
                                        <ul class="dropdown-menu downMenu" id="cateList">
                                            @include('frontend.partials.category_menu')
                                        </ul>
                                    </li>

                                    @foreach (get_top_3_collection() as $collection)
                                        <li class="nav-item">
                                            <a class="nav-link"
                                                href="{{ route('collections.category', ['collection_id' => $collection->id, 'category_slug' => 'all']) }}"><i
                                                    class="fa fa-shopping-basket" aria-hidden="true"></i>
                                                {{ $collection->name }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </nav>--}}
                </div>

                <div class="right-menu">
                   {{-- <nav class="navbar navbar-expand-lg bg-body-tertiary">
                        <div class="container-fluid pr-0">
                            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                                <ul class="navbar-nav">
                                    @if (get_setting('header_menu_labels') != null)
                                        @foreach (json_decode(get_setting('header_menu_labels'), true) as $key => $value)
                                            <li class="nav-item">
                                                <a class="nav-link p-2"
                                                    href="{{ json_decode(get_setting('header_menu_links'), true)[$key] }}"
                                                    @if (url()->current() == json_decode(get_setting('header_menu_links'), true)[$key]) active @endif>
                                                    @if (get_setting('header_menu_icons') && json_decode(get_setting('header_menu_icons'), true)[$key] != null)
                                                        {!! json_decode(get_setting('header_menu_icons'), true)[$key] !!}
                                                    @endif
                                                    {{ translate($value) }}
                                                </a>
                                            </li>
                                        @endforeach
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </nav>--}}
                </div>
            </div>
        </div>
    </div>

    <div class="mobile-header">
        <div class="logo-area">
            <div class="mobile-left-navbar">
                <div class="menu-area">
                    <nav class="navbar navbar-expand-lg bg-body-tertiary">
                        <div class="container-fluid">

                            <button class="navbar-toggler menu-enable" type="button" data-toggle="class-toggle"
                                data-target=".aiz-top-menu-sidebar">
                                <i class="fa fa-bars"></i>
                            </button>

                            <div
                                class="aiz-top-menu-sidebar collapse-sidebar-wrap sidebar-xl sidebar-left d-lg-none z-1035">
                                <div class="overlay overlay-fixed dark c-pointer close-btn" data-toggle="class-toggle"
                                    data-target=".aiz-top-menu-sidebar" data-same=".hide-top-menu-bar">
                                    <i class="fa fa-close"></i>
                                </div>
                                <div class="toggle-banding-logo">
                                    <a href="{{ url('/') }}">
                                        <img src="https://buzfi.com/public/uploads/all/E4uGHwNdMIPtx2RISv69ffw8J5VfK4llkDKRfY1C.png"
                                            alt="">
                                    </a>
                                </div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <button type="button" class="btn btn-sm p-4 hide-top-menu-bar"
                                        data-toggle="class-toggle" data-target=".aiz-top-menu-sidebar">
                                        <i class="las la-times la-2x text-primary"></i>
                                    </button>
                                    <ul class="mb-0 pl-3 pb-3 h-100">
                                        <li class="mr-0">
                                            <a href="#"
                                                class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                Home </a>
                                        </li>
                                        <li class="mr-0">
                                            <a href="#"
                                                class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                Flash Sale </a>
                                        </li>
                                        {{-- <li class="mr-0">
                                            <a href="#"
                                               class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                All Brands </a>
                                        </li> --}}
                                        <li class="mr-0">
                                            <a href="{{ route('categories.all') }}"
                                                class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                All categories </a>
                                        </li>
                                        <li class="mr-0">
                                            <a href="#"
                                                class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                About Buzfi </a>
                                        </li>
                                        @if (!Auth::check())
                                            <li class="mr-0">
                                                <a href="{{ route('shops.create') }}"
                                                    class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                    Become a Seller </a>
                                            </li>
                                            <li class="mr-0">
                                                <a href="{{ route('dropshipperLanding') }}"
                                                    class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                                                    Become a {{ translate('Dropshipper') }} </a>
                                            </li>
                                        @else
                                            <li class="mr-0">
                                                <a href="{{ route('logout') }}"
                                                    class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-primary header_menu_links">
                                                    {{ translate('Logout') }}
                                                </a>
                                            </li>
                                        @endif

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </nav>
                </div>
                <div class="banding-logo">
                    <a href="{{ route('home') }}">
                        <img src="{{ static_asset('frontend/assets/img/logo.png') }}" alt="">
                    </a>
                </div>
            </div>
            <div class="mobile-right-navbar custom_mobile_right">
                <div class="main-search">
                    <form action="{{ route('search') }}" method="GET" class="stop-propagation mb-0">
                        <div class="d-xl-none" data-toggle="class-toggle" data-target=".front-header-search">
                            <div class="search-input-box d-flex justify-content-center">

                                <button class="serch-btn mobile-search-click"><i class="fa fa-search"
                                        aria-hidden="true"></i></button>
                            </div>
                    </form>

                    {{-- <form action="action">
                  <input type="text" class="Search-btn" placeholder="Search in Buzfi ...">
                  <button class="serch-btn mobile-search-click" type="button">
                     <i class="fa fa-search" aria-hidden="true"></i>
                  </button>
               </form> --}}
                    {{--
               <button class="voice">
                  <i class="fa fa-microphone" aria-hidden="true"></i>
               </button>
               --}}
                </div>
            </div>
        </div>
    </div>

    <!--    [Finish Logo Area]-->

</header>


<!-- Top Menu Sidebar -->
<div class="aiz-top-menu-sidebar collapse-sidebar-wrap sidebar-xl sidebar-left d-lg-none z-1035">
    <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-top-menu-sidebar"
        data-same=".hide-top-menu-bar"></div>
    <div class="collapse-sidebar c-scrollbar-light text-left">
        <button type="button" class="btn btn-sm p-4 hide-top-menu-bar" data-toggle="class-toggle"
            data-target=".aiz-top-menu-sidebar">
            <i class="las la-times la-2x text-primary"></i>
        </button>
        @auth
            <span class="d-flex align-items-center nav-user-info pl-4">
                <!-- Image -->
                <span class="size-40px rounded-circle overflow-hidden border border-transparent nav-user-img">
                    @if (Auth::user()->avatar_original != null)
                        <img src="{{ uploaded_asset(Auth::user()->avatar_original) }}" class="img-fit h-100"
                            alt="{{ translate('avatar') }}"
                            onerror="this.onerror=null;this.src='{{ asset('assets/img/avatar-place.png') }}';">
                    @else
                        <img src="{{ asset('assets/img/avatar-place.png') }}" class="image"
                            alt="{{ translate('avatar') }}"
                            onerror="this.onerror=null;this.src='{{ asset('assets/img/avatar-place.png') }}';">
                    @endif
                </span>
                <!-- Name -->
                <h4 class="h5 fs-14 fw-700 text-dark ml-2 mb-0">{{ Auth::user()->name }}</h4>
            </span>
        @else
            <!--Login & Registration -->
            <span class="d-flex align-items-center nav-user-info pl-4">
                <!-- Image -->
                <span
                    class="size-40px rounded-circle overflow-hidden border d-flex align-items-center justify-content-center nav-user-img">
                    <svg xmlns="http://www.w3.org/2000/svg" width="19.902" height="20.012" viewBox="0 0 19.902 20.012">
                        <path id="fe2df171891038b33e9624c27e96e367"
                            d="M15.71,12.71a6,6,0,1,0-7.42,0,10,10,0,0,0-6.22,8.18,1.006,1.006,0,1,0,2,.22,8,8,0,0,1,15.9,0,1,1,0,0,0,1,.89h.11a1,1,0,0,0,.88-1.1,10,10,0,0,0-6.25-8.19ZM12,12a4,4,0,1,1,4-4A4,4,0,0,1,12,12Z"
                            transform="translate(-2.064 -1.995)" fill="#91919b" />
                    </svg>
                </span>
                <a href="{{ route('user.login') }}"
                    class="text-reset opacity-60 hov-opacity-100 hov-text-primary fs-12 d-inline-block border-right border-soft-light border-width-2 pr-2 ml-3">{{ translate('Login') }}</a>
                <a href="{{ route('user.registration') }}"
                    class="text-reset opacity-60 hov-opacity-100 hov-text-primary fs-12 d-inline-block py-2 pl-2">{{ translate('Registration') }}</a>
            </span>
        @endauth
        <hr>
        <ul class="mb-0 pl-3 pb-3 h-100">
            @if (get_setting('header_menu_labels') != null)
                @foreach (json_decode(get_setting('header_menu_labels'), true) as $key => $value)
                    <li class="mr-0">
                        <a href="{{ json_decode(get_setting('header_menu_links'), true)[$key] }}"
                            class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links
               @if (url()->current() == json_decode(get_setting('header_menu_links'), true)[$key]) active @endif">
                            {{ translate($value) }}
                        </a>
                    </li>
                @endforeach
            @endif
            @auth
                @if (isAdmin())
                    <hr>
                    <li class="mr-0">
                        <a href="{{ route('admin.dashboard') }}"
                            class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links">
                            {{ translate('My Account') }}
                        </a>
                    </li>
                @else
                    <hr>
                    <li class="mr-0">
                        <a href="{{ route('dashboard') }}"
                            class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links
               {{ areActiveRoutes(['dashboard'], ' active') }}">
                            {{ translate('My Account') }}
                        </a>
                    </li>
                @endif
                @if (isCustomer())
                    <li class="mr-0">
                        <a href="{{ route('all-notifications') }}"
                            class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links
               {{ areActiveRoutes(['all-notifications'], ' active') }}">
                            {{ translate('Notifications') }}
                        </a>
                    </li>
                    <li class="mr-0">
                        <a href="{{ route('wishlists.index') }}"
                            class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links
               {{ areActiveRoutes(['wishlists.index'], ' active') }}">
                            {{ translate('Wishlist') }}
                        </a>
                    </li>
                    {{--
         <li class="mr-0 d-none">
            <a href="{{ route('compare') }}"
               class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-dark header_menu_links
               {{ areActiveRoutes(['compare'], ' active') }}">
            {{ translate('Compare 1') }}
            </a>
         </li>
         --}}
                @endif
                <hr>
                <li class="mr-0">
                    <a href="{{ route('logout') }}"
                        class="fs-13 px-3 py-3 w-100 d-inline-block fw-700 text-primary header_menu_links">
                        {{ translate('Logout') }}
                    </a>
                </li>
            @endauth
        </ul>
        <br>
        <br>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="order_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
        <div class="modal-content">
            <div id="order-details-modal-body">
            </div>
        </div>
    </div>
</div>
@section('script')
    <script type="text/javascript">
        function show_order_details(order_id) {
            $('#order-details-modal-body').html(null);

            if (!$('#modal-size').hasClass('modal-lg')) {
                $('#modal-size').addClass('modal-lg');
            }

            $.post('{{ route('orders.details') }}', {
                _token: AIZ.data.csrf,
                order_id: order_id
            }, function(data) {
                $('#order-details-modal-body').html(data);
                $('#order_details').modal();
                $('.c-preloader').hide();
                AIZ.plugins.bootstrapSelect('refresh');
            });
        }
    </script>

    <script>
        $(document).ready(function() {
            // Show order details in modal
            $('.order-details-btn').click(function(e) {
                e.preventDefault();
                var orderCode = $(this).data('order-code');

                $('#order-details-modal').modal('show');
                // Ajax request to fetch order details would go here
            });

            // Notification toggle functionality
            $('#notification-toggle').click(function(e) {
                e.preventDefault();
                // This will be handled by the Vue component once it's implemented
                // For now, just prevent the default link behavior
            });

            // Initialize notification polling
            function checkNotifications() {
                $.ajax({
                    url: '/api/v3/user/notifications/unread-count',
                    method: 'GET',
                    success: function(response) {
                        if (response.success && response.count > 0) {
                            $('.notification-badge').text(response.count);
                            $('.notification-badge').show();
                        } else {
                            $('.notification-badge').hide();
                        }
                    }
                });
            }

            // Check for notifications on page load
            @auth
            checkNotifications();

            // Poll for new notifications every 30 seconds
            setInterval(checkNotifications, 30000);
            @endauth
        });
    </script>
@endsection
