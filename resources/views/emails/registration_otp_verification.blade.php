<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{$array['subject']}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 0;
            border-radius: 8px;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            text-align: center;
            padding: 30px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .header img {
            max-width: 150px;
            margin-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .content {
            padding: 40px 30px;
        }
        .otp-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 8px;
            margin: 30px 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .otp-code {
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .countdown {
            background-color: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 15px;
            font-weight: bold;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin: 20px 0;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            text-align: center;
        }
        .stat-item {
            padding: 10px;
        }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>Email Verification</h1>
            <p>Complete Your Registration with BUZFI</p>
        </div>
        
        <div class="content">
            <p>Hello {{$array['user_name'] ?? 'User'}},</p>
            
            <p>Thank you for registering with BUZFI! To complete your registration, please verify your email address using the One-Time Password (OTP) below:</p>
            
            <div class="otp-box">
                <p style="margin: 0; font-size: 16px;">Your Verification Code</p>
                <div class="otp-code">{{$array['otp']}}</div>
                <div class="countdown">⏱️ Expires in {{$array['remaining_minutes']}} minutes</div>
            </div>
            
            <div class="instructions">
                <h3 style="margin-top: 0; color: #667eea;">How to use this code:</h3>
                <ol>
                    <li>Return to the registration page where you entered your email</li>
                    <li>Enter this 6-digit code in the verification field</li>
                    <li>Complete your registration form</li>
                    <li>Start shopping with BUZFI!</li>
                </ol>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{{$array['remaining_minutes']}}</div>
                    <div class="stat-label">Minutes Left</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{$array['resend_count']}}</div>
                    <div class="stat-label">Attempt #</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Digit Code</div>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong><br>
                • This code will expire in {{$array['remaining_minutes']}} minutes<br>
                • If you didn't initiate this registration, please ignore this email<br>
                • Never share this code with anyone<br>
                • If you have issues, contact our support team
            </div>
            
            <p style="text-align: center;">
                <strong>Need help?</strong><br>
                Contact our support team at <a href="mailto:{{$array['support_email']}}">{{$array['support_email']}}</a>
            </p>
        </div>
        
        <div class="footer">
            <p>This email was sent by {{$array['app_name']}} | Registration Verification Service</p>
            <p>This is an automated message, please do not reply to this email.</p>
            <p>© {{ date('Y') }} {{$array['app_name']}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html> 