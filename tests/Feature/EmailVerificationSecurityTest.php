<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\EmailVerify;
use App\Models\User;
use App\Services\EmailVerificationSecurityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class EmailVerificationSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected $securityService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->securityService = app(EmailVerificationSecurityService::class);
    }

    /** @test */
    public function it_implements_rate_limiting_for_email_verification_requests()
    {
        $email = '<EMAIL>';
        
        // Make 5 requests (should be allowed)
        for ($i = 0; $i < 5; $i++) {
            $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
                'email' => $email,
                'user_type' => 'customer'
            ]);
            
            if ($i < 4) {
                $this->assertNotEquals(429, $response->getStatusCode());
            }
        }
        
        // 6th request should be rate limited
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ]);
        
        $this->assertEquals(429, $response->getStatusCode());
        $this->assertStringContainsString('Too many requests', $response->json('message'));
    }

    /** @test */
    public function it_implements_proper_otp_expiration_and_single_use_validation()
    {
        $email = '<EMAIL>';
        
        // Generate OTP
        $record = EmailVerify::generateOTP($email, 1); // 1 minute expiry
        $otp = $record->otp;
        
        // First verification should succeed
        $verified = EmailVerify::verifyOTP($email, $otp);
        $this->assertTrue($verified);
        
        // Second verification with same OTP should fail (single-use)
        $verified = EmailVerify::verifyOTP($email, $otp);
        $this->assertFalse($verified);
        
        // Generate new OTP and let it expire
        $record = EmailVerify::generateOTP($email, -1); // Already expired
        $expiredOtp = $record->otp;
        
        // Update expire time to past
        $record->update(['expire_time' => Carbon::now()->subMinutes(1)]);
        
        // Verification should fail for expired OTP
        $verified = EmailVerify::verifyOTP($email, $expiredOtp);
        $this->assertFalse($verified);
    }

    /** @test */
    public function it_generates_cryptographically_secure_otps()
    {
        $email = '<EMAIL>';
        $otps = [];
        
        // Generate multiple OTPs
        for ($i = 0; $i < 10; $i++) {
            $record = EmailVerify::generateOTP($email);
            $otps[] = $record->otp;
        }
        
        // Check that all OTPs are unique
        $this->assertEquals(count($otps), count(array_unique($otps)));
        
        // Check OTP format
        foreach ($otps as $otp) {
            $this->assertMatchesRegularExpression('/^\d{6}$/', $otp);
            $this->assertNotEquals('000000', $otp);
            $this->assertNotEquals('123456', $otp);
        }
    }

    /** @test */
    public function it_protects_against_email_enumeration_attacks()
    {
        // Create existing user
        User::factory()->create(['email' => '<EMAIL>']);
        
        $startTime = microtime(true);
        
        // Request for existing email
        $response1 = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);
        
        $time1 = microtime(true) - $startTime;
        
        $startTime = microtime(true);
        
        // Request for non-existing email
        $response2 = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ]);
        
        $time2 = microtime(true) - $startTime;
        
        // Response times should be similar (within 100ms)
        $timeDifference = abs($time1 - $time2);
        $this->assertLessThan(0.1, $timeDifference);
        
        // Both should return responses (not reveal existence)
        $this->assertNotNull($response1->json());
        $this->assertNotNull($response2->json());
    }

    /** @test */
    public function it_blocks_suspicious_activity()
    {
        $ip = '*************';
        
        // Simulate suspicious activity
        $this->securityService->blockIp($ip);
        
        // Request should be blocked
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => '<EMAIL>',
            'user_type' => 'customer'
        ], ['REMOTE_ADDR' => $ip]);
        
        $this->assertEquals(429, $response->getStatusCode());
    }

    /** @test */
    public function it_tracks_failed_otp_attempts()
    {
        $email = '<EMAIL>';
        $ip = '*************';
        
        // Track multiple failed attempts
        for ($i = 0; $i < 3; $i++) {
            $this->securityService->trackFailedAttempt($email, $ip);
        }
        
        // Check if attempts are being tracked
        $this->assertTrue(Cache::has("failed_otp:{$email}"));
        $this->assertTrue(Cache::has("failed_otp_ip:{$ip}"));
    }

    /** @test */
    public function it_validates_otp_format_securely()
    {
        // Valid OTP formats
        $this->assertTrue($this->securityService->validateOtpFormat('123456'));
        $this->assertTrue($this->securityService->validateOtpFormat('987654'));
        
        // Invalid OTP formats
        $this->assertFalse($this->securityService->validateOtpFormat('12345')); // Too short
        $this->assertFalse($this->securityService->validateOtpFormat('1234567')); // Too long
        $this->assertFalse($this->securityService->validateOtpFormat('12345a')); // Contains letter
        $this->assertFalse($this->securityService->validateOtpFormat('111111')); // All same digit
        $this->assertFalse($this->securityService->validateOtpFormat('123456')); // Sequential
    }

    /** @test */
    public function it_detects_bot_activity()
    {
        // Bot-like user agents
        $botUserAgents = [
            'curl/7.68.0',
            'python-requests/2.25.1',
            'Mozilla/5.0 (compatible; Googlebot/2.1)',
            'HeadlessChrome/91.0.4472.114'
        ];
        
        foreach ($botUserAgents as $userAgent) {
            $isBot = $this->securityService->detectBotActivity($userAgent, [
                'accept' => 'text/html',
                'accept-language' => 'en-US'
            ]);
            $this->assertTrue($isBot, "Failed to detect bot: {$userAgent}");
        }
        
        // Normal browser user agent
        $normalUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        $isBot = $this->securityService->detectBotActivity($normalUserAgent, [
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'accept-language' => 'en-US,en;q=0.5',
            'accept-encoding' => 'gzip, deflate'
        ]);
        $this->assertFalse($isBot);
    }

    /** @test */
    public function it_implements_secure_token_generation()
    {
        $tokens = [];
        
        // Generate multiple tokens
        for ($i = 0; $i < 10; $i++) {
            $token = $this->securityService->generateSecureToken();
            $tokens[] = $token;
            
            // Validate token security
            $this->assertTrue($this->securityService->validateTokenSecurity($token));
        }
        
        // All tokens should be unique
        $this->assertEquals(count($tokens), count(array_unique($tokens)));
        
        // Tokens should be proper length
        foreach ($tokens as $token) {
            $this->assertEquals(32, strlen($token));
            $this->assertTrue(ctype_xdigit($token));
        }
    }

    /** @test */
    public function it_clears_failed_attempts_after_successful_verification()
    {
        $email = '<EMAIL>';
        $ip = '*************';
        
        // Track failed attempts
        $this->securityService->trackFailedAttempt($email, $ip);
        $this->assertTrue(Cache::has("failed_otp:{$email}"));
        
        // Clear attempts
        $this->securityService->clearFailedAttempts($email, $ip);
        $this->assertFalse(Cache::has("failed_otp:{$email}"));
    }

    /** @test */
    public function it_provides_security_statistics()
    {
        $stats = $this->securityService->getSecurityStatistics();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('rate_limits', $stats);
    }

    /** @test */
    public function it_handles_timing_attacks_in_verification()
    {
        $email = '<EMAIL>';
        
        // Generate valid OTP
        $record = EmailVerify::generateOTP($email);
        $validOtp = $record->otp;
        
        $times = [];
        
        // Test with valid OTP
        $startTime = microtime(true);
        EmailVerify::verifyOTP($email, $validOtp);
        $times['valid'] = microtime(true) - $startTime;
        
        // Test with invalid OTP
        $startTime = microtime(true);
        EmailVerify::verifyOTP($email, '999999');
        $times['invalid'] = microtime(true) - $startTime;
        
        // Response times should be similar (within 50ms due to timing protection)
        $timeDifference = abs($times['valid'] - $times['invalid']);
        $this->assertLessThan(0.05, $timeDifference);
    }
}