<?php

namespace Tests\Feature;

use App\Models\EmailVerify;
use App\Models\User;
use App\Services\EmailVerificationDatabaseService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class EmailVerificationDatabaseIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected EmailVerificationDatabaseService $databaseService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->databaseService = app(EmailVerificationDatabaseService::class);
    }

    /** @test */
    public function it_uses_existing_database_tables_without_structural_changes()
    {
        // Verify email_verify table exists with expected columns
        $this->assertTrue(Schema::hasTable('email_verify'));
        
        $expectedColumns = [
            'id', 'email', 'otp', 'generate_date', 'expire_time', 
            'duration', 'is_verify', 'resend_count', 'last_resend_at', 
            'created_at', 'updated_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(
                Schema::hasColumn('email_verify', $column),
                "Column {$column} should exist in email_verify table"
            );
        }

        // Verify users table exists with auth columns
        $this->assertTrue(Schema::hasTable('users'));
        $authColumns = ['id', 'email', 'password', 'email_verified_at', 'remember_token'];
        
        foreach ($authColumns as $column) {
            $this->assertTrue(
                Schema::hasColumn('users', $column),
                "Column {$column} should exist in users table"
            );
        }
    }

    /** @test */
    public function it_implements_proper_indexing_for_email_lookup_performance()
    {
        // Check if required indexes exist
        $indexes = collect(DB::select("SHOW INDEX FROM email_verify"));
        
        // Should have index on email
        $hasEmailIndex = $indexes->contains(function ($index) {
            return str_contains($index->Key_name, 'email');
        });
        $this->assertTrue($hasEmailIndex, 'Email index should exist');

        // Should have index on expire_time for cleanup queries
        $hasExpireTimeIndex = $indexes->contains(function ($index) {
            return str_contains($index->Key_name, 'expire_time');
        });
        $this->assertTrue($hasExpireTimeIndex, 'Expire time index should exist');

        // Test performance optimization
        $result = $this->databaseService->optimizeEmailLookupPerformance();
        $this->assertTrue($result['performance_optimized']);
    }

    /** @test */
    public function it_cleans_up_expired_otp_records()
    {
        // Create test data
        $expiredRecord = EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'generate_date' => Carbon::now()->subMinutes(20),
            'expire_time' => Carbon::now()->subMinutes(10),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subMinutes(20)
        ]);

        $validRecord = EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '654321',
            'generate_date' => Carbon::now()->subMinutes(5),
            'expire_time' => Carbon::now()->addMinutes(5),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subMinutes(5)
        ]);

        // Run cleanup
        $results = $this->databaseService->cleanupExpiredRecords();

        // Verify expired OTP was cleared
        $expiredRecord->refresh();
        $this->assertNull($expiredRecord->otp);

        // Verify valid record was not affected
        $validRecord->refresh();
        $this->assertNotNull($validRecord->otp);

        // Verify cleanup results
        $this->assertEquals(1, $results['expired_otps_cleared']);
    }

    /** @test */
    public function it_maintains_compatibility_with_existing_authentication_token_structure()
    {
        // Test authentication compatibility
        $compatibility = $this->databaseService->verifyAuthenticationCompatibility();

        $this->assertTrue($compatibility['user_table_compatible']);
        $this->assertTrue($compatibility['email_verify_table_compatible']);

        // Test that EmailVerify model works with existing auth structure
        $this->assertTrue(EmailVerify::ensureAuthCompatibility());

        // Test that User model still works
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    /** @test */
    public function it_handles_database_integrity_checks()
    {
        // Create some test data with integrity issues
        
        // 1. Orphaned verified email record (email not in users table)
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => null,
            'generate_date' => Carbon::now()->subHour(),
            'expire_time' => Carbon::now()->subMinutes(50),
            'duration' => 10,
            'is_verify' => true,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subHour()
        ]);

        // 2. Expired OTP that should be cleared
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'generate_date' => Carbon::now()->subHour(),
            'expire_time' => Carbon::now()->subMinutes(50),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subHour()
        ]);

        // 3. Invalid resend count
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => null,
            'generate_date' => Carbon::now()->subHour(),
            'expire_time' => Carbon::now()->subMinutes(50),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => -1, // Invalid negative count
            'last_resend_at' => Carbon::now()->subHour()
        ]);

        // Run integrity checks
        $results = $this->databaseService->ensureDatabaseIntegrity();

        // Verify issues were found and fixed
        $this->assertGreaterThan(0, $results['integrity_issues_found']);
        $this->assertGreaterThan(0, $results['issues_fixed']);
        $this->assertNotEmpty($results['issues']);
    }

    /** @test */
    public function it_provides_database_statistics_for_monitoring()
    {
        // Create test data
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => null,
            'generate_date' => Carbon::now()->subHour(),
            'expire_time' => Carbon::now()->subMinutes(50),
            'duration' => 10,
            'is_verify' => true,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subHour()
        ]);

        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'generate_date' => Carbon::now()->subMinutes(5),
            'expire_time' => Carbon::now()->addMinutes(5),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 1,
            'last_resend_at' => Carbon::now()->subMinutes(5)
        ]);

        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        // Get statistics
        $stats = $this->databaseService->getDatabaseStatistics();

        // Verify statistics structure
        $this->assertArrayHasKey('email_verify_records', $stats);
        $this->assertArrayHasKey('users_count', $stats);
        $this->assertArrayHasKey('performance_metrics', $stats);

        // Verify counts
        $this->assertEquals(2, $stats['email_verify_records']['total']);
        $this->assertEquals(1, $stats['email_verify_records']['verified']);
        $this->assertEquals(1, $stats['email_verify_records']['pending']);
        $this->assertEquals(1, $stats['users_count']);
    }

    /** @test */
    public function it_handles_batch_operations_efficiently()
    {
        // Create multiple expired records
        for ($i = 0; $i < 10; $i++) {
            EmailVerify::create([
                'email' => "expired{$i}@test.com",
                'otp' => '123456',
                'generate_date' => Carbon::now()->subMinutes(20),
                'expire_time' => Carbon::now()->subMinutes(10),
                'duration' => 10,
                'is_verify' => false,
                'resend_count' => 1,
                'last_resend_at' => Carbon::now()->subMinutes(20)
            ]);
        }

        // Test batch cleanup
        $cleaned = EmailVerify::batchCleanupExpiredOTPs(5); // Batch size of 5
        $this->assertEquals(10, $cleaned);

        // Verify all OTPs were cleared
        $remainingOTPs = EmailVerify::whereNotNull('otp')->count();
        $this->assertEquals(0, $remainingOTPs);
    }

    /** @test */
    public function it_maintains_performance_with_large_datasets()
    {
        // Create a larger dataset
        for ($i = 0; $i < 100; $i++) {
            EmailVerify::create([
                'email' => "user{$i}@test.com",
                'otp' => str_pad($i, 6, '0', STR_PAD_LEFT),
                'generate_date' => Carbon::now()->subMinutes(rand(1, 30)),
                'expire_time' => Carbon::now()->addMinutes(rand(1, 10)),
                'duration' => 10,
                'is_verify' => $i % 2 === 0, // Half verified, half not
                'resend_count' => rand(0, 3),
                'last_resend_at' => Carbon::now()->subMinutes(rand(1, 30))
            ]);
        }

        // Test performance metrics
        $metrics = EmailVerify::getPerformanceMetrics();

        $this->assertArrayHasKey('email_lookup_ms', $metrics);
        $this->assertArrayHasKey('otp_verification_ms', $metrics);
        $this->assertArrayHasKey('cleanup_query_ms', $metrics);
        $this->assertEquals(100, $metrics['total_records']);

        // Performance should be reasonable (under 100ms for test queries)
        $this->assertLessThan(100, $metrics['email_lookup_ms']);
        $this->assertLessThan(100, $metrics['otp_verification_ms']);
        $this->assertLessThan(100, $metrics['cleanup_query_ms']);
    }

    /** @test */
    public function it_handles_edge_cases_in_database_operations()
    {
        // Test with empty database
        $results = $this->databaseService->cleanupExpiredRecords();
        $this->assertEquals(0, $results['expired_otps_cleared']);
        $this->assertEquals(0, $results['resend_counts_reset']);
        $this->assertEquals(0, $results['old_records_deleted']);

        // Test with null values
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => null,
            'generate_date' => null,
            'expire_time' => null,
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 0,
            'last_resend_at' => null
        ]);

        // Should not cause errors
        $stats = EmailVerify::getCleanupStatistics();
        $this->assertIsArray($stats);
    }

    /** @test */
    public function it_preserves_existing_functionality()
    {
        // Test that existing EmailVerify methods still work
        $email = '<EMAIL>';
        
        // Generate OTP
        $record = EmailVerify::generateOTP($email, 10);
        $this->assertNotNull($record);
        $this->assertEquals($email, $record->email);
        $this->assertNotNull($record->otp);

        // Verify OTP
        $verified = EmailVerify::verifyOTP($email, $record->otp);
        $this->assertTrue($verified);

        // Check if verified
        $isVerified = EmailVerify::isEmailVerified($email);
        $this->assertTrue($isVerified);

        // Test cooldown functionality
        $record->refresh();
        $this->assertFalse($record->isInCooldownPeriod()); // Should not be in cooldown after verification
    }
}