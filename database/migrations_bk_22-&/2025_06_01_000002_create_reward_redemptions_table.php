<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('reward_redemptions')) {
            Schema::create('reward_redemptions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id');
                $table->foreignId('option_id')->constrained('reward_redemption_options');
                $table->string('option_title');
                $table->integer('points_used');
            $table->decimal('value_redeemed', 10, 2);
            $table->enum('status', ['pending', 'completed', 'failed'])->default('completed');
            $table->string('redemption_code')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_redemptions');
    }
}; 