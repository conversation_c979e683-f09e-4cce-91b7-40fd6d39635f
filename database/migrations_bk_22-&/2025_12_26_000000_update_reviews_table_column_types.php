<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            // Change photos column from varchar(191) to TEXT to support JSON arrays
            if (Schema::hasColumn('reviews', 'photos')) {
                $table->text('photos')->nullable()->change();
            }
            
            // Change review_videos column from varchar(191) to TEXT to support JSON arrays
            if (Schema::hasColumn('reviews', 'review_videos')) {
                $table->text('review_videos')->nullable()->change();
            }
            
            // Ensure tags column is TEXT (it should already be JSON but just in case)
            if (Schema::hasColumn('reviews', 'tags')) {
                $table->text('tags')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            // Revert back to varchar if needed (be careful with data loss)
            if (Schema::hasColumn('reviews', 'photos')) {
                $table->string('photos', 191)->nullable()->change();
            }
            
            if (Schema::hasColumn('reviews', 'review_videos')) {
                $table->string('review_videos', 191)->nullable()->change();
            }
            
            if (Schema::hasColumn('reviews', 'tags')) {
                $table->json('tags')->nullable()->change();
            }
        });
    }
}; 