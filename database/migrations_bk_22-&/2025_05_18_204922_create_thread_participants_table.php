<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('thread_participants')) {
            Schema::create('thread_participants', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('thread_id');
                $table->unsignedInteger('user_id');
                $table->string('role')->default('member');
                $table->timestamps();
                $table->unique(['thread_id', 'user_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('thread_participants');
    }
};
