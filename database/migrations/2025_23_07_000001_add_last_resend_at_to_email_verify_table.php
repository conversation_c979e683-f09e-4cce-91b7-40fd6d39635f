<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_verify', function (Blueprint $table) {
            $table->timestamp('last_resend_at')->nullable()->after('resend_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_verify', function (Blueprint $table) {
            $table->dropColumn('last_resend_at');
        });
    }
};
