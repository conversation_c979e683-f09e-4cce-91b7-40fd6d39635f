#!/bin/bash

# Buzfi Laravel Project - Quick Start Script
# This script provides an easy way to start the project

set -e

echo "🚀 Starting Buzfi Laravel Project with Docker"
echo "=============================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! docker compose version > /dev/null 2>&1; then
    echo "❌ Docker Compose is not available. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker is running"

# Navigate to docker directory
cd "$(dirname "$0")"

echo "📦 Building Docker containers..."
docker compose build --no-cache

echo "🔄 Starting services..."
docker compose up -d

echo "⏳ Waiting for services to be ready..."
sleep 15

echo "📋 Installing Laravel dependencies..."
docker compose exec -T php composer install --no-interaction --prefer-dist --optimize-autoloader

echo "🔧 Setting up <PERSON><PERSON> application..."
# Copy environment file if it doesn't exist
if [ ! -f "../.env" ]; then
    echo "📄 Creating .env file..."
    docker compose exec -T php cp .env.example .env
fi

# Generate application key
echo "🔑 Generating application key..."
docker compose exec -T php php artisan key:generate --no-interaction

# Create storage link
echo "🔗 Creating storage link..."
docker compose exec -T php php artisan storage:link --no-interaction

# Clear caches
echo "🧹 Clearing caches..."
docker compose exec -T php php artisan config:clear --no-interaction
docker compose exec -T php php artisan cache:clear --no-interaction
docker compose exec -T php php artisan view:clear --no-interaction
docker compose exec -T php php artisan route:clear --no-interaction

# Run migrations
echo "🗄️ Running database migrations..."
sleep 5
docker compose exec -T php php artisan migrate --no-interaction --force

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📍 Access URLs:"
echo "   Application:     http://localhost/buzfi-new-backend"
echo "   phpMyAdmin:      http://localhost:8080"
echo "   Redis Commander: http://localhost:8081"
echo ""
echo "🔐 Database Credentials:"
echo "   Host: localhost:3306"
echo "   Database: buzfi"
echo "   Username: buzfi_user"
echo "   Password: buzfi_password"
echo "   Root Password: root_password"
echo ""
echo "📊 Container Status:"
docker compose ps

echo ""
echo "💡 Useful commands:"
echo "   Stop services:    docker compose down"
echo "   View logs:        docker compose logs -f"
echo "   Access PHP shell: docker compose exec php bash"
echo "   Access MySQL:     docker compose exec mysql mysql -u root -proot_password buzfi"
echo ""
echo "📖 For more commands, check the Makefile or run 'make help'"
