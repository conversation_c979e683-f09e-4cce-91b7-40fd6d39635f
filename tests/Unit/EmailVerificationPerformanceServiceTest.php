<?php

namespace Tests\Unit;

use App\Services\EmailVerificationPerformanceService;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class EmailVerificationPerformanceServiceTest extends TestCase
{
    protected $performanceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->performanceService = new EmailVerificationPerformanceService();
    }

    /** @test */
    public function it_can_clear_expired_caches()
    {
        $cleared = $this->performanceService->clearExpiredCaches();
        
        $this->assertIsInt($cleared);
        $this->assertGreaterThanOrEqual(0, $cleared);
    }

    /** @test */
    public function it_can_invalidate_email_caches()
    {
        $email = '<EMAIL>';
        
        // This should not throw any exceptions
        $this->performanceService->invalidateEmailCaches($email);
        
        $this->assertTrue(true); // If we get here, the method worked
    }

    /** @test */
    public function it_can_get_performance_metrics()
    {
        $metrics = $this->performanceService->getPerformanceMetrics();

        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('cached_email_lookup_ms', $metrics);
        $this->assertArrayHasKey('direct_email_lookup_ms', $metrics);
        $this->assertArrayHasKey('cached_verification_ms', $metrics);
        $this->assertArrayHasKey('cache_hit_improvement', $metrics);
        $this->assertArrayHasKey('cache_statistics', $metrics);
        
        // Verify numeric values
        $this->assertIsNumeric($metrics['cached_email_lookup_ms']);
        $this->assertIsNumeric($metrics['direct_email_lookup_ms']);
        $this->assertIsNumeric($metrics['cached_verification_ms']);
        $this->assertIsNumeric($metrics['cache_hit_improvement']);
    }

    /** @test */
    public function it_can_monitor_query_performance()
    {
        $queryMetrics = $this->performanceService->monitorQueryPerformance();

        $this->assertIsArray($queryMetrics);
        $this->assertArrayHasKey('email_existence', $queryMetrics);
        $this->assertArrayHasKey('otp_verification', $queryMetrics);
        $this->assertArrayHasKey('verification_status', $queryMetrics);
        $this->assertArrayHasKey('cleanup_expired', $queryMetrics);

        foreach ($queryMetrics as $metric) {
            $this->assertArrayHasKey('execution_time_ms', $metric);
            $this->assertArrayHasKey('status', $metric);
            $this->assertIsNumeric($metric['execution_time_ms']);
            $this->assertContains($metric['status'], ['success', 'error']);
        }
    }

    /** @test */
    public function it_handles_cache_operations_gracefully()
    {
        // Test cache clearing
        $cleared = $this->performanceService->clearExpiredCaches();
        $this->assertIsInt($cleared);

        // Test cache invalidation
        $this->performanceService->invalidateEmailCaches('<EMAIL>');
        $this->assertTrue(true); // No exception means success

        // Test cache warming (with empty array)
        $warmed = $this->performanceService->warmUpEmailCaches([]);
        $this->assertEquals(0, $warmed);
    }

    /** @test */
    public function it_can_warm_up_caches_with_empty_array()
    {
        $warmed = $this->performanceService->warmUpEmailCaches([]);
        $this->assertEquals(0, $warmed);
    }

    /** @test */
    public function it_returns_consistent_performance_metrics_structure()
    {
        $metrics1 = $this->performanceService->getPerformanceMetrics();
        $metrics2 = $this->performanceService->getPerformanceMetrics();

        // Both should have the same structure
        $this->assertEquals(array_keys($metrics1), array_keys($metrics2));
        
        // Cache statistics should have consistent structure
        if (isset($metrics1['cache_statistics']) && isset($metrics2['cache_statistics'])) {
            $this->assertEquals(
                array_keys($metrics1['cache_statistics']), 
                array_keys($metrics2['cache_statistics'])
            );
        }
    }

    /** @test */
    public function it_handles_different_cache_drivers()
    {
        // Get current cache driver
        $driver = config('cache.default');
        
        $metrics = $this->performanceService->getPerformanceMetrics();
        $cacheStats = $metrics['cache_statistics'];
        
        $this->assertArrayHasKey('driver', $cacheStats);
        $this->assertEquals($driver, $cacheStats['driver']);
        
        // For non-Redis drivers, certain stats should be N/A
        if ($driver !== 'redis') {
            $this->assertEquals('N/A', $cacheStats['used_memory']);
            $this->assertEquals('N/A', $cacheStats['keyspace_hits']);
        }
    }

    /** @test */
    public function it_measures_execution_times_correctly()
    {
        $queryMetrics = $this->performanceService->monitorQueryPerformance();
        
        foreach ($queryMetrics as $queryName => $metric) {
            if ($metric['status'] === 'success') {
                $this->assertGreaterThanOrEqual(0, $metric['execution_time_ms']);
                $this->assertLessThan(1000, $metric['execution_time_ms']); // Should be less than 1 second
            }
        }
    }

    /** @test */
    public function it_provides_meaningful_cache_statistics()
    {
        $metrics = $this->performanceService->getPerformanceMetrics();
        $cacheStats = $metrics['cache_statistics'];
        
        $this->assertIsArray($cacheStats);
        
        // Should always have driver information
        $this->assertArrayHasKey('driver', $cacheStats);
        $this->assertNotEmpty($cacheStats['driver']);
        
        // Should handle errors gracefully
        if (isset($cacheStats['error'])) {
            $this->assertIsString($cacheStats['error']);
            $this->assertArrayHasKey('message', $cacheStats);
        }
    }
}