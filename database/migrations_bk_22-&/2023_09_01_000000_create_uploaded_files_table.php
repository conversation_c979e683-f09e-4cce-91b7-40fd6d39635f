<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('uploaded_files')) {
            Schema::create('uploaded_files', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->onDelete('cascade');
                $table->string('name');
                $table->string('path');
                $table->string('type');
            $table->bigInteger('size');
            $table->string('mime_type');
            $table->string('extension');
            $table->timestamps();
                
                // Indexes
                $table->index('user_id');
                $table->index('type');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('uploaded_files');
    }
}; 