<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('return_knowledge_bases')) {
            Schema::create('return_knowledge_bases', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description');
                $table->string('url')->nullable();
                $table->string('icon')->nullable();
                $table->boolean('is_featured')->default(false);
                $table->string('type')->default('resource');
                $table->integer('order')->default(0);
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('return_knowledge_bases');
    }
}; 