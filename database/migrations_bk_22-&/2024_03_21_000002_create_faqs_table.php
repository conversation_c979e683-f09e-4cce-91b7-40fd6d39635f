<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('faqs')) {
            Schema::create('faqs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('category_id')->constrained('faq_categories')->onDelete('cascade');
                $table->string('question');
                $table->text('answer');
                $table->string('slug')->unique();
                $table->boolean('is_active')->default(true);
                $table->integer('view_count')->default(0);
                $table->integer('helpful_count')->default(0);
                $table->integer('not_helpful_count')->default(0);
                $table->integer('position')->default(0);
                $table->timestamps();
                });
        }
    }

    public function down()
    {
        Schema::dropIfExists('faqs');
    }
}; 