<?php

namespace App\Http\Resources\V3\Customer;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Customer Offer Resource
 * Adapts Offer data to match PersonalizedOffer interface
 */
class CustomerOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'offerType' => $this->mapOfferType($this->offer_type),
            'discount' => (float) ($this->discount_percentage ?? 0),
            'discountAmount' => (float) ($this->discount_amount ?? 0),
            'minOrderValue' => (float) ($this->min_order_value ?? 0),
            'startDate' => $this->start_date ? $this->start_date->toIso8601String() : null,
            'endDate' => $this->end_date ? $this->end_date->toIso8601String() : null,
            'expiresAt' => $this->end_date ? $this->end_date->toIso8601String() : null,
            'isExclusive' => (bool) ($this->is_exclusive ?? false),
            'isSeasonal' => (bool) ($this->is_seasonal ?? false),
            'isPersonalized' => (bool) ($this->is_personalized ?? false),
            'priority' => (int) ($this->priority ?? 0),
            'promoCode' => $this->promo_code ?? null,
            'usageLimit' => (int) ($this->usage_limit ?? 0),
            'usedCount' => (int) ($this->used_count ?? 0),
            'isRedeemed' => false, // This would require checking user redemption status
            'isExpired' => $this->end_date ? $this->end_date->isPast() : false,
            'tags' => $this->tags ? (is_array($this->tags) ? $this->tags : json_decode($this->tags, true)) : [],
            'termsAndConditions' => $this->terms_conditions ? (is_array($this->terms_conditions) ? $this->terms_conditions : json_decode($this->terms_conditions, true)) : [],
            'offerImage' => $this->offer_banner ? uploaded_asset($this->offer_banner) : "",
            'imageUrl' => $this->offer_banner ? uploaded_asset($this->offer_banner) : "",
            'bannerImage' => $this->offer_banner ? uploaded_asset($this->offer_banner) : "",
            'offerBanner' => $this->offer_banner ? uploaded_asset($this->offer_banner) : "",
            'ctaText' => $this->cta_text ?? 'Apply Offer',
            'ctaUrl' => $this->cta_url ?? '#',
            'personalizedReason' => $this->getPersonalizedReason(),
            'categories' => $this->getCategories(),
            'products' => $this->getProducts(),
            'theme' => [
                'primaryColor' => $this->theme_primary_color ?? '#3B82F6',
                'secondaryColor' => $this->theme_secondary_color ?? '#1E40AF',
                'gradientStart' => $this->theme_gradient_start ?? 'rgba(59, 130, 246, 0.85)',
                'gradientEnd' => $this->theme_gradient_end ?? 'rgba(30, 64, 175, 0.9)',
                'textColor' => $this->theme_text_color ?? '#FFFFFF'
            ],
            'bulkDiscountThresholds' => $this->getBulkDiscountThresholds(),
            'savingsAmount' => $this->calculateSavingsAmount(),
            'type' => $this->mapDiscountType(),
            'isdropshipperOnly' => (bool) ($this->is_dropshipper_only ?? false),
            'created_at' => $this->created_at ? $this->created_at->toIso8601String() : null,
            'updated_at' => $this->updated_at ? $this->updated_at->toIso8601String() : null,
        ];
    }

    /**
     * Map offer type to frontend expected values
     */
    private function mapOfferType($offerType)
    {
        $typeMap = [
            'discount' => 'category_discount',
            'flash_sale' => 'flash_sale',
            'bundle' => 'product_bundle',
            'free_shipping' => 'free_shipping',
            'buy_one_get_one' => 'loyalty_reward',
            'seasonal' => 'seasonal_promotion',
            'loyalty' => 'loyalty_reward'
        ];

        return $typeMap[$offerType] ?? 'product_recommendation';
    }

    /**
     * Map discount type to frontend expected values
     */
    private function mapDiscountType()
    {
        $discountType = $this->discount_type ?? 'percentage';

        $typeMap = [
            'percentage' => 'percentage',
            'fixed_amount' => 'fixed',
            'free_shipping' => 'free_shipping',
            'buy_x_get_y' => 'buy_x_get_y'
        ];

        return $typeMap[$discountType] ?? 'percentage';
    }

    /**
     * Get personalized reason based on offer type
     */
    private function getPersonalizedReason()
    {
        if ($this->is_personalized) {
            $reasons = [
                'Based on your recent purchases',
                'Recommended for you',
                'Popular in your area',
                'Trending in your category',
                'Exclusive for loyal customers'
            ];
            return $reasons[array_rand($reasons)];
        }

        return $this->description ?? 'Great deal available for everyone!';
    }

    /**
     * Get categories associated with this offer
     */
    private function getCategories()
    {
        // This would need to be implemented based on your relationship structure
        // For now, return empty array or mock data
        return [];
    }

    /**
     * Get products associated with this offer
     */
    private function getProducts()
    {
        // This would need to be implemented based on your relationship structure
        // For now, return empty array or default structure
        return [];
    }

    /**
     * Get bulk discount thresholds
     */
    private function getBulkDiscountThresholds()
    {
        if ($this->bulk_discount_thresholds) {
            if (is_string($this->bulk_discount_thresholds)) {
                return json_decode($this->bulk_discount_thresholds, true) ?? [];
            }
            return $this->bulk_discount_thresholds;
        }

        return [];
    }

    /**
     * Calculate potential savings amount
     */
    private function calculateSavingsAmount()
    {
        if ($this->discount_amount) {
            return (float) $this->discount_amount;
        }

        if ($this->discount_percentage && $this->min_order_value) {
            return (float) ($this->min_order_value * ($this->discount_percentage / 100));
        }

        return 0;
    }

    /**
     * Get proper offer image URL
     */
    private function getOfferImageUrl()
    {
        if ($this->offer_banner) {
            // Get the upload record for the image
            $upload = \App\Models\Upload::find($this->offer_banner);
            if ($upload && $upload->file_name) {
                // If external link exists, use it
                if ($upload->external_link) {
                    return $upload->external_link;
                }

                // Get base URL without trailing slash
                $baseUrl = rtrim(config('app.url', request()->getSchemeAndHttpHost()), '/');
                $fileName = ltrim($upload->file_name, '/');

                // Build the correct URL
                // Files are in public/uploads/ directory
                // URL should be: http://localhost/buzfi-n-main/buzfi-backend-new/public/uploads/all/filename.png
                return $baseUrl . '/public/' . $fileName;
            }
        }

        // Fallback images
        return config('app.url') . '/public/assets/img/placeholder.svg';
    }
}
