-- Initialize the buzfi database
-- This script runs before the main buzfi.sql import

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS buzfi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user if it doesn't exist
CREATE USER IF NOT EXISTS 'buzfi_user'@'%' IDENTIFIED BY 'buzfi_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON buzfi.* TO 'buzfi_user'@'%';
GRANT ALL PRIVILEGES ON buzfi.* TO 'root'@'%';

-- Use the buzfi database for subsequent operations
USE buzfi;

-- Flush privileges
FLUSH PRIVILEGES;
