<?php

use App\Http\Controllers\ActivityController;
use App\Http\Controllers\AddonController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AizUploadController;
use App\Http\Controllers\AttributeController;
use App\Http\Controllers\AttributeValueController;
use App\Http\Controllers\BlogCategoryController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BusinessSettingsController;
use App\Http\Controllers\CarrierController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CityController;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\ConversationController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\CustomerProductController;
use App\Http\Controllers\DigitalProductController;
use App\Http\Controllers\FlashDealController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PickupPointController;
use App\Http\Controllers\ProductBulkUploadController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductQueryController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SellerController;
use App\Http\Controllers\B2BController;
use App\Http\Controllers\SellerWithdrawRequestController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\StateController;
use App\Http\Controllers\SubscriberController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\TaxController;
use App\Http\Controllers\UpdateController;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\ZoneController;
use App\Http\Controllers\BulkProductUploadController;
use App\Http\Controllers\BrandBulkUploadController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\Admin\ProductsByCategorySectionController;
use App\Http\Controllers\CollectionController; // Arif
use App\Http\Controllers\ExportController;
use App\Http\Controllers\Admin\WizardController;
use App\Http\Controllers\PriceAlertController;
use App\Http\Controllers\OfferController;
use App\Http\Controllers\CampaignController;

/*
  |--------------------------------------------------------------------------
  | Admin Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register admin routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */

Route::get('/fetch-products', [ProductController::class, 'fetchProducts'])->name('fetch.products');
Route::get('/fetch-fetch', [ProductController::class, 'fetchUsers'])->name('fetch.users');
Route::get('/website-banners', [WebsiteController::class, 'banners'])->name('website.banners');
Route::post('/website-banners', [WebsiteController::class, 'update_banners'])->name('website.banners.update');
Route::get('/popular-products', [WebsiteController::class, 'popular_products'])->name('website.popular_products');
Route::post('/popular-products', [WebsiteController::class, 'update_popular_products'])->name('website.update_popular_products');
Route::post('/get-popular-products', [WebsiteController::class, 'get_popular_products'])->name('website.get_popular_products');
//Update Routes
Route::controller(UpdateController::class)->group(function () {
    Route::post('/update', 'step0')->name('update');
    Route::get('/update/step1', 'step1')->name('update.step1');
    Route::get('/update/step2', 'step2')->name('update.step2');
    Route::get('/update/step3', 'step3')->name('update.step3');
    Route::post('/purchase_code', 'purchase_code')->name('update.code');
});

Route::get('/admin', [AdminController::class, 'admin_dashboard'])->name('admin.dashboard')->middleware(['auth', 'admin', 'prevent-back-history']);
Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'admin', 'prevent-back-history']], function () {
    Route::get('/activities', [ActivityController::class, 'showActivities'])->name('admin.activities');
    // Collections by Arif
    Route::resource('collections', CollectionController::class);
    Route::get('collections/{collection}/search-product', [CollectionController::class, 'searchProduct'])->name('collections.search_product');
    Route::get('/api/products', [CollectionController::class, 'apiIndex'])->name('api.products.index');

    // Wizard
    Route::group(['prefix' => 'wizards', 'as' => 'wizards.'], function () {
        Route::get('/', [WizardController::class, 'index'])->name('index');
        Route::get('/{wizard}/edit', [WizardController::class, 'edit'])->name('edit');
        Route::put('/{wizard}', [WizardController::class, 'update'])->name('update');
        Route::put('/{wizard}/update-banner', [WizardController::class, 'update_banner'])->name('update-banner');
        Route::post('wizards/get_wizard_products', [WizardController::class, 'get_wizard_products'])->name('get_wizard_products');
        Route::POST('/update_status', [WizardController::class, 'update_status'])->name('update_status');
    });
    Route::group(['prefix' => 'user_feedback', 'as' => 'user_feedback.'], function () {
        Route::get('/', [\App\Http\Controllers\Admin\UserFeedbackController::class, 'index'])->name('index');
    });

    // category
    Route::resource('categories', CategoryController::class);
    Route::controller(CategoryController::class)->group(function () {
        Route::get('/categories/edit/{id}', 'edit')->name('categories.edit');
        Route::get('/categories/destroy/{id}', 'destroy')->name('categories.destroy');
        Route::post('/categories/featured', 'updateFeatured')->name('categories.featured');
        Route::post('/categories/update-category-visibility', 'updateCategoryVisibility')->name('admin.update-category-visibility');
    });

    // Brand
    Route::resource('brands', BrandController::class);
    Route::controller(BrandController::class)->group(function () {
        Route::get('/brands/edit/{id}', 'edit')->name('brands.edit');
        Route::get('/brands/destroy/{id}', 'destroy')->name('brands.destroy');
    });

    // Products
    Route::controller(ProductController::class)->group(function () {
        Route::get('/products/admin', 'admin_products')->name('products.admin');
        Route::get('/products/seller/{product_type}', 'seller_products')->name('products.seller');
        Route::get('/products/all', 'all_products')->name('products.all');
        Route::get('/products/create', 'create')->name('products.create');
        Route::post('/products/store/', 'store')->name('products.store');
        Route::get('/products/admin/{id}/edit', 'admin_product_edit')->name('products.admin.edit');
        Route::get('/products/seller/{id}/edit', 'seller_product_edit')->name('products.seller.edit');
        Route::post('/products/update/{product}', 'update')->name('products.update');
        Route::post('/products/todays_deal', 'updateTodaysDeal')->name('products.todays_deal');
        Route::post('/products/featured', 'updateFeatured')->name('products.featured');
        Route::post('/products/published', 'updatePublished')->name('products.published');
        Route::post('/products/update_is_premium', 'update_is_premium')->name('products.update_is_premium');
        Route::post('/products/approved', 'updateProductApproval')->name('products.approved');
        Route::post('/products/get_products_by_subcategory', 'get_products_by_subcategory')->name('products.get_products_by_subcategory');
        Route::get('/products/duplicate/{id}', 'duplicate')->name('products.duplicate');
        Route::get('/products/destroy/{id}', 'destroy')->name('products.destroy');
        Route::post('/bulk-product-delete', 'bulk_product_delete')->name('bulk-product-delete');

        Route::post('/products/sku_combination', 'sku_combination')->name('products.sku_combination');
        Route::post('/products/sku_combination_edit', 'sku_combination_edit')->name('products.sku_combination_edit');
        Route::post('/products/add-more-choice-option', 'add_more_choice_option')->name('products.add-more-choice-option');
    });

    // Digital Product
    Route::resource('digitalproducts', DigitalProductController::class);
    Route::controller(DigitalProductController::class)->group(function () {
        Route::get('/digitalproducts/edit/{id}', 'edit')->name('digitalproducts.edit');
        Route::get('/digitalproducts/destroy/{id}', 'destroy')->name('digitalproducts.destroy');
        Route::get('/digitalproducts/download/{id}', 'download')->name('digitalproducts.download');
    });

    Route::controller(ProductBulkUploadController::class)->group(function () {
        //Product Export
        Route::get('/product-bulk-export', 'export')->name('product_bulk_export.index');
        //bulk edit
        Route::get('/product-bulk-edit', 'bulkEdit')->name('product_bulk_edit');
        Route::post('/product-bulk-update', 'bulkProUpdate')->name('bulk_product_update');
        //Product Bulk Upload
        Route::get('/product-bulk-upload/index', 'index')->name('product_bulk_upload.index');
        Route::post('/bulk-product-upload', 'bulk_upload')->name('bulk_product_upload');
        Route::get('/product-csv-download/{type}', 'import_product')->name('product_csv.download');
        Route::get('/vendor-product-csv-download/{id}', 'import_vendor_product')->name('import_vendor_product.download');
        Route::group(['prefix' => 'bulk-upload/download'], function () {
            Route::get('/category', 'pdf_download_category')->name('pdf.download_category');
            Route::get('/brand', 'pdf_download_brand')->name('pdf.download_brand');
            Route::get('/seller', 'pdf_download_seller')->name('pdf.download_seller');
        });
    });
    // added by arif
    // Product Bulk Upload
    Route::controller(BulkProductUploadController::class)->group(function () {
        //Product Export
        Route::get('/bulk-product-export', 'export')->name('bulk_product_export.index');
        //bulk edit
        Route::get('/bulk-product-edit', 'bulkEdit')->name('bulk_product_edit');
        Route::post('/bulk-product-update', 'bulkProductUpdate')->name('bulk_product_update');
        //bulk delete
        Route::get('/bulk-product-deleted', 'bulk_product_delete')->name('bulk_product_deleted');
        Route::post('/bulk-product-destroy', 'bulk_product_destroy')->name('bulk_product_destroy');
        //Product Bulk Upload
        Route::get('/bulk-product-upload/index', 'index')->name('bulk_product_upload.index');
        Route::post('/bulk-product-upload', 'bulk_product_upload')->name('bulk_product_upload');
        Route::group(['prefix' => 'bulk-product-upload/download'], function () {
            Route::get('/products', 'excel_download_products')->name('excel.bulk_product_download_excel');
        });
        Route::get('/import-bulk-status', 'getbulkUploadStatus')->name('import.status');




        Route::get('/bulk-product-export', 'bulk_product_export')->name('bulk_product_export');
        Route::get('/bulk-filter-products', 'filter_products')->name('bulk_products.filter');
        Route::get('/filter-bulk-product-export', 'product_bulk_export')->name('filter_bulk_product_export.product_export');
    });

    // end by arif

    // Brand Bulk Upload
    Route::controller(BrandBulkUploadController::class)->group(function () {
        //Brand Export
        Route::get('/bulk-brand-export', 'export')->name('bulk_brand_export.index');
        //bulk edit
        Route::get('/bulk-brand-edit', 'bulkEdit')->name('bulk_brand_edit');
        Route::post('/bulk-brand-update', 'bulkbrandUpdate')->name('bulk_brand_update');
        //bulk delete
        Route::get('/bulk-brand-deleted', 'bulk_brand_delete')->name('bulk_brand_deleted');
        Route::post('/bulk-brand-destroy', 'bulk_brand_destroy')->name('bulk_brand_destroy');
        //brand Bulk Upload
        Route::get('/bulk-brand-upload/index', 'index')->name('bulk_brand_upload.index');
        Route::post('/bulk-brand-upload', 'bulk_brand_upload')->name('bulk_brand_upload');
        Route::group(['prefix' => 'bulk-brand-upload/download'], function () {
            Route::get('/brands', 'excel_download_brands')->name('excel.bulk_brand_download_excel');
        });

        Route::get('/import-bulk-status', 'getbulkUploadStatus')->name('import.status');
    });



    // Seller
    Route::resource('sellers', SellerController::class);
    Route::controller(SellerController::class)->group(function () {
        Route::get('sellers_ban/{id}', 'ban')->name('sellers.ban');
        Route::get('/sellers/destroy/{id}', 'destroy')->name('sellers.destroy');
        Route::post('/bulk-seller-delete', 'bulk_seller_delete')->name('bulk-seller-delete');
        Route::get('/sellers/view/{id}/verification', 'show_verification_request')->name('sellers.show_verification_request');
        Route::get('/sellers/approve/{id}', 'approve_seller')->name('sellers.approve');
        Route::get('/sellers/reject/{id}', 'reject_seller')->name('sellers.reject');
        Route::get('/sellers/login/{id}', 'login')->name('sellers.login');
        Route::post('/sellers/payment_modal', 'payment_modal')->name('sellers.payment_modal');
        Route::post('/sellers/profile_modal', 'profile_modal')->name('sellers.profile_modal');
        Route::post('/sellers/approved', 'updateApproved')->name('sellers.approved');
    });

    // Seller Payment
    Route::controller(PaymentController::class)->group(function () {
        Route::get('/seller/payments', 'payment_histories')->name('sellers.payment_histories');
        Route::get('/seller/payments/show/{id}', 'show')->name('sellers.payment_history');
    });

    // Seller Withdraw Request
    Route::resource('/withdraw_requests', SellerWithdrawRequestController::class);
    Route::controller(SellerWithdrawRequestController::class)->group(function () {
        Route::get('/withdraw_requests_all', 'index')->name('withdraw_requests_all');
        Route::post('/withdraw_request/payment_modal', 'payment_modal')->name('withdraw_request.payment_modal');
        Route::post('/withdraw_request/message_modal', 'message_modal')->name('withdraw_request.message_modal');
    });

    // dropshipper admin by suman
    Route::resource('dropshipper', B2BController::class);
    Route::controller(B2BController::class)->group(function () {
        Route::get('dropshipper_ban/{id}', 'ban')->name('dropshipper.ban');
        Route::get('/dropshipper/destroy/{id}', 'destroy')->name('dropshipper.destroy');
        Route::post('/bulk-seller-delete', 'bulk_seller_delete')->name('bulk-seller-delete');
        Route::get('/dropshipper/view/{id}/verification', 'show_verification_request')->name('dropshipper.show_verification_request');
        Route::get('/dropshipper/approve/{id}', 'approve_seller')->name('dropshipper.approve');
        Route::get('/dropshipper/reject/{id}', 'reject_seller')->name('dropshipper.reject');
        Route::get('/dropshipper/login/{id}', 'login')->name('dropshipper.login');
        Route::post('/dropshipper/payment_modal', 'payment_modal')->name('dropshipper.payment_modal');
        Route::post('/dropshipper/profile_modal', 'profile_modal')->name('dropshipper.profile_modal');
        Route::post('/dropshipper/approved', 'updateApproved')->name('dropshipper.approved');
    });


    // dropshipper admin by suman

    Route::controller(CustomerController::class)->group(function () {
        Route::get('customers_ban/{customer}', 'ban')->name('customers.ban');
        Route::get('/customers/login/{id}', 'login')->name('customers.login');
        Route::get('/customers/destroy/{id}', 'destroy')->name('customers.destroy');
        Route::post('/bulk-customer-delete', 'bulk_customer_delete')->name('bulk-customer-delete');
        Route::get('/customers/download', 'exportCustomer')->name('customers.download');
    });
    // Customer
    Route::resource('customers', CustomerController::class);
    // Newsletter
    Route::controller(NewsletterController::class)->group(function () {
        Route::get('/newsletter', 'index')->name('newsletters.index');
        Route::post('/newsletter/send', 'send')->name('newsletters.send');
        Route::post('/newsletter/test/smtp', 'testEmail')->name('test.smtp');
    });

    Route::resource('profile', ProfileController::class);

    // Business Settings
    Route::controller(BusinessSettingsController::class)->group(function () {
        Route::post('/business-settings/update', 'update')->name('business_settings.update');
        Route::post('/business-settings/update/activation', 'updateActivationSettings')->name('business_settings.update.activation');
        Route::get('/general-setting', 'general_setting')->name('general_setting.index');
        Route::get('/activation', 'activation')->name('activation.index');
        Route::get('/payment-method', 'payment_method')->name('payment_method.index');
        Route::get('/file_system', 'file_system')->name('file_system.index');
        Route::get('/social-login', 'social_login')->name('social_login.index');
        Route::get('/smtp-settings', 'smtp_settings')->name('smtp_settings.index');
        Route::get('/google-analytics', 'google_analytics')->name('google_analytics.index');
        Route::get('/google-recaptcha', 'google_recaptcha')->name('google_recaptcha.index');
        Route::get('/google-map', 'google_map')->name('google-map.index');
        Route::get('/google-firebase', 'google_firebase')->name('google-firebase.index');

        //Facebook Settings
        Route::get('/facebook-chat', 'facebook_chat')->name('facebook_chat.index');
        Route::post('/facebook_chat', 'facebook_chat_update')->name('facebook_chat.update');
        Route::get('/facebook-comment', 'facebook_comment')->name('facebook-comment');
        Route::post('/facebook-comment', 'facebook_comment_update')->name('facebook-comment.update');
        Route::post('/facebook_pixel', 'facebook_pixel_update')->name('facebook_pixel.update');

        Route::post('/env_key_update', 'env_key_update')->name('env_key_update.update');
        Route::post('/payment_method_update', 'payment_method_update')->name('payment_method.update');
        Route::post('/google_analytics', 'google_analytics_update')->name('google_analytics.update');
        Route::post('/google_recaptcha', 'google_recaptcha_update')->name('google_recaptcha.update');
        Route::post('/google-map', 'google_map_update')->name('google-map.update');
        Route::post('/google-firebase', 'google_firebase_update')->name('google-firebase.update');

        Route::get('/verification/form', 'seller_verification_form')->name('seller_verification_form.index');
        Route::post('/verification/form', 'seller_verification_form_update')->name('seller_verification_form.update');
        Route::get('/vendor_commission', 'vendor_commission')->name('business_settings.vendor_commission');
        Route::post('/vendor_commission_update', 'vendor_commission_update')->name('business_settings.vendor_commission.update');

        //Shipping Configuration
        Route::get('/shipping_configuration', 'shipping_configuration')->name('shipping_configuration.index');
        Route::post('/shipping_configuration/update', 'shipping_configuration_update')->name('shipping_configuration.update');

        // Order Configuration
        Route::get('/order-configuration', 'order_configuration')->name('order_configuration.index');
    });


    //Currency
    Route::controller(CurrencyController::class)->group(function () {
        Route::get('/currency', 'currency')->name('currency.index');
        Route::post('/currency/update', 'updateCurrency')->name('currency.update');
        Route::post('/your-currency/update', 'updateYourCurrency')->name('your_currency.update');
        Route::get('/currency/create', 'create')->name('currency.create');
        Route::post('/currency/store', 'store')->name('currency.store');
        Route::post('/currency/currency_edit', 'edit')->name('currency.edit');
        Route::post('/currency/update_status', 'update_status')->name('currency.update_status');
    });

    //Tax
    Route::resource('tax', TaxController::class);
    Route::controller(TaxController::class)->group(function () {
        Route::get('/tax/edit/{id}', 'edit')->name('tax.edit');
        Route::get('/tax/destroy/{id}', 'destroy')->name('tax.destroy');
        Route::post('tax-status', 'change_tax_status')->name('taxes.tax-status');
    });

    // Language
    Route::resource('/languages', LanguageController::class);
    Route::controller(LanguageController::class)->group(function () {
        Route::post('/languages/{id}/update', 'update')->name('languages.update');
        Route::get('/languages/destroy/{id}', 'destroy')->name('languages.destroy');
        Route::post('/languages/update_rtl_status', 'update_rtl_status')->name('languages.update_rtl_status');
        Route::post('/languages/update-status', 'update_status')->name('languages.update-status');
        Route::post('/languages/key_value_store', 'key_value_store')->name('languages.key_value_store');

        //App Trasnlation
        Route::post('/languages/app-translations/import', 'importEnglishFile')->name('app-translations.import');
        Route::get('/languages/app-translations/show/{id}', 'showAppTranlsationView')->name('app-translations.show');
        Route::post('/languages/app-translations/key_value_store', 'storeAppTranlsation')->name('app-translations.store');
        Route::get('/languages/app-translations/export/{id}', 'exportARBFile')->name('app-translations.export');
    });


    // website setting
    Route::group(['prefix' => 'website'], function () {
        Route::controller(WebsiteController::class)->group(function () {
            Route::get('/footer', 'footer')->name('website.footer');
            Route::get('/header', 'header')->name('website.header');
            Route::get('/appearance', 'appearance')->name('website.appearance');
            Route::get('/pages', 'pages')->name('website.pages');
        });

        // Custom Page
        Route::resource('custom-pages', PageController::class);
        Route::controller(PageController::class)->group(function () {
            Route::get('/custom-pages/edit/{id}', 'edit')->name('custom-pages.edit');
            Route::get('/custom-pages/destroy/{id}', 'destroy')->name('custom-pages.destroy');
        });
    });

    // Staff Roles
    Route::resource('roles', RoleController::class);
    Route::controller(RoleController::class)->group(function () {
        Route::get('/roles/edit/{id}', 'edit')->name('roles.edit');
        Route::get('/roles/destroy/{id}', 'destroy')->name('roles.destroy');

        // Add Permissiom
        Route::post('/roles/add_permission', 'add_permission')->name('roles.permission');
    });

    // Staff
    Route::resource('staffs', StaffController::class);
    Route::get('/staffs/destroy/{id}', [StaffController::class, 'destroy'])->name('staffs.destroy');


    Route::controller(StaffController::class)->group(function () {
        Route::get('/audit-ip-info', 'showAudittrail')->name('auditIPInfo');
        Route::get('/suspect-info', 'showSuspectInfo')->name('suspectInfo');
    });
    Route::controller(ProductsByCategorySectionController::class)->group(function () {
        Route::get('/product_by_categories', 'index')->name('product_by_categories.index');
        Route::get('/product_by_categories/create', 'create')->name('product_by_categories.create');
        Route::get('/product_by_categories/create', 'create')->name('product_by_categories.create');
        Route::post('/product_by_categories/store', 'store')->name('product_by_categories.store');
        Route::get('/product_by_categories/edit/{id}', 'edit')->name('product_by_categories.edit');
        Route::post('/product_by_categories/update/{id}', 'update')->name('product_by_categories.update');
        Route::get('/product_by_categories/destroy/{id}', 'destroy')->name('product_by_categories.destroy');
        Route::post('/product_by_categories/update_status', 'updateStatus')->name('product_by_categories.update_status');
    });

    // Flash Deal
    Route::resource('flash_deals', FlashDealController::class);
    Route::controller(FlashDealController::class)->group(function () {
        Route::get('/flash_deals/edit/{id}', 'edit')->name('flash_deals.edit');
        Route::get('/flash_deals/destroy/{id}', 'destroy')->name('flash_deals.destroy');
        Route::post('/flash_deals/update_status', 'update_status')->name('flash_deals.update_status');
        Route::post('/flash_deals/update_featured', 'update_featured')->name('flash_deals.update_featured');
        Route::post('/flash_deals/product_discount', 'product_discount')->name('flash_deals.product_discount');
        Route::post('/flash_deals/product_discount_edit', 'product_discount_edit')->name('flash_deals.product_discount_edit');
    });

    //Subscribers
    Route::controller(SubscriberController::class)->group(function () {
        Route::get('/subscribers', 'index')->name('subscribers.index');
        Route::get('/subscribers/destroy/{id}', 'destroy')->name('subscriber.destroy');
    });

    Route::controller(ExportController::class)->group(function () {
        Route::get('orders/export', 'exportOrder')->name('orders.export');
        // Route::get('/customers/download','exportCustomer')->name('customers.download');
    });
    // Order
    Route::resource('orders', OrderController::class);
    Route::controller(OrderController::class)->group(function () {
        // All Orders
        Route::get('/all_orders', 'all_orders')->name('all_orders.index');
        Route::get('/dropshipper_all_orders', 'dropshipper_all_orders')->name('all_orders.index');
        Route::get('/inhouse-orders', 'all_orders')->name('inhouse_orders.index');
        Route::get('/seller_orders', 'all_orders')->name('seller_orders.index');
        Route::get('orders_by_pickup_point', 'all_orders')->name('pick_up_point.index');

        Route::get('/orders/{id}/show', 'show')->name('all_orders.show');
        Route::get('/inhouse-orders/{id}/show', 'show')->name('inhouse_orders.show');
        Route::get('/seller_orders/{id}/show', 'show')->name('seller_orders.show');
        Route::get('/orders_by_pickup_point/{id}/show', 'show')->name('pick_up_point.order_show');

        Route::post('/bulk-order-status', 'bulk_order_status')->name('bulk-order-status');

        Route::get('/orders/destroy/{id}', 'destroy')->name('orders.destroy');
        Route::post('/bulk-order-delete', 'bulk_order_delete')->name('bulk-order-delete');

        Route::get('/orders/destroy/{id}', 'destroy')->name('orders.destroy');
        Route::post('/orders/details', 'order_details')->name('orders.details');
        Route::post('/orders/update_delivery_status', 'update_delivery_status')->name('orders.update_delivery_status');
        Route::post('/orders/returned_after_delivery_status', 'returned_after_delivery_status')->name('orders.returned_after_delivery_status');
        Route::post('/orders/update_payment_status', 'update_payment_status')->name('orders.update_payment_status');
        Route::post('/orders/update_tracking_code', 'update_tracking_code')->name('orders.update_tracking_code');

        //Delivery Boy Assign
        Route::post('/orders/delivery-boy-assign', 'assign_delivery_boy')->name('orders.delivery-boy-assign');
        Route::get('/export/paid-payment-order-details', 'downloadPaidPaymentOrderDetails')->name('export.paid_payment_order_details');
    });


    Route::post('/pay_to_seller', [CommissionController::class, 'pay_to_seller'])->name('commissions.pay_to_seller');

    //Reports
    Route::controller(ReportController::class)->group(function () {
        Route::get('/in_house_sale_report', 'in_house_sale_report')->name('in_house_sale_report.index');
        Route::get('/seller_sale_report', 'seller_sale_report')->name('seller_sale_report.index');
        Route::get('/stock_report', 'stock_report')->name('stock_report.index');
        Route::get('/wish_report', 'wish_report')->name('wish_report.index');
        Route::get('/user_search_report', 'user_search_report')->name('user_search_report.index');
        Route::get('/commission-log', 'commission_history')->name('commission-log.index');
        Route::get('/wallet-history', 'wallet_transaction_history')->name('wallet-history.index');
    });

    //Blog Section
    //Blog cateory
    Route::resource('blog-category', BlogCategoryController::class);
    Route::get('/blog-category/destroy/{id}', [BlogCategoryController::class, 'destroy'])->name('blog-category.destroy');

    // Blog
    Route::resource('blog', BlogController::class);
    Route::controller(BlogController::class)->group(function () {
        Route::get('/blog/destroy/{id}', 'destroy')->name('blog.destroy');
        Route::post('/blog/change-status', 'change_status')->name('blog.change-status');
    });

    //Coupons
    Route::resource('coupon', CouponController::class);
    Route::controller(CouponController::class)->group(function () {
        Route::get('/coupon/destroy/{id}', 'destroy')->name('coupon.destroy');

        //Coupon Form
        Route::post('/coupon/get_form', 'get_coupon_form')->name('coupon.get_coupon_form');
        Route::post('/coupon/get_form_edit', 'get_coupon_form_edit')->name('coupon.get_coupon_form_edit');
    });

    //Reviews
    Route::controller(ReviewController::class)->group(function () {
        Route::get('/reviews', 'index')->name('reviews.index');
        Route::post('/reviews/published', 'updatePublished')->name('reviews.published');
    });

    //Support_Ticket
    Route::controller(SupportTicketController::class)->group(function () {
        Route::get('support_ticket/', 'admin_index')->name('support_ticket.admin_index');
        Route::get('support_ticket/{id}/show', 'admin_show')->name('support_ticket.admin_show');
        Route::post('support_ticket/reply', 'admin_store')->name('support_ticket.admin_store');
    });

    // Ticket Categories
    Route::resource('ticket-categories', \App\Http\Controllers\Admin\TicketCategoryController::class);
    Route::post('/ticket-categories/update-status', [\App\Http\Controllers\Admin\TicketCategoryController::class, 'updateStatus'])->name('ticket-categories.update-status');

    //Pickup_Points
    Route::resource('pick_up_points', PickupPointController::class);
    Route::controller(PickupPointController::class)->group(function () {
        Route::get('/pick_up_points/edit/{id}', 'edit')->name('pick_up_points.edit');
        Route::get('/pick_up_points/destroy/{id}', 'destroy')->name('pick_up_points.destroy');
    });

    //conversation of seller customer
    Route::controller(ConversationController::class)->group(function () {
        Route::get('conversations', 'admin_index')->name('conversations.admin_index');
        Route::get('conversations/{id}/show', 'admin_show')->name('conversations.admin_show');
    });

    // product Queries show on Admin panel
    Route::controller(ProductQueryController::class)->group(function () {
        Route::get('/product-queries', 'index')->name('product_query.index');
        Route::get('/product-queries/{id}', 'show')->name('product_query.show');
        Route::put('/product-queries/{id}', 'reply')->name('product_query.reply');
    });

    // Product Attribute
    Route::resource('attributes', AttributeController::class);
    Route::controller(AttributeController::class)->group(function () {
        Route::get('/attributes/edit/{id}', 'edit')->name('attributes.edit');
        Route::get('/attributes/destroy/{id}', 'destroy')->name('attributes.destroy');

        //Attribute Value
        Route::post('/store-attribute-value', 'store_attribute_value')->name('store-attribute-value');
        Route::get('/edit-attribute-value/{id}', 'edit_attribute_value')->name('edit-attribute-value');
        Route::post('/update-attribute-value/{id}', 'update_attribute_value')->name('update-attribute-value');
        Route::get('/destroy-attribute-value/{id}', 'destroy_attribute_value')->name('destroy-attribute-value');

        //Colors
        Route::get('/colors', 'colors')->name('colors');
        Route::post('/colors/store', 'store_color')->name('colors.store');
        Route::get('/colors/edit/{id}', 'edit_color')->name('colors.edit');
        Route::post('/colors/update/{id}', 'update_color')->name('colors.update');
        Route::get('/colors/destroy/{id}', 'destroy_color')->name('colors.destroy');
    });

    // Addon
    Route::resource('addons', AddonController::class);
    Route::post('/addons/activation', [AddonController::class, 'activation'])->name('addons.activation');

    //Customer Package
    Route::resource('customer_packages', CustomerPackageController::class);
    Route::controller(CustomerPackageController::class)->group(function () {
        Route::get('/customer_packages/edit/{id}', 'edit')->name('customer_packages.edit');
        Route::get('/customer_packages/destroy/{id}', 'destroy')->name('customer_packages.destroy');
    });

    //Classified Products
    Route::controller(CustomerProductController::class)->group(function () {
        Route::get('/classified_products', 'customer_product_index')->name('classified_products');
        Route::post('/classified_products/published', 'updatePublished')->name('classified_products.published');
        Route::get('/classified_products/destroy/{id}', 'destroy_by_admin')->name('classified_products.destroy');
    });

    // Countries
    Route::resource('countries', CountryController::class);
    Route::post('/countries/status', [CountryController::class, 'updateStatus'])->name('countries.status');

    // States
    Route::resource('states', StateController::class);
    Route::post('/states/status', [StateController::class, 'updateStatus'])->name('states.status');

    // Carriers
    Route::resource('carriers', CarrierController::class);
    Route::controller(CarrierController::class)->group(function () {
        Route::get('/carriers/destroy/{id}', 'destroy')->name('carriers.destroy');
        Route::post('/carriers/update_status', 'updateStatus')->name('carriers.update_status');
    });


    // Zones
    Route::resource('zones', ZoneController::class);
    Route::get('/zones/destroy/{id}', [ZoneController::class, 'destroy'])->name('zones.destroy');

    Route::resource('cities', CityController::class);
    Route::controller(CityController::class)->group(function () {
        Route::get('/cities/edit/{id}', 'edit')->name('cities.edit');
        Route::get('/cities/destroy/{id}', 'destroy')->name('cities.destroy');
        Route::post('/cities/status', 'updateStatus')->name('cities.status');
    });


    Route::view('/system/update', 'backend.system.update')->name('system_update');
    Route::view('/system/server-status', 'backend.system.server_status')->name('system_server');

    // uploaded files
    Route::resource('/uploaded-files', AizUploadController::class);
    Route::controller(AizUploadController::class)->group(function () {
        Route::any('/uploaded-files/file-info', 'file_info')->name('uploaded-files.info');
        Route::get('/uploaded-files/destroy/{id}', 'destroy')->name('uploaded-files.destroy');
        Route::post('/bulk-uploaded-files-delete', 'bulk_uploaded_files_delete')->name('bulk-uploaded-files-delete');
        Route::get('/all-file', 'all_file');
    });

    Route::get('/all-notification', [NotificationController::class, 'index'])->name('admin.all-notification');

    // Enhanced Notification Management Routes
    Route::group(['prefix' => 'notifications', 'as' => 'admin.notifications.'], function () {
        Route::get('/admin-notifications', [NotificationController::class, 'adminIndex'])->name('manage-notifications');
        Route::post('/bulk-send', [NotificationController::class, 'sendBulkNotification'])->name('bulk_send');
        Route::delete('/bulk-delete', [NotificationController::class, 'bulkDelete'])->name('bulk_delete');
        Route::get('/stats', [NotificationController::class, 'getStats'])->name('stats');
        Route::get('/export', [NotificationController::class, 'exportNotifications'])->name('export');
        Route::get('/users', [NotificationController::class, 'getUsers'])->name('users');
        Route::patch('/{id}/read', [NotificationController::class, 'markAsRead'])->name('mark_read');
        Route::delete('/{id}', [NotificationController::class, 'deleteNotification'])->name('delete');

        // Settings
        Route::get('/settings', function() {
            return view('backend.notification.settings');
        })->name('settings');

        // Dashboard
        Route::get('/dashboard', function() {
            return view('backend.notification.dashboard', [
                'stats' => [],
                'chartData' => [],
                'typeData' => []
            ]);
        })->name('dashboard');
    });

    Route::get('/clear-cache', [AdminController::class, 'clearCache'])->name('cache.clear');

    Route::get('/admin-permissions', [RoleController::class, 'create_admin_permissions']);

    // added by arif

    // Supplier routes

    Route::get('/suppliers', [SupplierController::class, 'index'])->name('suppliers.index');
    Route::get('/suppliers/create', [SupplierController::class, 'create'])->name('suppliers.create');
    Route::post('/suppliers', [SupplierController::class, 'store'])->name('suppliers.store');
    Route::get('/suppliers/{supplier}', [SupplierController::class, 'show'])->name('suppliers.show');
    Route::get('/suppliers/{supplier}/edit', [SupplierController::class, 'edit'])->name('suppliers.edit');
    Route::put('/suppliers/{supplier}', [SupplierController::class, 'update'])->name('suppliers.update');
    Route::delete('/suppliers/{supplier}', [SupplierController::class, 'destroy'])->name('suppliers.destroy');
    // end by arif
    //Offers
    Route::resource('offers', OfferController::class);
    Route::post('/offers/destroy/{id}', [OfferController::class, 'destroy'])->name('offers.destroy.post');
    Route::post('bulk-offer-delete', [OfferController::class, 'bulk_offer_delete'])->name('bulk-offer-delete');
    Route::post('offers/update-status', [OfferController::class, 'updateStatus'])->name('offers.update_status');
    // Add a new direct route for deletion
    Route::get('/offers/delete/{id}', [OfferController::class, 'destroy'])->name('offers.delete');
    // Wallet Management
    Route::group(['prefix' => 'wallet', 'middleware' => ['admin'], 'as' => 'wallet.'], function () {
        Route::get('/', [App\Http\Controllers\Admin\WalletController::class, 'index'])->name('index');
        Route::get('/details/{id}', [App\Http\Controllers\Admin\WalletController::class, 'details'])->name('details');
        Route::get('/transactions', [App\Http\Controllers\Admin\WalletController::class, 'transactions'])->name('transactions');

        Route::get('/add-funds/{id}', [App\Http\Controllers\Admin\WalletController::class, 'showAddFunds'])->name('add_funds_form');
        Route::post('/add-funds/{id}', [App\Http\Controllers\Admin\WalletController::class, 'addFunds'])->name('add_funds');
        Route::post('/add-funds-store', [App\Http\Controllers\Admin\WalletController::class, 'addFundsStore'])->name('add_funds_store');

        Route::get('/deduct-funds/{id}', [App\Http\Controllers\Admin\WalletController::class, 'showDeductFunds'])->name('deduct_funds_form');
        Route::post('/deduct-funds/{id}', [App\Http\Controllers\Admin\WalletController::class, 'deductFunds'])->name('deduct_funds');
        Route::post('/deduct-funds-store', [App\Http\Controllers\Admin\WalletController::class, 'deductFundsStore'])->name('deduct_funds_store');

        Route::get('/pending-transactions', [App\Http\Controllers\Admin\WalletController::class, 'pendingTransactions'])->name('pending_transactions');
        Route::post('/transaction-approval/{id}', [App\Http\Controllers\Admin\WalletController::class, 'approveTransaction'])->name('approve_transaction');
        Route::post('/transaction-rejection/{id}', [App\Http\Controllers\Admin\WalletController::class, 'rejectTransaction'])->name('reject_transaction');

        Route::get('/exchange-rates', [App\Http\Controllers\Admin\WalletController::class, 'exchangeRates'])->name('exchange_rates');
        Route::post('/exchange-rates/store', [App\Http\Controllers\Admin\WalletController::class, 'createExchangeRate'])->name('exchange_rates.store');
        Route::get('/exchange-rates/edit/{id}', [App\Http\Controllers\Admin\WalletController::class, 'editExchangeRateForm'])->name('exchange_rates.edit');
        Route::post('/exchange-rates/edit/{id}', [App\Http\Controllers\Admin\WalletController::class, 'updateExchangeRate'])->name('exchange_rates.update');
        Route::delete('/exchange-rates/delete/{id}', [App\Http\Controllers\Admin\WalletController::class, 'deleteExchangeRate'])->name('exchange_rates.destroy');
        Route::post('/exchange-rates/update-status', [App\Http\Controllers\Admin\WalletController::class, 'updateExchangeRateStatus'])->name('exchange_rates.update_status');

        Route::get('/statistics', [App\Http\Controllers\Admin\WalletController::class, 'statistics'])->name('statistics');

        // Missing route for transaction details
        Route::get('/transaction-details/{id}', [App\Http\Controllers\Admin\WalletController::class, 'transactionDetails'])->name('transaction_details');
    });

    // Admin Messaging System Routes
    Route::group(['prefix' => 'messaging', 'as' => 'admin.messaging.'], function () {
        // Thread management
        Route::get('/threads', [App\Http\Controllers\Admin\MessagingController::class, 'threads'])->name('threads.index');
        Route::get('/threads/{id}', [App\Http\Controllers\Admin\MessagingController::class, 'showThread'])->name('threads.show');
        Route::post('/threads/{id}/reply', [App\Http\Controllers\Admin\MessagingController::class, 'replyToThread'])->name('threads.reply');
        Route::post('/threads/create', [App\Http\Controllers\Admin\MessagingController::class, 'createThread'])->name('threads.create');
        Route::post('/threads/{id}/close', [App\Http\Controllers\Admin\MessagingController::class, 'closeThread'])->name('threads.close');
        Route::post('/threads/{id}/reopen', [App\Http\Controllers\Admin\MessagingController::class, 'reopenThread'])->name('threads.reopen');

        // Conversation management
        Route::get('/conversations', [App\Http\Controllers\Admin\MessagingController::class, 'conversations'])->name('conversations.index');
        Route::get('/conversations/{id}', [App\Http\Controllers\Admin\MessagingController::class, 'showConversation'])->name('conversations.show');
        Route::post('/conversations/{id}/send', [App\Http\Controllers\Admin\MessagingController::class, 'sendMessage'])->name('conversations.send');
        Route::post('/conversations/create', [App\Http\Controllers\Admin\MessagingController::class, 'createConversation'])->name('conversations.create');

        // User search for creating conversations/threads
        Route::get('/search-users', [App\Http\Controllers\Admin\MessagingController::class, 'searchUsers'])->name('search-users');
    });

    // Category Banners Management
    Route::group(['prefix' => 'category-banners', 'as' => 'admin.category-banners.'], function () {
        Route::get('/', [App\Http\Controllers\Admin\CategoryBannerController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\CategoryBannerController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\CategoryBannerController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [App\Http\Controllers\Admin\CategoryBannerController::class, 'edit'])->name('edit');
        Route::put('/{id}', [App\Http\Controllers\Admin\CategoryBannerController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\Admin\CategoryBannerController::class, 'destroy'])->name('destroy');
        Route::post('/update-order', [App\Http\Controllers\Admin\CategoryBannerController::class, 'updateOrder'])->name('update-order');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\Admin\CategoryBannerController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Price Alerts
    Route::group(['prefix' => '/price-alerts', 'as' => 'admin.price-alerts.'], function () {
        Route::get('/', [App\Http\Controllers\Admin\PriceAlertController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\PriceAlertController::class, 'create'])->name('create');
        Route::post('/store', [App\Http\Controllers\Admin\PriceAlertController::class, 'store'])->name('store');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Admin\PriceAlertController::class, 'destroy'])->name('destroy');
        Route::post('/check', [App\Http\Controllers\Admin\PriceAlertController::class, 'check'])->name('check');
    });

});
