<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('cart_info')) {
            Schema::table('cart_info', function (Blueprint $table) {
                if (!Schema::hasColumn('cart_info', 'temp_user_id')) {
                    $table->string('temp_user_id', 100)->nullable()->after('user_id');
                    $table->index('temp_user_id');
                }
                
                // Add address_id and shipping_method_id if they don't exist
                if (!Schema::hasColumn('cart_info', 'address_id')) {
                    $table->unsignedBigInteger('address_id')->nullable()->after('temp_user_id');
                }
                
                if (!Schema::hasColumn('cart_info', 'shipping_method_id')) {
                    $table->unsignedBigInteger('shipping_method_id')->nullable()->after('address_id');
                }
            });
        }
        
        if (Schema::hasTable('carts')) {
            Schema::table('carts', function (Blueprint $table) {
                if (!Schema::hasColumn('carts', 'temp_user_id')) {
                    $table->string('temp_user_id', 100)->nullable()->after('user_id');
                    $table->index('temp_user_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('cart_info')) {
            Schema::table('cart_info', function (Blueprint $table) {
                if (Schema::hasColumn('cart_info', 'temp_user_id')) {
                    $table->dropIndex(['temp_user_id']);
                    $table->dropColumn('temp_user_id');
                }
            });
        }
        
        if (Schema::hasTable('carts')) {
            Schema::table('carts', function (Blueprint $table) {
                if (Schema::hasColumn('carts', 'temp_user_id')) {
                    $table->dropIndex(['temp_user_id']);
                    $table->dropColumn('temp_user_id');
                }
            });
        }
    }
}; 