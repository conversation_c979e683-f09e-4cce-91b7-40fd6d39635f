<?php

namespace Tests\Feature;

use App\Models\EmailVerify;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class ForgotPasswordIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    /** @test */
    public function it_can_request_password_reset_with_valid_email()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword')
        ]);

        // Request password reset
        $response = $this->postJson('/api/v3/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password reset code sent successfully to your email'
                ])
                ->assertJsonStructure([
                    'data' => [
                        'email',
                        'otp_sent',
                        'expires_at',
                        'expires_in_minutes',
                        'resend_count',
                        'max_resend_attempts',
                        'next_resend_available_at'
                    ]
                ]);

        // Verify OTP record was created
        $this->assertDatabaseHas('email_verify', [
            'email' => '<EMAIL>',
            'is_verify' => false
        ]);
    }

    /** @test */
    public function it_rejects_password_reset_for_non_existent_email()
    {
        $response = $this->postJson('/api/v3/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false
                ]);
    }

    /** @test */
    public function it_can_reset_password_with_valid_otp()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword')
        ]);

        // Create OTP record
        $emailVerifyRecord = EmailVerify::generateOTP('<EMAIL>', 10);

        // Reset password
        $response = $this->postJson('/api/v3/auth/reset-password', [
            'email' => '<EMAIL>',
            'otp' => $emailVerifyRecord->otp,
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password reset successfully'
                ]);

        // Verify password was changed
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));

        // Verify OTP record was cleaned up
        $this->assertDatabaseMissing('email_verify', [
            'email' => '<EMAIL>',
            'is_verify' => true
        ]);
    }

    /** @test */
    public function it_rejects_password_reset_with_invalid_otp()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword')
        ]);

        // Create OTP record
        EmailVerify::generateOTP('<EMAIL>', 10);

        // Try to reset with wrong OTP
        $response = $this->postJson('/api/v3/auth/reset-password', [
            'email' => '<EMAIL>',
            'otp' => '999999',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'message' => 'The verification code is incorrect. Please check and try again.'
                    ]
                ]);

        // Verify password was not changed
        $user->refresh();
        $this->assertTrue(Hash::check('oldpassword', $user->password));
    }

    /** @test */
    public function it_enforces_resend_cooldown_for_password_reset()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Request password reset
        $this->postJson('/api/v3/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        // Try to request again immediately
        $response = $this->postJson('/api/v3/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'message' => 'Resend cooldown active'
                    ]
                ]);
    }

    /** @test */
    public function it_can_resend_password_reset_otp()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create an expired OTP record to simulate cooldown period passed
        $emailVerifyRecord = EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'generate_date' => now()->subMinutes(10),
            'expire_time' => now()->subMinutes(5),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 1,
            'last_resend_at' => now()->subMinutes(10)
        ]);

        // Resend OTP
        $response = $this->postJson('/api/v3/auth/resend-reset-otp', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password reset code resent successfully'
                ]);

        // Verify resend count was incremented
        $emailVerifyRecord->refresh();
        $this->assertEquals(2, $emailVerifyRecord->resend_count);
    }

    /** @test */
    public function it_enforces_maximum_resend_attempts()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create OTP record with max resend attempts reached
        EmailVerify::create([
            'email' => '<EMAIL>',
            'otp' => '123456',
            'generate_date' => now()->subMinutes(10),
            'expire_time' => now()->addMinutes(5),
            'duration' => 10,
            'is_verify' => false,
            'resend_count' => 5,
            'last_resend_at' => now()->subMinutes(10)
        ]);

        // Try to resend
        $response = $this->postJson('/api/v3/auth/resend-reset-otp', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'message' => 'Maximum password reset attempts reached for this email. Please try again after 1 hour.'
                    ]
                ]);
    }
}