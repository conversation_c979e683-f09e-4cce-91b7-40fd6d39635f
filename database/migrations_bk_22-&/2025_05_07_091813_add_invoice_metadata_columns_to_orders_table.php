<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'invoice_view_count')) {
                $table->integer('invoice_view_count')->default(0)->after('viewed');
            }
            if (!Schema::hasColumn('orders', 'invoice_last_viewed')) {
                $table->timestamp('invoice_last_viewed')->nullable()->after('invoice_view_count');
            }
            if (!Schema::hasColumn('orders', 'invoice_download_count')) {
                $table->integer('invoice_download_count')->default(0)->after('invoice_last_viewed');
            }
            if (!Schema::hasColumn('orders', 'invoice_last_downloaded')) {
                $table->timestamp('invoice_last_downloaded')->nullable()->after('invoice_download_count');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('invoice_view_count');
            $table->dropColumn('invoice_last_viewed');
            $table->dropColumn('invoice_download_count');
            $table->dropColumn('invoice_last_downloaded');
        });
    }
};
