<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('videos', function (Blueprint $table) {
            if (!Schema::hasColumn('videos', 'subject_type')) {
                $table->string('subject_type', 191)->after('duration')->nullable();
            }
            if (!Schema::hasColumn('videos', 'subject_id')) {
                $table->unsignedBigInteger('subject_id')->after('subject_type')->nullable();
            }
            if (!Schema::hasColumn('videos', 'upload_id')) {
                $table->unsignedBigInteger('upload_id')->after('subject_id')->nullable();
            }
            if (!Schema::hasColumn('videos', 'insert_by')) {
                $table->integer('insert_by')->nullable()->after('upload_id');
            }
            if (!Schema::hasColumn('videos', 'update_by')) {
                $table->integer('update_by')->nullable()->after('insert_by');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('videos', function (Blueprint $table) {
            $table->dropColumn(['subject_type', 'subject_id', 'upload_id', 'insert_by', 'update_by']);
        });
    }
};
