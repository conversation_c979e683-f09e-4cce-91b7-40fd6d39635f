<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserTypeToDropshipperOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('dropshipper_offers')) {
        Schema::table('dropshipper_offers', function (Blueprint $table) {
            if (!Schema::hasColumn('dropshipper_offers', 'user_type')) {
                $table->enum('user_type', ['all', 'customer', 'seller', 'dropshipper'])->default('all')->after('is_dropshipper_only');
            }
            if (!Schema::hasColumn('offers', 'discount_type')) {
                $table->string('discount_type')->default('percentage')->after('description');
            }
            if (!Schema::hasColumn('offers', 'end_date')) {
                $table->renameColumn('expiration_date', 'end_date');
            }
            if (!Schema::hasColumn('offers', 'status')) {
                $table->string('status')->default('active')->after('is_expired');
            }
            if (!Schema::hasColumn('offers', 'created_by')) {
                $table->unsignedInteger('created_by')->nullable()->after('savings_amount');
            }
            if (!Schema::hasColumn('offers', 'updated_by')) {
                $table->unsignedInteger('updated_by')->nullable()->after('created_by');
            }
            if (!Schema::hasColumn('offers', 'terms_conditions')) {
                $table->text('terms_conditions')->nullable()->after('tags');
            }
            if (!Schema::hasColumn('offers', 'categories')) {
                $table->dropColumn('categories');
            }
            if (!Schema::hasColumn('offers', 'products')) {
                $table->dropColumn('products');
            }
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('offers', function (Blueprint $table) {
            $table->dropColumn('user_type');
            $table->dropColumn('discount_type');
            $table->renameColumn('end_date', 'expiration_date');
            $table->dropColumn('status');
            $table->dropColumn('created_by');
            $table->dropColumn('updated_by');
            $table->dropColumn('terms_conditions');

            // Add back the JSON columns
            $table->json('categories')->nullable();
            $table->json('products')->nullable();
        });
    }
}
