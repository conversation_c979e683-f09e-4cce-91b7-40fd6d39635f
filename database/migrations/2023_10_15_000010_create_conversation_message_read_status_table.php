<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('conversation_message_read_status', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id');
                //$table->unsignedInteger('user_id');
                $table->foreignId('message_id');
                $table->timestamps();
                $table->unique(['message_id', 'user_id']);
                //$table->foreign('message_id')->references('id')->on('conversation_messages')->onDelete('cascade');
                //$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('conversation_message_read_status');
    }
};
