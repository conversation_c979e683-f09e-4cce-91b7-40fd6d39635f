<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tickets', function (Blueprint $table) {
            // Drop the old status column first
            if (Schema::hasColumn('tickets', 'status')) {
                $table->dropColumn('status');
            }
        });
        Schema::table('tickets', function (Blueprint $table) {
            $table->enum('status', ['pending', 'open', 'solved', 'closed','closed_by_user','reopen'])->default('pending');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
