<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateAvailableIntegrationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //if (!Schema::hasTable('available_integrations')) {
            Schema::create('available_integrations', function (Blueprint $table) {
                $table->id();
                $table->string('type');
                $table->string('provider')->unique();
                $table->string('name');
                $table->text('description');
            $table->string('authType');
            $table->string('iconUrl')->nullable();
            $table->json('features')->nullable();
            $table->string('authUrl')->nullable();
            $table->string('documentationUrl')->nullable();
            $table->boolean('isPopular')->default(false);
            $table->boolean('isActive')->default(true);
            $table->integer('displayOrder')->default(0);
            $table->timestamps();

            $table->index('type');
            $table->index('authType');
            $table->index('isPopular');
            $table->index('isActive');
            $table->index('displayOrder');
            });
        //}

        // Seed with some common integrations
        $this->seedAvailableIntegrations();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('available_integrations');
    }

    /**
     * Seed the table with common integrations
     */
    private function seedAvailableIntegrations()
    {
        $integrations = [
            [
                'id' => '2e5a9f8a-1b3c-4d5e-9f8a-1b3c4d5e6f7a',
                'type' => 'payment',
                'provider' => 'stripe',
                'name' => 'Stripe',
                'description' => 'Accept payments online with Stripe payment gateway.',
                'authType' => 'oauth',
                'iconUrl' => '/assets/images/integrations/stripe.svg',
                'features' => json_encode(['payments', 'subscriptions', 'invoicing']),
                'authUrl' => 'https://connect.stripe.com/oauth/authorize',
                'documentationUrl' => 'https://stripe.com/docs',
                'isPopular' => true,
                'isActive' => true,
                'displayOrder' => 1,
            ],
            [
                'id' => '3f6b0e9b-2c4d-5e6f-0e9b-2c4d5e6f7a8b',
                'type' => 'payment',
                'provider' => 'paypal',
                'name' => 'PayPal',
                'description' => 'Accept payments using PayPal services.',
                'authType' => 'oauth',
                'iconUrl' => '/assets/images/integrations/paypal.svg',
                'features' => json_encode(['payments', 'invoicing', 'international']),
                'authUrl' => 'https://www.paypal.com/connect',
                'documentationUrl' => 'https://developer.paypal.com/docs',
                'isPopular' => true,
                'isActive' => true,
                'displayOrder' => 2,
            ],
            [
                'id' => '4g7c1f0c-3d5e-6f7g-1f0c-3d5e6f7g8a9b',
                'type' => 'shipping',
                'provider' => 'shippo',
                'name' => 'Shippo',
                'description' => 'Multi-carrier shipping API for e-commerce businesses.',
                'authType' => 'api_key',
                'iconUrl' => '/assets/images/integrations/shippo.svg',
                'features' => json_encode(['shipping_labels', 'tracking', 'rates_comparison']),
                'authUrl' => null,
                'documentationUrl' => 'https://goshippo.com/docs',
                'isPopular' => false,
                'isActive' => true,
                'displayOrder' => 3,
            ],
            [
                'id' => '5h8d2g1d-4e6f-7g8h-2g1d-4e6f7g8h9a0b',
                'type' => 'social_media',
                'provider' => 'facebook',
                'name' => 'Facebook',
                'description' => 'Integrate with Facebook for social media marketing and shop features.',
                'authType' => 'oauth',
                'iconUrl' => '/assets/images/integrations/facebook.svg',
                'features' => json_encode(['facebook_shop', 'social_login', 'social_sharing']),
                'authUrl' => 'https://www.facebook.com/v11.0/dialog/oauth',
                'documentationUrl' => 'https://developers.facebook.com/docs',
                'isPopular' => true,
                'isActive' => true,
                'displayOrder' => 4,
            ],
            [
                'id' => '6i9e3h2e-5f7g-8h9i-3h2e-5f7g8h9i0a1b',
                'type' => 'marketing',
                'provider' => 'mailchimp',
                'name' => 'Mailchimp',
                'description' => 'Email marketing, ads, landing pages, and CRM tools.',
                'authType' => 'oauth',
                'iconUrl' => '/assets/images/integrations/mailchimp.svg',
                'features' => json_encode(['email_marketing', 'automation', 'segmentation']),
                'authUrl' => 'https://login.mailchimp.com/oauth2/authorize',
                'documentationUrl' => 'https://mailchimp.com/developer',
                'isPopular' => true,
                'isActive' => true,
                'displayOrder' => 5,
            ],
        ];

        foreach ($integrations as $integration) {
            DB::table('available_integrations')->insert($integration);
        }
    }
}
