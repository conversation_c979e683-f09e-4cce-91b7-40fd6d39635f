<?php

namespace App\Providers;

use App\Services\EmailVerificationDatabaseService;
use Illuminate\Support\ServiceProvider;

class EmailVerificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(EmailVerificationDatabaseService::class, function ($app) {
            return new EmailVerificationDatabaseService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register any boot logic here if needed
    }
}