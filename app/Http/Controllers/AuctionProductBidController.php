<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AuctionProductBid;
use App\Models\Product;
use App\Models\User;
use App\Mail\AuctionBidMailManager;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class AuctionProductBidController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check for admin routes
        $this->middleware(['permission:view_auction_bids'])->only([
            'product_bids_admin'
        ]);
        $this->middleware(['permission:delete_auction_bids'])->only([
            'bid_destroy_admin'
        ]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $bids = AuctionProductBid::with(['user', 'product'])
            ->latest()
            ->paginate(15);
        
        return view('backend.auction_bids.index', compact('bids'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $products = Product::where('auction_product', 1)->get();
        return view('backend.auction_bids.create', compact('products'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'amount' => 'required|numeric|min:0'
        ]);

        $product = Product::where('id', $request->product_id)
            ->where('auction_product', 1)
            ->where('auction_start_date', '<=', strtotime("now"))
            ->where('auction_end_date', '>=', strtotime("now"))
            ->first();

        if (!$product) {
            if ($request->expectsJson()) {
                return response()->json([
                    'result' => false,
                    'message' => translate('Auction is not active for this product')
                ], 400);
            }
            flash(translate('Auction is not active for this product'))->error();
            return back();
        }

        // Check if bid amount is higher than current highest bid
        $highest_bid = AuctionProductBid::where('product_id', $request->product_id)
            ->max('amount');

        if ($highest_bid && $request->amount <= $highest_bid) {
            if ($request->expectsJson()) {
                return response()->json([
                    'result' => false,
                    'message' => translate('Your bid amount must be higher than the current highest bid')
                ], 400);
            }
            flash(translate('Your bid amount must be higher than the current highest bid'))->error();
            return back();
        }

        // Check if bid amount is higher than starting bid
        if ($request->amount < $product->starting_bid) {
            if ($request->expectsJson()) {
                return response()->json([
                    'result' => false,
                    'message' => translate('Your bid amount must be higher than the starting bid')
                ], 400);
            }
            flash(translate('Your bid amount must be higher than the starting bid'))->error();
            return back();
        }

        $bid = AuctionProductBid::where('product_id', $request->product_id)
            ->where('user_id', Auth::id())
            ->first();

        if ($bid == null) {
            $bid = new AuctionProductBid;
            $bid->user_id = Auth::id();
        }

        $bid->product_id = $request->product_id;
        $bid->amount = $request->amount;

        if ($bid->save()) {
            // Notify the second highest bidder
            $second_max_bid = AuctionProductBid::where('product_id', $request->product_id)
                ->orderBy('amount', 'desc')
                ->skip(1)
                ->first();

            if ($second_max_bid != null && $second_max_bid->user->email != null) {
                try {
                    $array['view'] = 'emails.auction_bid';
                    $array['subject'] = translate('Auction Bid');
                    $array['from'] = env('MAIL_FROM_ADDRESS');
                    $array['content'] = 'Hi! A new user bidded more than you for the product, ' . $product->name . '. ' . 'Highest bid amount: ' . $bid->amount;
                    $array['link'] = route('auction-product', $product->slug);
                    Mail::to($second_max_bid->user->email)->queue(new AuctionBidMailManager($array));
                } catch (\Exception $e) {
                    // Log error but don't break the flow
                }
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'result' => true,
                    'message' => translate('Bid placed successfully'),
                    'data' => $bid
                ], 200);
            }

            flash(translate('Bid placed successfully'))->success();
            return back();
        } else {
            if ($request->expectsJson()) {
                return response()->json([
                    'result' => false,
                    'message' => translate('Something went wrong')
                ], 500);
            }
            flash(translate('Something went wrong'))->error();
            return back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $bid = AuctionProductBid::with(['user', 'product'])->findOrFail($id);
        return view('backend.auction_bids.show', compact('bid'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $bid = AuctionProductBid::findOrFail($id);
        $products = Product::where('auction_product', 1)->get();
        return view('backend.auction_bids.edit', compact('bid', 'products'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0'
        ]);

        $bid = AuctionProductBid::findOrFail($id);
        $bid->amount = $request->amount;

        if ($bid->save()) {
            flash(translate('Bid has been updated successfully'))->success();
            return redirect()->route('auction_product_bids.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $bid = AuctionProductBid::findOrFail($id);

        if ($bid->delete()) {
            flash(translate('Bid has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    // Admin Methods

    /**
     * Show product bids for admin
     */
    public function product_bids_admin($id)
    {
        $product = Product::where('auction_product', 1)->findOrFail($id);
        $bids = AuctionProductBid::where('product_id', $id)
            ->with('user')
            ->orderBy('amount', 'desc')
            ->paginate(15);

        return view('backend.auction_bids.product_bids', compact('product', 'bids'));
    }

    /**
     * Delete bid for admin
     */
    public function bid_destroy_admin($id)
    {
        $bid = AuctionProductBid::findOrFail($id);

        if ($bid->delete()) {
            flash(translate('Bid has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    // Seller Methods

    /**
     * Show product bids for seller
     */
    public function product_bids_seller($id)
    {
        $product = Product::where('auction_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        
        $bids = AuctionProductBid::where('product_id', $id)
            ->with('user')
            ->orderBy('amount', 'desc')
            ->paginate(15);

        return view('seller.auction_bids.product_bids', compact('product', 'bids'));
    }

    /**
     * Delete bid for seller (only their own product bids)
     */
    public function bid_destroy_seller($id)
    {
        $bid = AuctionProductBid::findOrFail($id);
        
        // Check if the bid belongs to seller's product
        $product = Product::where('id', $bid->product_id)
            ->where('user_id', Auth::id())
            ->first();

        if (!$product) {
            flash(translate('Unauthorized action'))->error();
            return back();
        }

        if ($bid->delete()) {
            flash(translate('Bid has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }
} 