<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

            Schema::create('message_read_status', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id');
                $table->foreignId('user_id');
                //$table->foreignId('message_id')->references('id')->on('messages')->onDelete('cascade');
                //$table->unsignedInteger('user_id');
                $table->timestamps();
                $table->unique(['message_id', 'user_id']);
                //$table->foreign('message_id')->references('id')->on('messages')->onDelete('cascade');
                //$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });

    }

    public function down(): void
    {
        Schema::dropIfExists('message_read_status');
    }
};
