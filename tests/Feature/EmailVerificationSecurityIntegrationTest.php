<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\EmailVerificationSecurityService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;

class EmailVerificationSecurityIntegrationTest extends TestCase
{
    use WithFaker;

    protected $securityService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->securityService = app(EmailVerificationSecurityService::class);
    }

    /** @test */
    public function it_applies_rate_limiting_to_email_verification_endpoints()
    {
        $email = $this->faker->email;
        
        // Make multiple requests to trigger rate limiting
        $responses = [];
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
                'email' => $email,
                'user_type' => 'customer'
            ]);
            $responses[] = $response->getStatusCode();
        }
        
        // At least one request should be rate limited (429)
        $this->assertContains(429, $responses);
    }

    /** @test */
    public function it_validates_otp_format_in_verification_endpoint()
    {
        $email = $this->faker->email;
        
        // Test with invalid OTP format
        $response = $this->postJson('/api/v3/auth/verify-registration-otp', [
            'email' => $email,
            'otp' => '12345' // Too short
        ]);
        
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('validation', strtolower($response->json('message')));
    }

    /** @test */
    public function it_detects_bot_activity_in_requests()
    {
        $email = $this->faker->email;
        
        // Make request with bot-like user agent
        $response = $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email,
            'user_type' => 'customer'
        ], [
            'User-Agent' => 'curl/7.68.0'
        ]);
        
        // Should either be blocked or handled appropriately
        $this->assertNotNull($response->json());
    }

    /** @test */
    public function it_handles_security_service_integration()
    {
        // Test that security service methods work correctly
        $email = $this->faker->email;
        $ip = $this->faker->ipv4;
        
        // Test IP blocking
        $this->assertFalse($this->securityService->isIpBlocked($ip));
        $this->securityService->blockIp($ip, 60);
        $this->assertTrue($this->securityService->isIpBlocked($ip));
        
        // Test email blocking
        $this->assertFalse($this->securityService->isEmailBlocked($email));
        $this->securityService->blockEmail($email, 60);
        $this->assertTrue($this->securityService->isEmailBlocked($email));
        
        // Test failed attempt tracking
        $this->securityService->trackFailedAttempt($email, $ip);
        $this->assertTrue(Cache::has("failed_otp:{$email}"));
        
        // Test clearing attempts
        $this->securityService->clearFailedAttempts($email, $ip);
        $this->assertFalse(Cache::has("failed_otp:{$email}"));
    }

    /** @test */
    public function it_provides_consistent_response_times()
    {
        // This test ensures timing attack protection is working
        $email1 = $this->faker->email;
        $email2 = $this->faker->email;
        
        $times = [];
        
        // Test with first email
        $start = microtime(true);
        $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email1,
            'user_type' => 'customer'
        ]);
        $times[] = microtime(true) - $start;
        
        // Test with second email
        $start = microtime(true);
        $this->postJson('/api/v3/auth/check-email-send-otp', [
            'email' => $email2,
            'user_type' => 'customer'
        ]);
        $times[] = microtime(true) - $start;
        
        // Response times should be reasonably consistent
        $timeDifference = abs($times[0] - $times[1]);
        $this->assertLessThan(1.0, $timeDifference); // Within 1 second
    }

    /** @test */
    public function it_generates_secure_tokens_consistently()
    {
        $tokens = [];
        
        // Generate multiple tokens
        for ($i = 0; $i < 5; $i++) {
            $token = $this->securityService->generateSecureToken();
            $tokens[] = $token;
            
            // Each token should be valid
            $this->assertTrue($this->securityService->validateTokenSecurity($token));
        }
        
        // All tokens should be unique
        $this->assertEquals(count($tokens), count(array_unique($tokens)));
    }

    /** @test */
    public function it_logs_security_events_properly()
    {
        // Test that security logging doesn't throw exceptions
        $this->securityService->logSecurityEvent('test_integration_event', [
            'test_data' => 'integration_test',
            'timestamp' => now()->toISOString()
        ]);
        
        // If we reach here, logging worked without errors
        $this->assertTrue(true);
    }

    /** @test */
    public function it_handles_cleanup_operations()
    {
        // Test cleanup statistics
        $stats = $this->securityService->getSecurityStatistics();
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('rate_limits', $stats);
        
        // Test cleanup operations (should not throw exceptions)
        $cleanupResults = $this->securityService->cleanupExpiredRecords();
        $this->assertIsArray($cleanupResults);
    }
}