<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMessagingTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, create all base tables without foreign keys
        if (!Schema::hasTable('message_threads')) {
            Schema::create('message_threads', function (Blueprint $table) {
                $table->id();
                $table->string('subject')->nullable();
                $table->boolean('is_archived')->default(false);
                $table->boolean('is_closed')->default(false);
                $table->timestamps();
                $table->softDeletes();
            });
        }

        if (!Schema::hasTable('conversations')) {
            Schema::create('conversations', function (Blueprint $table) {
                $table->id();
                $table->string('title')->nullable();
                $table->string('type')->default('private');
                $table->boolean('is_archived')->default(false);
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Then create tables with foreign keys
        if (!Schema::hasTable('thread_participants')) {
            Schema::create('thread_participants', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('thread_id');
                $table->unsignedInteger('user_id');
                $table->string('role')->default('member');
                $table->timestamps();
                $table->softDeletes();
                $table->unique(['thread_id', 'user_id']);
                $table->foreign('thread_id')->references('id')->on('message_threads')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('messages')) {
            Schema::create('messages', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('thread_id');
                $table->unsignedInteger('sender_id');
                $table->text('content');
                $table->timestamps();
                $table->softDeletes();
                $table->foreign('thread_id')->references('id')->on('message_threads')->onDelete('cascade');
                $table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('message_attachments')) {
            Schema::create('message_attachments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id')->references('id')->on('messages')->onDelete('cascade');
                $table->string('name');
                $table->string('url');
                $table->string('type');
                $table->unsignedInteger('size');
                $table->timestamps();
                $table->softDeletes();
                $table->foreign('message_id')->references('id')->on('messages')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('message_read_status')) {
            Schema::create('message_read_status', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id')->references('id')->on('messages')->onDelete('cascade');
                $table->unsignedInteger('user_id');
                $table->timestamps();
                $table->unique(['message_id', 'user_id']);
                $table->foreign('message_id')->references('id')->on('messages')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('conversation_participants')) {
            Schema::create('conversation_participants', function (Blueprint $table) {
                $table->id();
                $table->foreignId('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                $table->unsignedInteger('user_id');
                $table->string('role')->default('member');
                $table->timestamps();
                $table->softDeletes();
                $table->unique(['conversation_id', 'user_id']);
                $table->foreign('conversation_id')->on('conversations')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('conversation_messages')) {
            Schema::create('conversation_messages', function (Blueprint $table) {
                $table->id();
                $table->foreignId('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                $table->unsignedInteger('sender_id');
                $table->text('content');
                $table->timestamps();
                $table->softDeletes();
                $table->foreign('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
                $table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('conversation_message_attachments')) {
            Schema::create('conversation_message_attachments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('message_id')->references('id')->on('conversation_messages')->onDelete('cascade');
                $table->string('name');
                $table->string('url');
                $table->string('type');
                $table->unsignedInteger('size');
                $table->timestamps();
                $table->softDeletes();
                $table->foreign('message_id')->references('id')->on('conversation_messages')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('conversation_message_read_status')) {
            Schema::create('conversation_message_read_status', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('user_id');
                $table->timestamps();
                $table->unique(['message_id', 'user_id']);
                $table->foreign('message_id')->references('id')->on('conversation_messages')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('conversation_message_read_status');
        Schema::dropIfExists('conversation_message_attachments');
        Schema::dropIfExists('conversation_messages');
        Schema::dropIfExists('conversation_participants');
        Schema::dropIfExists('conversations');
        Schema::dropIfExists('message_read_status');
        Schema::dropIfExists('message_attachments');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('thread_participants');
        Schema::dropIfExists('message_threads');
    }
} 