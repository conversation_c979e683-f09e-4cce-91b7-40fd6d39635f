<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_feedback', function (Blueprint $table) {
            // Add columns if they don't exist
            if (!Schema::hasColumn('user_feedback', 'path')) {
                $table->string('path')->nullable()->after('rate');
            }
            
            if (!Schema::hasColumn('user_feedback', 'feedback_type')) {
                $table->string('feedback_type')->nullable()->after('rate');
            }
            
            if (!Schema::hasColumn('user_feedback', 'metadata')) {
                $table->json('metadata')->nullable()->after('rate');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_feedback', function (Blueprint $table) {
            $table->dropColumn(['path', 'feedback_type', 'metadata']);
        });
    }
}; 