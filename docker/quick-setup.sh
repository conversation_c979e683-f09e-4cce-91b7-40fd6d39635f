#!/bin/bash

# Quick setup script using pre-built images to avoid build issues

set -e

echo "🚀 Quick Setup - Buzfi Laravel Project with Pre-built Images"
echo "============================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! docker compose version > /dev/null 2>&1; then
    echo "❌ Docker Compose is not available. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker is running"

# Navigate to docker directory
cd "$(dirname "$0")"

echo "🔄 Starting services with pre-built images..."
docker compose -f docker-compose.prebuilt.yml up -d

echo "⏳ Waiting for services to be ready..."
sleep 20

echo "📋 Installing Laravel dependencies..."
docker compose -f docker-compose.prebuilt.yml exec -T php composer install --no-interaction --prefer-dist --optimize-autoloader

echo "🔧 Setting up Laravel application..."
# Copy environment file if it doesn't exist
if [ ! -f "../.env" ]; then
    echo "📄 Creating .env file..."
    docker compose -f docker-compose.prebuilt.yml exec -T php cp .env.example .env
fi

# Generate application key
echo "🔑 Generating application key..."
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan key:generate --no-interaction

# Create storage link
echo "🔗 Creating storage link..."
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan storage:link --no-interaction

# Clear caches
echo "🧹 Clearing caches..."
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan config:clear --no-interaction
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan cache:clear --no-interaction
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan view:clear --no-interaction
docker compose -f docker-compose.prebuilt.yml exec -T php php artisan route:clear --no-interaction

# Import database if buzfi.sql exists
echo "🗄️ Checking for database import..."
sleep 5
if [ -f "../import_database/buzfi.sql" ]; then
    echo "📥 Found buzfi.sql file. Importing database..."
    docker compose -f docker-compose.prebuilt.yml exec -T mysql mysql -u root -proot_password buzfi < ../import_database/buzfi.sql
    echo "✅ Database imported successfully!"
else
    echo "⚠️ buzfi.sql file not found in import_database/ folder"
    echo "💡 You can run migrations manually with: docker compose -f docker-compose.prebuilt.yml exec php php artisan migrate"
fi

echo ""
echo "🎉 Quick setup completed successfully!"
echo ""
echo "📍 Access URLs:"
echo "   Application:     http://localhost/buzfi-new-backend"
echo "   phpMyAdmin:      http://localhost:8080"
echo "   Redis Commander: http://localhost:8081"
echo ""
echo "🔐 Database Credentials:"
echo "   Host: localhost:3306"
echo "   Database: buzfi"
echo "   Username: buzfi_user"
echo "   Password: buzfi_password"
echo "   Root Password: root_password"
echo ""
echo "📊 Container Status:"
docker compose -f docker-compose.prebuilt.yml ps

echo ""
echo "💡 Useful commands:"
echo "   Stop services:    docker compose -f docker-compose.prebuilt.yml down"
echo "   View logs:        docker compose -f docker-compose.prebuilt.yml logs -f"
echo "   Access PHP shell: docker compose -f docker-compose.prebuilt.yml exec php bash"
echo "   Access MySQL:     docker compose -f docker-compose.prebuilt.yml exec mysql mysql -u root -proot_password buzfi"
echo ""
echo "📖 For more commands, check the Makefile or run 'make help'"
